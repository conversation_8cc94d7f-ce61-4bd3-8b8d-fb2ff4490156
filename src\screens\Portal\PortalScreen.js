import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  PanResponder,
  Dimensions,
  StatusBar,
  Platform,
  Alert
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import NotificationsCenter from '../../components/NotificationsCenter';
import Sidebar from '../../components/Sidebar';
import HamburgerIcon from '../../components/HamburgerIcon';
import RefreshIcon from '../../components/RefreshIcon';
import { clearWebViewSession, createFreshAuthWebView, disposeWebView } from '../../utils/WebViewUtils';
import { useTheme } from '../../contexts/ThemeContext';
import { useOffline } from '../../contexts/OfflineContext';
import { errorLog, infoLog, warnLog } from '../../utils/DebugUtils';
import { handleDemoPortalInit, handleDemoPortalRefresh } from '../../utils/DemoModeHelper';

const HomeScreen = ({ navigation, route }) => {
  // Use global theme context with fallbacks
  const { theme, currentThemeName, transitionAnim, fadeAnim } = useTheme();

  // Offline context
  const { isOfflineMode, checkOfflineMode } = useOffline();

  // Add fallback for currentThemeName
  const safeCurrentThemeName = currentThemeName || 'dark';

  // Fix opacity issue after theme changes
  useEffect(() => {
    // Ensure fadeAnim is at full opacity after theme changes
    const timer = setTimeout(() => {
      fadeAnim.setValue(1);
      transitionAnim.setValue(1);
    }, 500); // Increased delay to avoid interfering with theme transition animation

    return () => clearTimeout(timer);
  }, [currentThemeName, fadeAnim, transitionAnim]);

  const [studentInfo, setStudentInfo] = useState({
    fullName: '',
    studentId: '',
    faculty: ''
  });
  const [studentNameLoaded, setStudentNameLoaded] = useState(false);
  const [notificationsWebView, setNotificationsWebView] = useState(null);
  const [notificationsData, setNotificationsData] = useState(null); // Start with null to indicate no data loaded yet
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isBackgroundRefreshing, setIsBackgroundRefreshing] = useState(false);
  const [sessionKey, setSessionKey] = useState(0); // Force new WebView instances
  const [selectedNotification, setSelectedNotification] = useState(null);
  const [notificationModalVisible, setNotificationModalVisible] = useState(false);
  const [slideAnim] = useState(new Animated.Value(Dimensions.get('window').height));
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const [sidebarAnim] = useState(new Animated.Value(-300)); // Start off-screen
  const [isLoadingFromCache, setIsLoadingFromCache] = useState(false);

  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false);
  const [refreshRotation] = useState(new Animated.Value(0)); // For rotating refresh arrow
  const [refreshFloat] = useState(new Animated.Value(0)); // For floating animation
  const notificationsWebViewRef = useRef(null);
  const backgroundWebViewRef = useRef(null);
  const tempWebViewRef = useRef(null);
  const retryWebViewRef = useRef(null);
  const isMountedRef = useRef(true); // Track if component is still mounted

  // Debug: Log current theme
  console.log(`🎨 Current theme: ${currentThemeName}`, theme.colors);
  console.log(`🔔 Bell colors: background=${theme.colors.bellBackground}, text=${theme.colors.bellText}`);

  // Generate styles based on current theme
  const styles = createStyles(theme, safeCurrentThemeName);

  // Cleanup function to kill all background operations
  const killAllBackgroundOperations = async () => {
    console.log('🛑 Killing all background operations...');

    // Mark component as unmounted
    isMountedRef.current = false;

    // Stop all animations
    refreshRotation.stopAnimation();
    refreshFloat.stopAnimation();
    sidebarAnim.stopAnimation();
    slideAnim.stopAnimation();
    transitionAnim.stopAnimation();
    fadeAnim.stopAnimation();

    // Reset all loading states to prevent cache clearing
    setIsRefreshing(false);
    setIsBackgroundRefreshing(false);
    setIsLoadingFromCache(false);

    // Dispose of WebView references
    await disposeWebView(tempWebViewRef, 'temp-login');
    await disposeWebView(notificationsWebViewRef, 'notifications');
    await disposeWebView(backgroundWebViewRef, 'background-notifications');
    await disposeWebView(retryWebViewRef, 'retry-student');

    // Clear WebView components from state
    setNotificationsWebView(null);

    console.log('✅ All background operations killed');
  };

  // Safe state setter that checks if component is still mounted
  const safeSetState = (setter, value, stateName) => {
    if (isMountedRef.current) {
      setter(value);
    } else {
      console.log(`⚠️ Prevented ${stateName} state update after unmount`);
    }
  };

  // Helper function to get first name from full name
  const getFirstName = (fullName) => {
    if (!fullName) return '';
    return fullName.split(' ')[0];
  };

  // Retry fetching student name when "Hello Student" is displayed
  const retryStudentNameFetch = async () => {
    try {
      console.log('🔄 Retrying student name fetch...');

      // First, try to reload from localStorage in case it was updated
      await loadStudentInfoFromStorage();

      // Check if we now have a name
      const storedFullName = await AsyncStorage.getItem('student_fullName');
      if (storedFullName && storedFullName.trim() !== '') {
        console.log('✅ Student name found in localStorage after retry:', storedFullName);
        return;
      }

      // If still no name, try to fetch fresh data from WebView
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('❌ No stored credentials for retry');
        return;
      }

      console.log('🔄 Creating fresh WebView to fetch student name...');

      // Clear WebView session before creating fresh login WebView
      await clearWebViewSession();

      // Create a temporary WebView for fresh login to get student info
      const encodedUsername = encodeURIComponent(storedUsername);
      const encodedPassword = encodeURIComponent(storedPassword);
      const loginUrl = `https://${encodedUsername}:${encodedPassword}@apps.guc.edu.eg/student_ext/`;

      const tempRetryWebView = (
        <WebView
          key={`retry-student-${Date.now()}`}
          ref={retryWebViewRef}
          source={{ uri: loginUrl }}
          style={styles.hiddenWebView}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          cacheEnabled={false}
          incognito={true}
          onNavigationStateChange={(navState) => {
            if (navState.url.includes('index.aspx') && !navState.loading) {
              // Login successful, extract student data
              setTimeout(() => {
                if (retryWebViewRef.current) {
                  retryWebViewRef.current.injectJavaScript(`
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                      type: 'retry_student_data',
                      html: document.documentElement.outerHTML,
                      url: window.location.href
                    }));
                  `);
                }
              }, 1000);
            }
          }}
          onMessage={async (event) => {
            try {
              const data = JSON.parse(event.nativeEvent.data);
              if (data.type === 'retry_student_data' && data.url.includes('index.aspx')) {
                console.log('✅ Retry: Fresh login successful, extracting student data...');

                // Extract student info from fresh data
                const extracted = await extractStudentInfo(data.html);
                if (extracted && extracted.fullName) {
                  console.log('✅ Retry: Student name successfully fetched:', extracted.fullName);
                  setStudentInfo(prev => ({ ...prev, ...extracted }));
                } else {
                  console.log('⚠️ Retry: Still no student name found');
                }
              }
            } catch (error) {
              console.log('❌ Error processing retry student data:', error);
            }
          }}
          onError={() => {
            console.log('❌ Retry student name fetch failed');
          }}
        />
      );

      // Set the temporary WebView (it will be hidden)
      setNotificationsWebView(tempRetryWebView);

    } catch (error) {
      console.log('❌ Error in retry student name fetch:', error);
    }
  };

  // Extract enrollment year from student ID
  const getEnrollmentYear = (studentId) => {
    if (!studentId) return null;

    // Extract digits before the dash
    const match = studentId.match(/^(\d+)-/);
    if (!match) return null;

    const digits = match[1];
    const firstTwoDigits = parseInt(digits.substring(0, 2));

    // Map the first two digits to academic years
    if (firstTwoDigits >= 64 && firstTwoDigits <= 66) {
      return '2024-2025';
    } else if (firstTwoDigits >= 61 && firstTwoDigits <= 63) {
      return '2023-2024';
    } else if (firstTwoDigits >= 58 && firstTwoDigits <= 60) {
      return '2022-2023';
    } else {
      return `Unknown year for digits: ${firstTwoDigits}`;
    }
  };

  // Load student info directly from localStorage (cache-first strategy)
  const loadStudentInfoFromStorage = async () => {
    try {
      const storedFullName = await AsyncStorage.getItem('student_fullName');
      const storedStudentId = await AsyncStorage.getItem('student_id');
      const storedFaculty = await AsyncStorage.getItem('student_faculty');
      const timestamp = await AsyncStorage.getItem('student_data_timestamp');

      console.log('🔍 Loading student info from storage:', {
        fullName: storedFullName ? `"${storedFullName}"` : 'null',
        studentId: storedStudentId ? `"${storedStudentId}"` : 'null',
        faculty: storedFaculty ? `"${storedFaculty}"` : 'null'
      });

      // Always set student info, even if some fields are empty
      setStudentInfo({
        fullName: storedFullName || '',
        studentId: storedStudentId || '',
        faculty: storedFaculty || ''
      });
      
      // Mark student name as loaded to prevent flickering
      setStudentNameLoaded(true);

      if (storedFullName || storedStudentId || storedFaculty) {
        const cacheAge = timestamp ? Date.now() - parseInt(timestamp) : 0;
        const cacheAgeMinutes = Math.floor(cacheAge / (1000 * 60));

        console.log('✅ Student info loaded from cache:', {
          fullName: storedFullName ? '✅' : '❌',
          studentId: storedStudentId ? '✅' : '❌',
          faculty: storedFaculty ? '✅' : '❌',
          cacheAge: `${cacheAgeMinutes} minutes`
        });

        // Extract and log enrollment year
        if (storedStudentId) {
          const enrollmentYear = getEnrollmentYear(storedStudentId);
          console.log('📅 Student ID:', storedStudentId);
          console.log('📅 Enrollment Year:', enrollmentYear);
        }
      } else {
        console.log('⚠️ No student info found in cache - this should not happen in Portal screen');
        // If no student info is found, try to retry fetch
        retryStudentNameFetch();
      }
    } catch (error) {
      console.log('❌ Error loading student info from cache:', error);
    }
  };

  // Cache management functions
  const saveToCache = async (studentData, notificationsData) => {
    try {
      const cacheData = {
        studentInfo: studentData,
        notifications: notificationsData,
        timestamp: Date.now()
      };
      await AsyncStorage.setItem('portal_cache', JSON.stringify(cacheData));
      console.log('✅ Data saved to cache');
    } catch (error) {
      console.log('❌ Error saving to cache:', error);
    }
  };

  const loadFromCache = async () => {
    try {
      setIsLoadingFromCache(true);
      const cachedData = await AsyncStorage.getItem('portal_cache');
      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        const cacheAge = Date.now() - parsed.timestamp;
        const maxCacheAge = 24 * 60 * 60 * 1000; // 24 hours

        if (cacheAge < maxCacheAge) {
          console.log('✅ Loading from cache (age:', Math.round(cacheAge / 1000 / 60), 'minutes)');

          // Load student info from cache
          if (parsed.studentInfo) {
            setStudentInfo(parsed.studentInfo);
          }

          // Load notifications from cache immediately
          if (parsed.notifications && Array.isArray(parsed.notifications)) {
            console.log('📋 Setting cached notifications:', parsed.notifications.length, 'items');
            setNotificationsData(parsed.notifications);
            setHasInitiallyLoaded(true); // Mark as loaded when we have cached data
          } else {
            console.log('📭 No cached notifications found - keeping null for ghost loading');
            // Keep notificationsData as null to show ghost loading
          }

          setIsLoadingFromCache(false);
          return true;
        } else {
          console.log('⏰ Cache expired, will load fresh data');
          await AsyncStorage.removeItem('portal_cache');
        }
      } else {
        console.log('📭 No cache found');
      }
    } catch (error) {
      console.log('❌ Error loading from cache:', error);
    }

    // If no cache or error, keep notificationsData as null for ghost loading
    console.log('📭 No cache available - keeping notificationsData as null for ghost loading');
    setIsLoadingFromCache(false);
    return false;
  };

  const compareAndUpdateData = async (newStudentInfo, newNotifications) => {
    let hasUpdates = false;

    // Compare student info (only if newStudentInfo is provided and not null)
    if (newStudentInfo) {
      const isStudentInfoDifferent = (
        newStudentInfo.fullName !== studentInfo.fullName ||
        newStudentInfo.studentId !== studentInfo.studentId ||
        newStudentInfo.faculty !== studentInfo.faculty
      );

      if (isStudentInfoDifferent) {
        console.log('🔄 Student info updated from background fetch');
        setStudentInfo(newStudentInfo);
        hasUpdates = true;
      }
    }

    // Compare notifications
    if (newNotifications && JSON.stringify(newNotifications) !== JSON.stringify(notificationsData)) {
      console.log('🔄 Notifications updated from background fetch');
      setNotificationsData(newNotifications);
      hasUpdates = true;
    }

    // Update cache if there are changes
    if (hasUpdates) {
      await saveToCache(newStudentInfo || studentInfo, newNotifications || notificationsData);
      console.log('✅ Data saved to cache');
    }

    return hasUpdates;
  };

  // Function to decode HTML entities
  const decodeHtmlEntities = (text) => {
    if (!text) return text;

    return text
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ')
      .trim();
  };

  // Pan responder for swipe down to close
  // Create a separate pan responder that only works on the handle
  const handlePanResponder = PanResponder.create({
    onStartShouldSetPanResponder: () => true,
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to downward swipes
      return gestureState.dy > 10 && Math.abs(gestureState.dy) > Math.abs(gestureState.dx);
    },
    onPanResponderMove: (evt, gestureState) => {
      if (gestureState.dy > 0) {
        slideAnim.setValue(gestureState.dy);
      }
    },
    onPanResponderRelease: (evt, gestureState) => {
      if (gestureState.dy > 100) {
        closeNotificationModal();
      } else {
        Animated.spring(slideAnim, {
          toValue: 0,
          useNativeDriver: true,
        }).start();
      }
    },
  });

  const openNotificationModal = (notification) => {
    setSelectedNotification(notification);
    setNotificationModalVisible(true);
    slideAnim.setValue(Dimensions.get('window').height);
    Animated.spring(slideAnim, {
      toValue: 0,
      useNativeDriver: true,
    }).start();
  };

  const closeNotificationModal = () => {
    Animated.timing(slideAnim, {
      toValue: Dimensions.get('window').height,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setNotificationModalVisible(false);
      setSelectedNotification(null);
    });
  };

  const openSidebar = () => {
    setIsSidebarVisible(true);
    Animated.timing(sidebarAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeSidebar = () => {
    Animated.timing(sidebarAnim, {
      toValue: -300,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsSidebarVisible(false);
    });
  };

  // Swipe gesture handler for opening/closing sidebar - Enhanced for Android
  const swipeGestureHandler = PanResponder.create({
    onStartShouldSetPanResponder: () => false, // Don't capture immediately
    onStartShouldSetPanResponderCapture: () => false, // Don't capture on start
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to significant horizontal swipes
      const { dx, dy } = gestureState;

      // Only capture if it's a clear horizontal swipe with significant movement
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
      // Only capture significant horizontal swipes to prevent Android system gestures
      const { dx, dy } = gestureState;
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onPanResponderGrant: () => {
      // Gesture has been granted - prevent other handlers
      return true;
    },
    onPanResponderMove: () => {
      // Optional: Add visual feedback during swipe
    },
    onPanResponderRelease: (evt, gestureState) => {
      const { dx, dy } = gestureState;
      const isHorizontalSwipe = Math.abs(dx) > Math.abs(dy);
      const swipeDistance = Math.abs(dx);

      if (isHorizontalSwipe && swipeDistance > 100) {
        if (dx > 0) {
          // Swipe right - open sidebar
          openSidebar();
        } else {
          // Swipe left - close sidebar if it's open
          if (isSidebarVisible) {
            closeSidebar();
          }
        }
      }
    },
    onPanResponderTerminationRequest: () => false, // Don't allow termination
    onShouldBlockNativeResponder: () => true, // Block native responders
  });

  // Rotation animation functions
  const startRotationAnimation = () => {
    refreshRotation.setValue(0);
    Animated.loop(
      Animated.timing(refreshRotation, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    ).start();
  };

  // Floating animation for refresh button
  const startFloatingAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(refreshFloat, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(refreshFloat, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const stopRotationAnimation = () => {
    console.log('🛑 Stopping rotation animation. Current states:', {
      isRefreshing,
      isBackgroundRefreshing,
      hasInitiallyLoaded
    });

    // Stop any running animation first
    refreshRotation.stopAnimation();

    // Reset to 0 with a smooth transition
    Animated.timing(refreshRotation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      // Ensure the value is exactly 0 after animation completes
      refreshRotation.setValue(0);
      console.log('✅ Rotation animation stopped and reset to 0');
    });
  };



  // Handle emoji tap gestures
  const handleEmojiTap = () => {
    console.log('👆 EMOJI TAP DETECTED!');
    const now = Date.now();
    const TAP_DELAY = 500; // Reduced delay for better responsiveness

    console.log(`🕐 Current time: ${now}, Last tap: ${lastTap}, Tap count: ${tapCount}`);

    if (lastTap && (now - lastTap) < TAP_DELAY) {
      // This is a potential double/triple tap
      const newTapCount = tapCount + 1;
      setTapCount(newTapCount);
      tapCountRef.current = newTapCount; // Update ref for setTimeout
      console.log(`🔢 Multi-tap detected! New tap count: ${newTapCount}`);

      if (newTapCount === 2) {
        console.log('🎯 DOUBLE TAP DETECTED! Waiting for potential triple tap...');
        // Update last tap time for potential triple tap
        setLastTap(now);
        // Wait for potential triple tap before executing double tap action
        setTimeout(() => {
          // Check if tap count is still 2 (no triple tap occurred)
          if (tapCountRef.current === 2) {
            console.log('✅ Executing double tap action');
            if (currentThemeName === 'dark') {
              console.log('📱 From dark → theme switching disabled');
              // Theme switching moved to sidebar
            } else {
              console.log('📱 Theme switching moved to sidebar');
              // Theme switching moved to sidebar
            }
            setTapCount(0);
            tapCountRef.current = 0;
            setLastTap(null);
          }
        }, 300); // Wait 300ms for potential triple tap
      } else if (newTapCount === 3) {
        console.log('🎯 TRIPLE TAP DETECTED! Waiting for potential quadruple tap...');
        // Update last tap time for potential quadruple tap
        setLastTap(now);
        // Wait for potential quadruple tap before executing triple tap action
        setTimeout(() => {
          // Check if tap count is still 3 (no quadruple tap occurred)
          if (tapCountRef.current === 3) {
            console.log('✅ Executing triple tap - colorful theme');
            if (currentThemeName === 'dark') {
              console.log('📱 From dark → theme switching disabled');
              // Theme switching moved to sidebar
            } else {
              console.log('📱 Theme switching moved to sidebar');
              // Theme switching moved to sidebar
            }
            setTapCount(0);
            tapCountRef.current = 0;
            setLastTap(null);
          }
        }, 300); // Wait 300ms for potential quadruple tap
      } else if (newTapCount === 4) {
        console.log('🎯 QUADRUPLE TAP DETECTED!');
        // Execute quadruple tap immediately (this cancels the triple tap timeout)
        console.log('✅ Executing quadruple tap - navy theme');
        if (currentThemeName === 'dark') {
          console.log('📱 From dark → theme switching disabled');
          // Theme switching moved to sidebar
        } else {
          console.log('📱 Theme switching moved to sidebar');
          // Theme switching moved to sidebar
        }
        setTapCount(0);
        tapCountRef.current = 0;
        setLastTap(null);
      } else {
        // More than 4 taps - reset
        console.log('🔄 Too many taps, resetting...');
        setTapCount(0);
        tapCountRef.current = 0;
        setLastTap(null);
      }
    } else {
      // First tap or tap after delay - just register the tap
      console.log('� SINGLE TAP DETECTED! Switching to dark theme...');
      // No action on single tap - just register it
      setTapCount(1);
      tapCountRef.current = 1;
      setLastTap(now);

      // Reset after delay if no more taps
      setTimeout(() => {
        setTapCount(0);
        tapCountRef.current = 0;
        setLastTap(null);
        console.log('🔄 Tap sequence reset after timeout');
      }, TAP_DELAY + 100); // Small buffer
    }
  };



  // Refresh function to reload ONLY notifications (student data stays cached)
  const handleRefresh = async () => {
    try {
      console.log('🔄 Starting notifications refresh (student data stays cached)...');
      setIsRefreshing(true);
      // Don't reset hasInitiallyLoaded - keep showing cached data during refresh
      startRotationAnimation();

      // Check if we're in demo mode first
      const isDemoMode = await AsyncStorage.getItem('is_demo_user');
      if (isDemoMode === 'true') {
        console.log('🎭 Demo mode detected - refreshing demo notifications');
        
        // Only reset notifications data, keep student info visible from cache
        setNotificationsData(null);
        
        // Simulate loading delay
        setTimeout(() => {
          handleDemoPortalRefresh().then(demoNotifications => {
            setNotificationsData(demoNotifications);
            setIsRefreshing(false);
            stopRotationAnimation();
            console.log('🎭 Demo notifications refreshed:', demoNotifications.length, 'items');
          });
        }, 1000);
        return;
      }

      // Only reset notifications data, keep student info visible from cache
      console.log('🔄 PortalScreen: Refreshing notifications only, keeping student data cached');
      setNotificationsData(null);
      setNotificationsWebView(null);

      // Get stored credentials (skip for demo mode as it's handled above)
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('❌ No stored credentials found');
        setIsRefreshing(false);
        stopRotationAnimation();
        return;
      }

      // Create new session with fresh WebView
      const newSessionKey = sessionKey + 1;
      setSessionKey(newSessionKey);

      // Clear WebView session before creating fresh login WebView
      await clearWebViewSession();

      // Simulate fresh login by creating new WebView with credentials
      const encodedUsername = encodeURIComponent(storedUsername);
      const encodedPassword = encodeURIComponent(storedPassword);
      const loginUrl = `https://${encodedUsername}:${encodedPassword}@apps.guc.edu.eg/student_ext/`;

      // Create a temporary WebView for fresh login with unique key
      const tempWebView = (
        <WebView
          key={`temp-login-${newSessionKey}-${Date.now()}`}
          ref={tempWebViewRef}
          source={{ uri: loginUrl }}
          style={styles.hiddenWebView}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          cacheEnabled={false}
          incognito={true}
          onNavigationStateChange={(navState) => {
            if (navState.url.includes('index.aspx') && !navState.loading) {
              // Login successful, extract data
              setTimeout(() => {
                if (tempWebViewRef.current) {
                  tempWebViewRef.current.injectJavaScript(`
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                      type: 'page_html',
                      html: document.documentElement.outerHTML,
                      url: window.location.href,
                      title: document.title
                    }));
                  `);
                }
              }, 1000);
            }
          }}
          onMessage={async (event) => {
            try {
              const data = JSON.parse(event.nativeEvent.data);
              if (data.type === 'page_html' && data.url.includes('index.aspx')) {
                console.log('✅ Fresh login successful, extracting data...');

                // Extract student info from fresh data and store in localStorage
                const extracted = await extractStudentInfo(data.html);
                // Merge with existing student info, don't replace completely
                setStudentInfo(prev => ({
                  ...prev,
                  ...extracted,
                  // Keep existing values if new ones are empty/undefined
                  fullName: extracted.fullName || prev.fullName,
                  studentId: extracted.studentId || prev.studentId,
                  faculty: extracted.faculty || prev.faculty
                }));

                // Create new notifications WebView with fresh session
                createNotificationsWebView(storedUsername, storedPassword, newSessionKey);

                // Don't stop refresh states here - let notifications WebView complete first
                // The refresh will be completed when notifications are loaded
                console.log('✅ Student info updated, waiting for notifications...');
              }
            } catch (error) {
              console.log('❌ Error processing refresh data:', error);
              setIsRefreshing(false);
              stopRotationAnimation();
            }
          }}
          onError={() => {
            console.log('❌ Refresh login failed');
            setIsRefreshing(false);
            stopRotationAnimation();
          }}
        />
      );

      // Set the temporary WebView (it will be hidden)
      setNotificationsWebView(tempWebView);

    } catch (error) {
      console.log('❌ Refresh error:', error);
      setIsRefreshing(false);
      stopRotationAnimation();
    }
  };

  // Extract student information from HTML using specific class names and store in localStorage
  const extractStudentInfo = async (html, retryCount = 0) => {
    const maxRetries = 3;

    try {
      // Validate input
      if (!html || typeof html !== 'string') {
        console.log('⚠️ Invalid HTML provided to extractStudentInfo');
        if (retryCount < maxRetries - 1) {
          console.log(`🔄 Retrying student info extraction (${retryCount + 1}/${maxRetries})...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
          return await extractStudentInfo(html, retryCount + 1);
        }
        return {
          fullName: '',
          studentId: '',
          faculty: ''
        };
      }

      console.log(`🔍 Extracting student info from HTML (attempt ${retryCount + 1}/${maxRetries})...`);
      const extracted = {};

      // Extract Full Name from ContentPlaceHolderright_ContentPlaceHoldercontent_LabelFullName
      const fullNameMatch = html.match(/<[^>]*id="ContentPlaceHolderright_ContentPlaceHoldercontent_LabelFullName"[^>]*>([^<]+)<\/[^>]*>/i);
      if (fullNameMatch && fullNameMatch[1]) {
        extracted.fullName = decodeHtmlEntities(fullNameMatch[1].trim());
      }

      // Extract Student ID from ContentPlaceHolderright_ContentPlaceHoldercontent_LabelUniqAppNo
      const studentIdMatch = html.match(/<[^>]*id="ContentPlaceHolderright_ContentPlaceHoldercontent_LabelUniqAppNo"[^>]*>([^<]+)<\/[^>]*>/i);
      if (studentIdMatch && studentIdMatch[1]) {
        extracted.studentId = decodeHtmlEntities(studentIdMatch[1].trim());
      }

      // Extract Faculty from ContentPlaceHolderright_ContentPlaceHoldercontent_Labelfaculty
      const facultyMatch = html.match(/<[^>]*id="ContentPlaceHolderright_ContentPlaceHoldercontent_Labelfaculty"[^>]*>([^<]+)<\/[^>]*>/i);
      if (facultyMatch && facultyMatch[1]) {
        extracted.faculty = decodeHtmlEntities(facultyMatch[1].trim());
      }

      // Check if extraction was successful
      const hasValidData = extracted.fullName || extracted.studentId || extracted.faculty;

      if (!hasValidData && retryCount < maxRetries - 1) {
        console.log(`⚠️ No student data extracted, retrying (${retryCount + 1}/${maxRetries})...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return await extractStudentInfo(html, retryCount + 1);
      }

      console.log('📋 Extracted student info:', {
        fullName: extracted.fullName ? '✅' : '❌',
        studentId: extracted.studentId ? '✅' : '❌',
        faculty: extracted.faculty ? '✅' : '❌'
      });

      // Store in localStorage
      try {
        if (extracted.fullName) {
          await AsyncStorage.setItem('student_fullName', extracted.fullName);
        }
        if (extracted.studentId) {
          await AsyncStorage.setItem('student_id', extracted.studentId);
        }
        if (extracted.faculty) {
          await AsyncStorage.setItem('student_faculty', extracted.faculty);
        }
        console.log('✅ Student info stored in localStorage');

        // Extract and log enrollment year
        if (extracted.studentId) {
          const enrollmentYear = getEnrollmentYear(extracted.studentId);
          console.log('📅 Student ID:', extracted.studentId);
          console.log('📅 Enrollment Year:', enrollmentYear);
        }
      } catch (storageError) {
        console.log('❌ Error storing student info:', storageError);
      }

      // Update state with extracted info
      setStudentInfo({
        fullName: extracted.fullName || '',
        studentId: extracted.studentId || '',
        faculty: extracted.faculty || ''
      });

      return extracted;
    } catch (error) {
      console.log(`❌ Error extracting student info (attempt ${retryCount + 1}):`, error);

      if (retryCount < maxRetries - 1) {
        console.log(`🔄 Retrying due to error (${retryCount + 1}/${maxRetries})...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return await extractStudentInfo(html, retryCount + 1);
      }

      console.log('❌ All retry attempts failed for student info extraction');
      return {
        fullName: '',
        studentId: '',
        faculty: ''
      };
    }
  };

  // Create notifications WebView using credentials (same approach as login)
  const createNotificationsWebView = async (username = null, password = null, keyOverride = null) => {
    try {
      console.log('🔄 createNotificationsWebView called with:', { username: !!username, password: !!password, keyOverride });

      // Get credentials from parameters or original webview data
      let credentials;
      if (username && password) {
        credentials = { username, password };
        console.log('✅ Using provided credentials');
      } else {
        credentials = route?.params?.webViewData?.credentials;
        if (!credentials) {
          console.log('❌ No credentials available');
          return;
        }
        console.log('✅ Using route credentials');
      }

      // Clear WebView session before creating notifications WebView
      await clearWebViewSession();

      // Create the WebView component with embedded credentials and fresh session
      const encodedUsername = encodeURIComponent(credentials.username);
      const encodedPassword = encodeURIComponent(credentials.password);
      const notificationsUrl = `https://${encodedUsername}:${encodedPassword}@apps.guc.edu.eg/student_ext/Main/Notifications.aspx`;

      const webViewComponent = (
        <WebView
          key={`notifications-${keyOverride || sessionKey}-${Date.now()}`}
          ref={notificationsWebViewRef}
          source={{
            uri: notificationsUrl
          }}
          style={styles.hiddenWebView}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={false}
          cacheEnabled={false}
          incognito={true}
          onLoadEnd={() => {
            console.log('📱 Notifications WebView loaded, injecting JavaScript...');
            setTimeout(() => {
              if (notificationsWebViewRef.current) {
                console.log('✅ WebView ref available, injecting script...');
                notificationsWebViewRef.current.injectJavaScript(`
                  try {
                    console.log('🔍 Looking for notifications table...');

                    // First, log the full HTML to see what we're working with
                    console.log('🔍 Full HTML Content:', document.documentElement.outerHTML);
                    console.log('🔍 Body Content:', document.body.innerHTML);
                    console.log('🔍 Page Title:', document.title);
                    console.log('🔍 Current URL:', window.location.href);

                    // Log all tables on the page
                    const tables = document.querySelectorAll('table');
                    console.log('🔍 Found', tables.length, 'tables on the page');
                    tables.forEach((table, index) => {
                      console.log('🔍 Table', index, 'classes:', table.className);
                    });

                    const table = document.querySelector('table.table.table-bordered.grid.dataTable.no-footer');
                    if (table) {
                      console.log('✅ Found notifications table');
                      const rows = table.querySelectorAll('tr');
                      const notifications = [];

                      for (let i = 1; i < rows.length; i++) { // Skip header row
                        const cells = rows[i].querySelectorAll('td');

                        console.log('🔍 ===== ROW', i, 'DETAILED ANALYSIS =====');
                        console.log('🔍 Row', i, 'total cells:', cells.length);
                        console.log('🔍 Row', i, 'full HTML:', rows[i].outerHTML);

                        // Log each cell individually
                        cells.forEach((cell, cellIndex) => {
                          console.log('🔍 Row', i, 'Cell', cellIndex, ':');
                          console.log('  - innerHTML:', cell.innerHTML);
                          console.log('  - textContent:', cell.textContent.trim());
                          console.log('  - className:', cell.className);
                          console.log('  - attributes:', Array.from(cell.attributes).map(attr => attr.name + '=' + attr.value));

                          // Look for divs within each cell
                          const divs = cell.querySelectorAll('div');
                          if (divs.length > 0) {
                            console.log('  - Found', divs.length, 'div(s) in cell', cellIndex);
                            divs.forEach((div, divIndex) => {
                              console.log('    - Div', divIndex, 'content:', div.textContent.trim());
                              console.log('    - Div', divIndex, 'innerHTML:', div.innerHTML);
                            });
                          }

                          // Look for buttons/links within each cell
                          const buttons = cell.querySelectorAll('input, button, a');
                          if (buttons.length > 0) {
                            console.log('  - Found', buttons.length, 'button/link(s) in cell', cellIndex);
                            buttons.forEach((btn, btnIndex) => {
                              console.log('    - Button/Link', btnIndex, 'type:', btn.tagName);
                              console.log('    - Button/Link', btnIndex, 'value/text:', btn.value || btn.textContent.trim());
                            });
                          }
                        });

                        if (cells.length >= 5) {
                          // Extract data from the correct cells based on the structure you provided
                          const buttonCell = cells[1]; // Second cell contains the button with data attributes
                          const titleCell = cells[2] ? cells[2].textContent.trim() : ''; // Third cell has the title
                          const dateCell = cells[3] ? cells[3].textContent.trim() : ''; // Fourth cell has the date
                          const staffCell = cells[4] ? cells[4].textContent.trim() : ''; // Fifth cell has the staff

                          console.log('🔍 Row', i, 'EXTRACTED DATA:');
                          console.log('  - titleCell (cells[2]):', titleCell);
                          console.log('  - dateCell (cells[3]):', dateCell);
                          console.log('  - staffCell (cells[4]):', staffCell);

                          // Extract notification content from button's data-body_text attribute
                          let notificationContent = '';
                          let notificationSubject = '';

                          const button = buttonCell.querySelector('button.openmodeldetials');
                          if (button) {
                            notificationContent = button.getAttribute('data-body_text') || '';
                            notificationSubject = button.getAttribute('data-subject_text') || '';
                            console.log('🔍 Row', i, '- Found button with data attributes');
                            console.log('  - data-subject_text:', notificationSubject);
                            console.log('  - data-body_text length:', notificationContent.length);
                            console.log('  - data-body_text preview:', notificationContent.substring(0, 100) + '...');
                          } else {
                            console.log('🔍 Row', i, '- No button with data attributes found');
                          }

                          // Parse title to extract notification title and course code
                          let notificationTitle = '';
                          let courseCode = '';

                          // Extract text between "Notification System:" and "[Group:"
                          const titleMatch = titleCell.match(/Notification System:\\s*(.*?)\\s*\\[Group:/s);
                          if (titleMatch) {
                            notificationTitle = titleMatch[1].trim();
                            console.log('🔍 Row', i, '- Extracted title:', notificationTitle);
                          }

                          // Extract course code by splitting on '-' character within [Group: ... ]
                          const groupMatch = titleCell.match(/\\[Group:\\s*([^\\]]+)\\]/);
                          if (groupMatch) {
                            const groupContent = groupMatch[1];
                            const parts = groupContent.split('-').map(part => part.trim());
                            if (parts.length >= 2) {
                              courseCode = parts[1]; // Course code is at index 1 after splitting
                              console.log('🔍 Row', i, '- Extracted course:', courseCode);
                            }
                          }

                          if (notificationTitle || courseCode || notificationContent) {
                            console.log('🔍 Row', i, '- Adding notification with content');
                            notifications.push({
                              id: i,
                              title: notificationTitle,
                              courseCode: courseCode,
                              date: dateCell || 'Recent',
                              staff: staffCell,
                              fullTitle: titleCell,
                              content: notificationContent,
                              subject: notificationSubject
                            });
                          } else {
                            console.log('🔍 Row', i, '- No title, course, or content found, skipping');
                          }
                        } else {
                          console.log('🔍 Row', i, '- Not enough cells (', cells.length, '), skipping');
                        }
                      }

                      console.log('📋 Found', notifications.length, 'notifications');
                      window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'notifications_data',
                        notifications: notifications
                      }));
                    } else {
                      console.log('❌ Notifications table not found');
                      window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'notifications_error',
                        error: 'Table not found'
                      }));
                    }
                  } catch (error) {
                    console.log('❌ Error parsing notifications:', error.message);
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                      type: 'notifications_error',
                      error: error.message
                    }));
                  }
                  true;
                `);
              } else {
                console.log('❌ WebView ref not available for script injection');
              }
            }, 1000); // Reduced from 3000ms to 1000ms
          }}
          onMessage={(event) => {
            try {
              const data = JSON.parse(event.nativeEvent.data);
              console.log('📨 Received message from notifications WebView:', data.type);
              if (data.type === 'notifications_data') {
                console.log('✅ Setting notifications data:', data.notifications.length, 'items');
                // Decode HTML entities in notification data
                const decodeEntities = (text) => {
                  if (!text) return text;
                  return text
                    .replace(/&amp;/g, '&')
                    .replace(/&lt;/g, '<')
                    .replace(/&gt;/g, '>')
                    .replace(/&quot;/g, '"')
                    .replace(/&#39;/g, "'")
                    .replace(/&nbsp;/g, ' ')
                    .replace(/&apos;/g, "'")
                    .trim();
                };

                const decodedNotifications = data.notifications.map(notification => ({
                  ...notification,
                  title: decodeEntities(notification.title),
                  content: decodeEntities(notification.content),
                  subject: decodeEntities(notification.subject)
                }));
                console.log('✅ Setting notifications data:', decodedNotifications.length, 'items');
                safeSetState(setNotificationsData, decodedNotifications, 'notificationsData');

                // Mark as initially loaded now that we have fresh data
                safeSetState(setHasInitiallyLoaded, true, 'hasInitiallyLoaded');
                console.log('✅ Fresh notifications loaded, marking as initially loaded');

                // Save to cache now that we have fresh notifications data
                setTimeout(async () => {
                  await saveToCache(studentInfo, data.notifications);
                  console.log('💾 Cache updated with fresh notifications data');
                  console.log('✅ Data saved to cache');

                  // Complete refresh AFTER cache save is done
                  if (isRefreshing) {
                    safeSetState(setIsRefreshing, false, 'isRefreshing');
                    stopRotationAnimation();
                    console.log('✅ Refresh completed successfully');
                  }
                }, 500);
              } else if (data.type === 'notifications_error') {
                console.log('❌ Notifications error:', data.error);
                // Set empty array only if we have no cached data, otherwise keep cached data
                safeSetState(setNotificationsData, notificationsData || [], 'notificationsData');

                // Mark as initially loaded even on error to prevent infinite loading
                safeSetState(setHasInitiallyLoaded, true, 'hasInitiallyLoaded');

                // Complete refresh if it was in progress (even on error)
                if (isRefreshing) {
                  safeSetState(setIsRefreshing, false, 'isRefreshing');
                  stopRotationAnimation();
                  console.log('⚠️ Refresh completed with notifications error');
                }

                console.log('⚠️ Notifications error, but marking as initially loaded to prevent infinite loading');
              }
            } catch (error) {
              console.log('❌ Error parsing notifications message:', error);
              // Set empty array only if we have no cached data, otherwise keep cached data
              safeSetState(setNotificationsData, notificationsData || [], 'notificationsData');

              // Mark as initially loaded even on parsing error
              safeSetState(setHasInitiallyLoaded, true, 'hasInitiallyLoaded');

              // Complete refresh if it was in progress (even on error)
              if (isRefreshing) {
                safeSetState(setIsRefreshing, false, 'isRefreshing');
                stopRotationAnimation();
                console.log('⚠️ Refresh completed with parsing error');
              }

              console.log('⚠️ Notifications parsing error, but marking as initially loaded');
            }
          }}
          onError={(syntheticEvent) => {
            console.log('❌ Notifications WebView error:', syntheticEvent.nativeEvent);
            // Keep existing data on WebView error, don't clear it
            safeSetState(setHasInitiallyLoaded, true, 'hasInitiallyLoaded');

            // Complete refresh if it was in progress (even on error)
            if (isRefreshing) {
              safeSetState(setIsRefreshing, false, 'isRefreshing');
              stopRotationAnimation();
              console.log('⚠️ Refresh completed with WebView error');
            }
          }}
          onHttpError={(syntheticEvent) => {
            console.log('❌ Notifications WebView HTTP error:', syntheticEvent.nativeEvent);
            // Keep existing data on HTTP error, don't clear it
            safeSetState(setHasInitiallyLoaded, true, 'hasInitiallyLoaded');

            // Complete refresh if it was in progress (even on error)
            if (isRefreshing) {
              safeSetState(setIsRefreshing, false, 'isRefreshing');
              stopRotationAnimation();
              console.log('⚠️ Refresh completed with HTTP error');
            }
          }}
        />
      );

      setNotificationsWebView(webViewComponent);

    } catch (error) {
      console.log('❌ Error creating notifications WebView:', error);
    }
  };

  // Create notifications WebView with callback for cache updates
  const createNotificationsWebViewWithCallback = async (username, password) => {
    try {
      // Clear WebView session before creating notifications WebView
      await clearWebViewSession();

      const credentials = { username, password };
      const encodedUsername = encodeURIComponent(credentials.username);
      const encodedPassword = encodeURIComponent(credentials.password);
      const notificationsUrl = `https://${encodedUsername}:${encodedPassword}@apps.guc.edu.eg/student_ext/Main/Notifications.aspx`;

      const webViewComponent = (
        <WebView
          key={`notifications-cache-${sessionKey}-${Date.now()}`}
          ref={backgroundWebViewRef}
          source={{ uri: notificationsUrl }}
          style={styles.hiddenWebView}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={false}
          cacheEnabled={false}
          incognito={true}
          onLoadEnd={() => {
            // Inject JavaScript to extract notifications
            setTimeout(() => {
              if (backgroundWebViewRef.current) {
                backgroundWebViewRef.current.injectJavaScript(`
                  (function() {
                    try {
                      console.log('🔍 Background: Looking for notifications table...');
                      const table = document.querySelector('table.table.table-bordered.grid.dataTable.no-footer');
                      if (table) {
                        console.log('✅ Background: Found notifications table');
                        const rows = table.querySelectorAll('tr');
                        const notifications = [];

                        for (let i = 1; i < rows.length; i++) {
                          const cells = rows[i].querySelectorAll('td');
                          if (cells.length >= 5) {
                            const buttonCell = cells[1];
                            const titleCell = cells[2] ? cells[2].textContent.trim() : '';
                            const dateCell = cells[3] ? cells[3].textContent.trim() : '';
                            const staffCell = cells[4] ? cells[4].textContent.trim() : '';

                            let notificationContent = '';
                            let notificationSubject = '';
                            let notificationTitle = '';
                            let courseCode = '';

                            // Extract notification content from button's data attributes
                            const button = buttonCell.querySelector('button.openmodeldetials');
                            if (button) {
                              notificationContent = button.getAttribute('data-body_text') || '';
                              notificationSubject = button.getAttribute('data-subject_text') || '';
                            }

                            // Parse title to extract notification title and course code
                            const titleMatch = titleCell.match(/Notification System:\\s*(.*?)\\s*\\[Group:/s);
                            if (titleMatch) {
                              notificationTitle = titleMatch[1].trim();
                            }

                            // Extract course code from [Group: ... ] section
                            const groupMatch = titleCell.match(/\\[Group:\\s*([^\\]]+)\\]/);
                            if (groupMatch) {
                              const groupContent = groupMatch[1];
                              const parts = groupContent.split('-').map(part => part.trim());
                              if (parts.length >= 2) {
                                courseCode = parts[1];
                              }
                            }

                            if (notificationTitle || courseCode || notificationContent) {
                              notifications.push({
                                id: i,
                                title: notificationTitle,
                                courseCode: courseCode,
                                date: dateCell || 'Recent',
                                staff: staffCell,
                                fullTitle: titleCell,
                                content: notificationContent,
                                subject: notificationSubject
                              });
                            }
                          }
                        }

                        console.log('📋 Background: Found', notifications.length, 'notifications');
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                          type: 'background_notifications',
                          notifications: notifications
                        }));
                      } else {
                        console.log('❌ Background: Notifications table not found');
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                          type: 'background_notifications',
                          notifications: []
                        }));
                      }
                    } catch (error) {
                      console.log('❌ Background: Error extracting notifications:', error.message);
                      window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'background_notifications',
                        notifications: []
                      }));
                    }
                  })();
                `);
              }
            }, 2000);
          }}
          onMessage={async (event) => {
            try {
              const data = JSON.parse(event.nativeEvent.data);
              if (data.type === 'background_notifications') {
                console.log('📨 Background notifications received:', data.notifications.length);

                // Compare and update notifications only (don't update student info during background refresh)
                await compareAndUpdateData(null, data.notifications);

                // Stop background refresh loading animation AFTER cache save
                safeSetState(setIsBackgroundRefreshing, false, 'isBackgroundRefreshing');
                stopRotationAnimation();
              }
            } catch (error) {
              console.log('❌ Error processing background notifications:', error);
              // Stop background refresh loading animation on error
              safeSetState(setIsBackgroundRefreshing, false, 'isBackgroundRefreshing');
              stopRotationAnimation();
            }
          }}
          onError={() => {
            console.log('❌ Background notifications loading failed');
            // Stop background refresh loading animation on WebView error
            safeSetState(setIsBackgroundRefreshing, false, 'isBackgroundRefreshing');
            stopRotationAnimation();
          }}
        />
      );

      setNotificationsWebView(webViewComponent);
    } catch (error) {
      console.log('❌ Error creating background notifications WebView:', error);
    }
  };

  // Load data with cache-first strategy (student data from cache, notifications refreshed)
  const loadDataWithCache = async () => {
    try {
      console.log('🚀 Starting cache-first data loading...');

      // Check if we're in demo mode first
      const isDemoMode = await AsyncStorage.getItem('is_demo_user');
      if (isDemoMode === 'true') {
        console.log('🎭 Demo mode detected - loading demo notifications and student info');
        
        // Load demo student info first
        await loadStudentInfoFromStorage();
        
        // Use demo data from DemoModeHelper
        const demoNotifications = await handleDemoPortalInit();
        
        setNotificationsData(demoNotifications);
        setHasInitiallyLoaded(true);
        console.log('🎭 Demo notifications loaded:', demoNotifications.length, 'items');
        return;
      }

      // Step 1: Always load student info from cache first (set by LoginScreen)
      console.log('📋 Loading student info from cache...');
      await loadStudentInfoFromStorage();

      // Step 2: Load notifications from cache for instant display
      const hasCachedNotifications = await loadFromCache();

      // Step 3: Get stored credentials for background notifications refresh
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('❌ No stored credentials found');
        setHasInitiallyLoaded(true); // Mark as loaded even if no credentials
        return;
      }

      // Step 4: Always refresh notifications in background (student data stays cached)
      console.log('🔄 Student data loaded from cache, refreshing notifications only...');

      if (hasCachedNotifications) {
        // Show cached notifications immediately, refresh in background
        setHasInitiallyLoaded(true);
        setIsBackgroundRefreshing(true);
        startRotationAnimation();
        console.log('✅ Using cached notifications, refreshing in background...');
      } else {
        // No cached notifications, show ghost loading
        console.log('📭 No cached notifications, showing ghost loading...');
      }

      // Always refresh notifications (but not student data)
      await createNotificationsWebView(storedUsername, storedPassword, sessionKey);

    } catch (error) {
      console.log('❌ Error in cached data loading:', error);
      setHasInitiallyLoaded(true); // Mark as loaded even on error to prevent loops
    }
  };

  // Load student info immediately on component mount
  useEffect(() => {
    console.log('🚀 PortalScreen mounted - Loading student info and checking for cached notifications...');
    loadStudentInfoFromStorage();
    
    // Also check for cached notifications immediately on mount
    const loadInitialData = async () => {
      try {
        const isDemoMode = await AsyncStorage.getItem('is_demo_user');
        if (isDemoMode === 'true') {
          console.log('🎭 Demo mode detected on mount - loading demo notifications');
          const demoNotifications = await handleDemoPortalInit();
          setNotificationsData(demoNotifications);
          setHasInitiallyLoaded(true);
        } else {
          // Load cached notifications immediately if available
          const hasCachedNotifications = await loadFromCache();
          if (hasCachedNotifications) {
            console.log('✅ Cached notifications loaded on mount');
            setHasInitiallyLoaded(true);
          }
        }
      } catch (error) {
        console.log('❌ Error loading initial data on mount:', error);
      }
    };
    
    loadInitialData();
    // Start floating animation for refresh button
    startFloatingAnimation();
  }, []);

  // Note: Student name retry logic removed - student data now cached from LoginScreen

  // Watch for loading state changes and manage refresh button animation
  // NOTE: Commented out to prevent premature animation stopping
  // useEffect(() => {
  //   const isLoading = isRefreshing || isBackgroundRefreshing || !hasInitiallyLoaded;
  //   console.log('🔄 Loading state changed:', {
  //     isRefreshing,
  //     isBackgroundRefreshing,
  //     hasInitiallyLoaded,
  //     isLoading
  //   });

  //   if (!isLoading) {
  //     // All loading states are false, ensure animation is stopped
  //     setTimeout(() => {
  //       stopRotationAnimation();
  //     }, 100); // Small delay to ensure state updates are processed
  //   }
  // }, [isRefreshing, isBackgroundRefreshing, hasInitiallyLoaded]);

  useEffect(() => {
    // Get WebView data from navigation params
    const webViewData = route?.params?.webViewData;

    if (webViewData && webViewData.html) {
      const initializeData = async () => {
        try {
          console.log('🔄 Initializing Portal with WebView data...');
          const extracted = await extractStudentInfo(webViewData.html);

          // Ensure extracted data is valid before setting
          if (extracted && typeof extracted === 'object') {
            setStudentInfo(prev => ({
              ...prev,
              ...extracted
            }));
            console.log('✅ Student info extracted and set:', extracted);
          } else {
            console.log('⚠️ No valid student info extracted, using defaults');
            setStudentInfo({
              fullName: '',
              studentId: '',
              faculty: ''
            });
          }

          // Create notifications WebView after successful login and save to cache
          await createNotificationsWebView();

          // Save initial data to cache
          setTimeout(() => {
            saveToCache(extracted, notificationsData);
          }, 3000); // Wait for notifications to load
        } catch (error) {
          console.log('❌ Error initializing Portal data:', error);
          errorLog('Portal initialization error', {
            error: error.message,
            stack: error.stack,
            context: 'PortalScreen.initializeData'
          });
          // Set default student info on error
          setStudentInfo({
            fullName: '',
            studentId: '',
            faculty: ''
          });
        }
      };

      // Also try to load cached data first, even when we have webViewData
      const initializeWithCache = async () => {
        console.log('🔄 Loading cached data first, then initializing...');
        
        // Check if we're in demo mode first to avoid overriding demo data
        const isDemoMode = await AsyncStorage.getItem('is_demo_user');
        if (isDemoMode === 'true') {
          console.log('🎭 Demo mode detected - only loading demo data, skipping WebView initialization');
          await loadDataWithCache();
          setHasInitiallyLoaded(true);
          return;
        }
        
        // Not demo mode - proceed with normal WebView initialization
        await loadDataWithCache();

        initializeData();
        setHasInitiallyLoaded(true);
      };

      initializeWithCache();
    } else {
      console.log('🔄 No WebView data, using caching system...');
      // No webViewData, use caching system
      loadDataWithCache();
    }
  }, [route?.params?.webViewData]);

  // Check offline mode on component mount
  useEffect(() => {
    const initializeOfflineMode = async () => {
      const isOffline = await checkOfflineMode();
      if (isOffline) {
        console.log('📴 Portal loaded in offline mode');
      }
    };

    initializeOfflineMode();
  }, []);

  // Reset session and dispose WebViews when component unmounts or when navigating away
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      console.log('🧹 PortalScreen unmounting - Killing all background operations...');
      await killAllBackgroundOperations();
    });

    return unsubscribe;
  }, [navigation]);

  // Reset session when returning to Portal (e.g., after logout and fresh login)
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      console.log('🔄 PortalScreen focused - Checking for fresh session...');

      // If we're coming from a fresh login, ensure we have a fresh session key
      const webViewData = route?.params?.webViewData;
      if (webViewData && webViewData.credentials) {
        console.log('✅ Fresh login detected - Resetting session key');
        setSessionKey(Date.now()); // Use timestamp for unique session
      }
    });

    return unsubscribe;
  }, [navigation, route?.params?.webViewData]);

  // Handle screen focus/blur - kill background operations when losing focus
  useFocusEffect(
    useCallback(() => {
      console.log('🔄 Screen focused - checking student info and data...');

      // Mark component as mounted when focused
      isMountedRef.current = true;

      // Reload student info from localStorage when screen is focused
      loadStudentInfoFromStorage();

      // Only trigger loading if we don't have notifications data AND we're not in demo mode
      if (!notificationsData) {
        console.log('📭 No notifications data on focus, checking demo mode...');
        AsyncStorage.getItem('is_demo_user').then(isDemoMode => {
          if (isDemoMode !== 'true') {
            console.log('📭 Not demo mode - triggering load...');
            loadDataWithCache();
          } else {
            console.log('🎭 Demo mode detected - not reloading to preserve demo data');
          }
        });
      }

      // Return cleanup function that runs when screen loses focus
      return () => {
        console.log('🔄 Screen losing focus - killing background operations...');
        killAllBackgroundOperations();
      };
    }, [notificationsData])
  );

  return (
    <View style={{ flex: 1 }} {...swipeGestureHandler.panHandlers}>
      <SafeAreaView style={styles.container}>
      {/* Sidebar Button */}
      <View style={styles.sidebarButtonContainer}>
        <TouchableOpacity
          style={styles.sidebarButton}
          onPress={openSidebar}
        >
          <HamburgerIcon size={20} color={theme.colors.primary} strokeWidth={3} />
        </TouchableOpacity>
      </View>

      {/* Portal Title */}
      <View style={styles.portalTitleContainer}>
        <Text style={styles.portalTitle}>Portal</Text>
      </View>

      {/* Refresh Button */}
      <Animated.View
        style={[
          styles.refreshButtonContainer,
          {
            transform: [{
              translateY: refreshFloat.interpolate({
                inputRange: [0, 1],
                outputRange: [0, -8]
              })
            }]
          }
        ]}
      >
        <TouchableOpacity
          style={[
            styles.refreshButton,
            isOfflineMode && styles.refreshButtonDisabled
          ]}
          onPress={isOfflineMode ? undefined : handleRefresh}
          disabled={isRefreshing || isBackgroundRefreshing || isOfflineMode}
          activeOpacity={isOfflineMode ? 0.3 : 0.7}
        >
          <Animated.View
            style={{
              transform: [{
                rotate: isOfflineMode ? '0deg' : refreshRotation.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg']
                })
              }]
            }}
          >
            <RefreshIcon
              size={24}
              color={isOfflineMode ? '#808080' : (isRefreshing || isBackgroundRefreshing) ? '#808080' : safeCurrentThemeName === 'navy' ? '#DC2626' : '#f1c40f'}
            />
          </Animated.View>
        </TouchableOpacity>
      </Animated.View>

      {/* Top section - Welcome section - Always visible */}
      <View style={styles.topSection}>
        {isRefreshing ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Refreshing data...</Text>
          </View>
        ) : (
          <View style={styles.welcomeContainer}>
            <Animated.View style={{ transform: [{ scale: transitionAnim }], opacity: fadeAnim }}>
              <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
                <Text style={styles.welcomeText}>
                  {/* Student name always available in cache - prevent flickering */}
                  Hello {studentNameLoaded && studentInfo.fullName ? getFirstName(studentInfo.fullName.trim()) : (studentNameLoaded ? 'Student' : 'Loading...')}!
                </Text>
                <Text style={[styles.welcomeText]}>👋</Text>
              </View>
            </Animated.View>

            {/* CMS and Mail Buttons */}
            <View style={styles.buttonContainer}>
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  isOfflineMode && styles.actionButtonDisabled
                ]}
                onPress={() => {
                  if (isOfflineMode) {
                    Alert.alert(
                      'Feature Unavailable',
                      'CMS is not available in offline mode. Please go online to access this feature.',
                      [{ text: 'OK' }]
                    );
                    return;
                  }
                  navigation.navigate('CMS');
                }}
                activeOpacity={isOfflineMode ? 0.3 : 0.7}
              >
                <Text style={[
                  styles.actionButtonText,
                  isOfflineMode && styles.actionButtonTextDisabled
                ]}>CMS</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.actionButton,
                  isOfflineMode && styles.actionButtonDisabled
                ]}
                onPress={() => {
                  if (isOfflineMode) {
                    Alert.alert(
                      'Feature Unavailable',
                      'Mail is not available in offline mode. Please go online to access this feature.',
                      [{ text: 'OK' }]
                    );
                    return;
                  }
                  navigation.navigate('Mail');
                }}
                activeOpacity={isOfflineMode ? 0.3 : 0.7}
              >
                <Text style={[
                  styles.actionButtonText,
                  isOfflineMode && styles.actionButtonTextDisabled
                ]}>Mail</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </View>

      {/* Bottom section - NotificationsCenter component */}
      <View style={styles.bottomSection}>
        <NotificationsCenter
          notificationsWebView={notificationsWebView}
          notificationsData={notificationsData}
          onNotificationPress={openNotificationModal}
          theme={theme}
          hasInitiallyLoaded={hasInitiallyLoaded}
          isRefreshing={isRefreshing}
        />
      </View>

      {/* Notification Modal */}
      <Modal
        visible={notificationModalVisible}
        transparent={true}
        animationType="none"
        statusBarTranslucent={true}
        onRequestClose={closeNotificationModal}
      >
        <StatusBar backgroundColor="rgba(0, 0, 0, 0.5)" barStyle="light-content" />
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={closeNotificationModal}
        >
          <Animated.View
            style={[
              styles.modalContainer,
              {
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Handle area - only this area responds to swipe down */}
            <View
              style={styles.modalHandle}
              {...handlePanResponder.panHandlers}
            >
              <View style={styles.modalHandleBar} />
            </View>

            {/* Content area - scrollable, doesn't respond to pan gestures */}
            {selectedNotification && (
              <ScrollView
                style={styles.modalContent}
                showsVerticalScrollIndicator={false}
                nestedScrollEnabled={true}
                scrollEventThrottle={16}
              >
                <TouchableOpacity activeOpacity={1} onPress={() => {}}>
                  <View style={styles.notificationHeader}>
                    <Text style={styles.notificationTitle}>
                      {selectedNotification.subject}
                    </Text>
                    <Text style={styles.notificationSender}>
                      {selectedNotification.sender}
                    </Text>
                    <Text style={styles.notificationDate}>
                      {selectedNotification.date}
                    </Text>
                  </View>

                  <View style={styles.notificationDivider} />

                  <View style={styles.notificationBody}>
                    <Text style={styles.notificationContent}>
                      {selectedNotification.content}
                    </Text>
                  </View>
                </TouchableOpacity>
              </ScrollView>
            )}
          </Animated.View>
        </TouchableOpacity>
      </Modal>

      {/* Sidebar Component */}
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={closeSidebar}
        sidebarAnim={sidebarAnim}
        navigation={navigation}
        currentScreen="Home"
      />

      {/* Hidden WebView for notifications */}
      <View style={styles.hiddenWebView}>
        {notificationsWebView}
      </View>
    </SafeAreaView>
    </View>
  );
};

// Create styles function that uses theme
const createStyles = (theme, safeCurrentThemeName) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  portalTitleContainer: {
    position: 'absolute',
    top: 55, // Raised higher
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 999,
  },
  portalTitle: {
    fontSize: 30, // Increased font size slightly
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
  },
  sidebarButtonContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 1000,
  },
  sidebarButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  refreshButtonContainer: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1000,
  },
  refreshButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  refreshButtonDisabled: {
    opacity: 0.4,
    backgroundColor: theme.colors.surface + '80',
  },
  topSection: {
    flex: 0.25, // Reduced from 40% to 30% to make room for notifications
    justifyContent: 'flex-start', // Changed from 'center' to 'flex-start'
    alignItems: 'center',
    paddingHorizontal: 32,
    paddingBottom: 20,
    paddingTop: 115, // Increased from 120 to 140 to raise content even higher
  },
  bottomSection: {
    flex: 0.75, // Increased from 60% to 80% for more notification space
    paddingTop: 20, // Add some padding at the top
  },
  welcomeContainer: {
    width: '100%',
    alignItems: 'center',
  },
  welcomeText: {
    fontSize: Math.min(36, Dimensions.get('window').width * 0.09), // Responsive font size
    fontWeight: 'bold',
    color: theme.colors.primary, // Use the exact same yellow shade as other themes
    textAlign: 'center',
    paddingHorizontal: 10,
    marginBottom: 10
  },
  backgroundLoadingText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginTop: -20,
    marginBottom: 10,
    fontStyle: 'italic',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'center', // Center the buttons
    alignItems: 'center',
    width: '100%',
    marginTop: 15, // Reduced from 30 to 15 to move buttons even higher
    paddingHorizontal: 20, // Increased from 10 to 20 for better spacing
    flexWrap: 'wrap', // Allow wrapping on very small screens
    gap: Dimensions.get('window').width * 0.05, // Responsive gap between buttons (5% of screen width)
  },
  actionButton: {
    backgroundColor: theme.colors.primary,
    paddingVertical: Math.max(15, Dimensions.get('window').height * 0.02), // Responsive padding
    paddingHorizontal: Math.max(25, Dimensions.get('window').width * 0.06), // Slightly reduced horizontal padding
    borderRadius: 15,
    borderWidth: 2,
    borderColor: theme.colors.primary,
    width: Math.max(110, Dimensions.get('window').width * 0.32), // Fixed width for consistent sizing
    alignItems: 'center',
    justifyContent: 'center', // Ensure text is centered
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  actionButtonText: {
    fontSize: Math.min(18, Dimensions.get('window').width * 0.045), // Responsive font size
    fontWeight: 'bold',
    color: theme.colors.primaryText,
    textAlign: 'center',
  },
  actionButtonDisabled: {
    backgroundColor: theme.colors.surface,
    opacity: 0.5,
    borderColor: theme.colors.border,
  },
  actionButtonTextDisabled: {
    color: theme.colors.textSecondary,
    opacity: 0.6,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  hiddenWebView: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
    opacity: 0,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: theme.colors.modalOverlay,
    justifyContent: 'flex-end',
    paddingTop: StatusBar.currentHeight || 0,
  },
  modalContainer: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: '80%',
    paddingTop: 10,
  },
  modalHandle: {
    height: 60,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
  },
  modalHandleBar: {
    width: 40,
    height: 4,
    backgroundColor: '#888888',
    borderRadius: 2,
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
  },
  notificationHeader: {
    paddingBottom: 20,
  },
  notificationTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 10,
  },
  notificationSender: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginBottom: 5,
  },
  notificationDate: {
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  notificationDivider: {
    height: 1,
    backgroundColor: theme.colors.border,
    marginVertical: 20,
  },
  notificationBody: {
    paddingBottom: 40,
  },
  notificationContent: {
    fontSize: 16,
    color: theme.colors.text,
    lineHeight: 24,
  },
  initialLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 100, // Account for header space
  },
  loadingSpinnerContainer: {
    marginBottom: 20,
  },
  loadingSpinner: {
    fontSize: 40,
    color: '#EAB308', // Golden yellow spinner
    fontWeight: 'bold',
  },
  initialLoadingText: {
    fontSize: 18,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },

});

export default HomeScreen;
