import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Animated,
  Modal,
  TextInput,
  PanResponder,
  Dimensions,
  Easing,
  TouchableWithoutFeedback,
  Keyboard,
  Platform,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import Sidebar from '../../components/Sidebar';
import HamburgerIcon from '../../components/HamburgerIcon';
import RefreshIcon from '../../components/RefreshIcon';
import ThemeTransitionWrapper from '../../components/ThemeTransitionWrapper';
import Svg, { Circle } from 'react-native-svg';
import { MaterialIcons } from '@expo/vector-icons';
import { clearWebViewSession, disposeWebView } from '../../utils/WebViewUtils';
import { useTheme } from '../../contexts/ThemeContext';
import { useOffline } from '../../contexts/OfflineContext';
import { checkDemoMode, DEMO_GRADES_DATA, getDemoGradesDropdown, getDemoMidtermGrade } from '../../utils/DemoData';
import { getCachedGradesCourses } from '../../utils/OfflineUtils';


const GradesScreen = ({ navigation }) => {
  // Theme context
  const { theme, currentThemeName } = useTheme();

  // Offline context
  const { isOfflineMode } = useOffline();

  // Fallback for currentThemeName to prevent undefined errors
  const safeCurrentThemeName = currentThemeName || 'dark';

  const [isLoading, setIsLoading] = useState(true);
  const [credentials, setCredentials] = useState(null);
  const [gradesUrl, setGradesUrl] = useState('');
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const [sidebarAnim] = useState(new Animated.Value(-300));

  // Generate styles based on current theme
  const styles = createStyles(theme, safeCurrentThemeName);
  const [dropdownOptions, setDropdownOptions] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState({ main: 'Select Course', code: '' });
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [isDropdownLoading, setIsDropdownLoading] = useState(true);
  const [gradesData, setGradesData] = useState([]);
  const [isLoadingGrades, setIsLoadingGrades] = useState(false);
  const [midtermGrade, setMidtermGrade] = useState(null);
  const [refreshRotation] = useState(new Animated.Value(0)); // For rotating refresh arrow
  const [isUpdatingGrades, setIsUpdatingGrades] = useState(false);
  const [hasCachedGrades, setHasCachedGrades] = useState(false);
  const [isCalculatorVisible, setIsCalculatorVisible] = useState(false);
  const [calculatorSections, setCalculatorSections] = useState([]);
  const [calculatorMode, setCalculatorMode] = useState('main'); // 'main' or 'addCoursework'

  // Calculator modal animation values
  const calculatorModalScale = useRef(new Animated.Value(0)).current;
  const calculatorModalOpacity = useRef(new Animated.Value(0)).current;
  const calculatorOverlayOpacity = useRef(new Animated.Value(0)).current;
  const [currentCoursework, setCurrentCoursework] = useState({
    title: '',
    weight: '',
    gradeCount: '',
    assignedGrades: []
  });
  const [showWeightInput, setShowWeightInput] = useState(false);
  const [weightInputValue, setWeightInputValue] = useState('');
  const [isEditingSection, setIsEditingSection] = useState(false);
  const [gradeListUpdateTrigger, setGradeListUpdateTrigger] = useState(0); // Force re-render trigger
  const [forceUpdateCounter, setForceUpdateCounter] = useState(0); // Additional force update mechanism
  const [previousGradesData, setPreviousGradesData] = useState([]); // Store previous grades for comparison
  const [animatedGrades, setAnimatedGrades] = useState(new Set()); // Track which grades have been animated
  const completedAnimationsRef = useRef(new Set()); // Track completed animations (immediate access)
  const animationDecisionRef = useRef(null); // Store the latest animation decision
  const webViewRef = useRef(null);

  // Refs for tracking background operations and component mount state
  const isMountedRef = useRef(true);
  const activeTimeoutsRef = useRef(new Set());
  const activeAnimationsRef = useRef(new Set());
  const gradeAnimationRefs = useRef(new Map()); // Track individual grade animations

  // Comprehensive function to kill all background operations
  const killAllBackgroundOperations = async () => {
    console.log('🛑 GradesScreen: Killing all background operations...');

    // Mark component as unmounted to prevent state updates
    isMountedRef.current = false;

    // Clear all active timeouts
    activeTimeoutsRef.current.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    activeTimeoutsRef.current.clear();

    // Stop all animations
    refreshRotation.stopAnimation();
    sidebarAnim.stopAnimation();
    calculatorModalScale.stopAnimation();
    calculatorModalOpacity.stopAnimation();
    calculatorOverlayOpacity.stopAnimation();

    // Stop all grade animations
    gradeAnimationRefs.current.forEach((animationData, gradeId) => {
      if (animationData.animatedValue) {
        animationData.animatedValue.stopAnimation();
      }
      if (animationData.listener) {
        animationData.animatedValue.removeListener(animationData.listener);
      }
    });
    gradeAnimationRefs.current.clear();

    // Reset all loading states to prevent cache corruption
    setIsLoading(false);
    setIsLoadingGrades(false);
    setIsUpdatingGrades(false);
    setIsDropdownLoading(false);

    // Dispose of WebView
    await disposeWebView(webViewRef, 'grades-webview');

    console.log('✅ GradesScreen: All background operations killed');
  };

  // Safe state setter that checks if component is still mounted
  const safeSetState = (setter, value, stateName) => {
    if (isMountedRef.current) {
      setter(value);
    } else {
      console.log(`⚠️ GradesScreen: Prevented ${stateName} state update after unmount`);
    }
  };

  // Safe timeout wrapper that tracks timeouts for cleanup
  const safeSetTimeout = (callback, delay) => {
    const timeoutId = setTimeout(() => {
      activeTimeoutsRef.current.delete(timeoutId);
      if (isMountedRef.current) {
        callback();
      }
    }, delay);
    activeTimeoutsRef.current.add(timeoutId);
    return timeoutId;
  };

  useEffect(() => {
    loadCredentialsAndSetupGrades();

    // Cleanup function
    return () => {
      console.log('🧹 GradesScreen: Component unmounting - cleaning up...');
      killAllBackgroundOperations();
    };
  }, []);

  // Handle navigation focus/blur - kill background operations when losing focus
  useFocusEffect(
    useCallback(() => {
      console.log('🔄 GradesScreen: Screen focused');

      // Mark component as mounted when focused
      isMountedRef.current = true;

      // Clear previous grades for fresh comparison
      setPreviousGradesData([]);
      completedAnimationsRef.current = new Set();

      // Return cleanup function that runs when screen loses focus
      return () => {
        console.log('🔄 GradesScreen: Screen losing focus - killing background operations...');
        killAllBackgroundOperations();
      };
    }, [])
  );

  // Kill background operations when navigating away completely
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      console.log('🧹 GradesScreen: Screen unmounting - Killing all background operations...');
      await killAllBackgroundOperations();
    });

    return unsubscribe;
  }, [navigation]);

  const openSidebar = () => {
    setIsSidebarVisible(true);
    Animated.timing(sidebarAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeSidebar = () => {
    Animated.timing(sidebarAnim, {
      toValue: -300,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsSidebarVisible(false);
    });
  };

  // Swipe gesture handler for opening/closing sidebar - Enhanced for Android
  const swipeGestureHandler = PanResponder.create({
    onStartShouldSetPanResponder: () => false, // Don't capture immediately
    onStartShouldSetPanResponderCapture: () => false, // Don't capture on start
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to significant horizontal swipes
      const { dx, dy } = gestureState;

      // Only capture if it's a clear horizontal swipe with significant movement
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
      // Only capture significant horizontal swipes to prevent Android system gestures
      const { dx, dy } = gestureState;
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onPanResponderGrant: () => {
      // Gesture has been granted - prevent other handlers
      return true;
    },
    onPanResponderMove: () => {
      // Optional: Add visual feedback during swipe
    },
    onPanResponderRelease: (evt, gestureState) => {
      const { dx, dy } = gestureState;
      const isHorizontalSwipe = Math.abs(dx) > Math.abs(dy);
      const swipeDistance = Math.abs(dx);

      if (isHorizontalSwipe && swipeDistance > 100) {
        if (dx > 0) {
          // Swipe right - open sidebar
          openSidebar();
        } else {
          // Swipe left - close sidebar if it's open
          if (isSidebarVisible) {
            closeSidebar();
          }
        }
      }
    },
    onPanResponderTerminationRequest: () => false, // Don't allow termination once we have it
    onShouldBlockNativeResponder: () => true, // Block native responders only when we have the gesture
  });

  // Cache management functions for individual course grades
  const getCacheKey = async (courseCode) => {
    const isDemoMode = await AsyncStorage.getItem('is_demo_user');
    const prefix = isDemoMode === 'true' ? 'demo_grades_cache' : 'grades_cache';
    return `${prefix}_${courseCode}`;
  };
  const getCacheTimestampKey = async (courseCode) => {
    const isDemoMode = await AsyncStorage.getItem('is_demo_user');
    const prefix = isDemoMode === 'true' ? 'demo_grades_cache_timestamp' : 'grades_cache_timestamp';
    return `${prefix}_${courseCode}`;
  };

  const saveGradesToCache = async (courseCode, gradesData, midtermGrade) => {
    try {
      // Don't cache demo data for real accounts or vice versa
      const isDemoMode = await AsyncStorage.getItem('is_demo_user');
      console.log(`📦 Saving grades to cache for course: ${courseCode}, demo mode: ${isDemoMode}`);

      const cacheData = {
        gradesData: gradesData,
        midtermGrade: midtermGrade,
        timestamp: Date.now(),
        isDemoData: isDemoMode === 'true'
      };

      const cacheKey = await getCacheKey(courseCode);
      const timestampKey = await getCacheTimestampKey(courseCode);

      await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheData));
      await AsyncStorage.setItem(timestampKey, Date.now().toString());
      console.log(`📦 Grades cached successfully for course: ${courseCode} (${isDemoMode === 'true' ? 'demo' : 'real'} mode)`);
    } catch (error) {
      console.log('❌ Error saving grades to cache:', error);
    }
  };

  const loadGradesFromCache = async (courseCode) => {
    try {
      const isDemoMode = await AsyncStorage.getItem('is_demo_user');
      const cacheKey = await getCacheKey(courseCode);
      const cachedData = await AsyncStorage.getItem(cacheKey);

      if (cachedData) {
        const parsedData = JSON.parse(cachedData);

        // Verify that cached data matches current mode (demo vs real)
        const cachedIsDemoData = parsedData.isDemoData === true;
        const currentIsDemoMode = isDemoMode === 'true';

        if (cachedIsDemoData !== currentIsDemoMode) {
          console.log(`🚫 Cache mode mismatch for course: ${courseCode}. Cached: ${cachedIsDemoData ? 'demo' : 'real'}, Current: ${currentIsDemoMode ? 'demo' : 'real'}. Clearing cache.`);
          await AsyncStorage.removeItem(cacheKey);
          return false;
        }

        console.log(`📦 Loading cached grades for course: ${courseCode} (${currentIsDemoMode ? 'demo' : 'real'} mode)`);

        // Set cached data to state using smart animation system
        updateGradesData(parsedData.gradesData || []);
        setMidtermGrade(parsedData.midtermGrade || null);
        setHasCachedGrades(true);

        return true; // Cache loaded successfully
      }
      return false; // No cache found
    } catch (error) {
      console.log('❌ Error loading grades from cache:', error);
      return false;
    }
  };

  const clearGradesCache = async (courseCode) => {
    try {
      await AsyncStorage.removeItem(getCacheKey(courseCode));
      await AsyncStorage.removeItem(getCacheTimestampKey(courseCode));
      console.log(`🗑️ Grades cache cleared for course: ${courseCode}`);
    } catch (error) {
      console.log('❌ Error clearing grades cache:', error);
    }
  };

  const handleRefresh = async () => {
    console.log('🔄 Refresh button pressed - reloading course grades');

    // Check if a course is selected
    if (!selectedCourse || !selectedCourse.main || selectedCourse.main === 'Select Course') {
      console.log('⚠️ No course selected for refresh');
      return;
    }

    // Check if we're in demo mode
    const isDemoMode = await AsyncStorage.getItem('is_demo_user');
    if (isDemoMode === 'true') {
      console.log('🎭 Demo mode - simulating refresh');

      // Start rotation animation
      startRotationAnimation();

      // Simulate loading delay
      setTimeout(() => {
        stopRotationAnimation();
        console.log('🎭 Demo refresh complete');
      }, 1500);

      return;
    }

    // Find the selected course option from dropdownOptions
    const selectedOption = dropdownOptions.find(option => {
      const formatted = formatCourseName(option.text);
      return formatted.main === selectedCourse.main && formatted.code === selectedCourse.code;
    });

    if (!selectedOption) {
      console.log('⚠️ Selected course not found in dropdown options');
      return;
    }

    console.log('🔄 Refreshing grades for course:', selectedCourse.main, selectedCourse.code);

    // Clear cache for this course to force fresh data
    await clearGradesCache(selectedCourse.code);

    // Clear current data and start loading
    setIsLoadingGrades(true);
    setGradesData([]);
    setMidtermGrade(null);
    setHasCachedGrades(false);
    setIsUpdatingGrades(false); // Don't show update indicator during manual refresh

    // Clear previous grades and completed animations so refresh will animate all grades
    setPreviousGradesData([]);
    completedAnimationsRef.current = new Set();

    // Start rotation animation
    startRotationAnimation();

    // Re-trigger the course selection process
    selectCourseInWebView(selectedOption.value);
  };

  // Format course name: cut until '-', split on space, put index 1 first and index 0 at the end
  const formatCourseName = (courseName) => {
    if (!courseName) return { main: '', code: '' };

    const dashIndex = courseName.indexOf('- ');
    if (dashIndex !== -1) {
      const afterDash = courseName.substring(dashIndex + 2); // +2 to skip "- "
      const parts = afterDash.split(' ');

      if (parts.length >= 2) {
        const code = parts[0]; // Course code (e.g., "CSEN602")
        const name = parts.slice(1).join(' '); // Course name (e.g., "Operating Systems")
        return { main: name, code: code };
      }
      return { main: afterDash, code: '' };
    }
    return { main: courseName, code: '' };
  };

  // Handle dropdown option selection with cache-first strategy
  const handleCourseSelect = async (option) => {
    const formatted = formatCourseName(option.text);
    setSelectedCourse(formatted);
    setIsDropdownVisible(false);
    console.log('📚 Course selected:', formatted.main, formatted.code);

    // Clear previous grades and completed animations so new course will animate all grades
    setPreviousGradesData([]);
    completedAnimationsRef.current = new Set();

    // Check if we're in demo mode
    const isDemoMode = await AsyncStorage.getItem('is_demo_user');
    if (isDemoMode === 'true') {
      console.log('🎭 Demo mode - loading demo grades for course:', option.text);

      // Get demo grades for this course
      const demoGrades = DEMO_GRADES_DATA[option.text] || [];
      const demoMidterm = getDemoMidtermGrade(option.text);

      // Simulate loading delay
      setIsLoadingGrades(true);
      setTimeout(() => {
        setGradesData(demoGrades);
        setMidtermGrade(demoMidterm);
        setIsLoadingGrades(false);
        setHasCachedGrades(false); // Demo data should not be treated as cached
        console.log('🎭 Demo grades loaded:', demoGrades.length, 'grades');
        console.log('🎭 Demo midterm grade:', demoMidterm);
        console.log('🎭 Demo data will not be cached to prevent mixing with real data');
      }, 1000);

      return;
    }

    // Try to load cached grades first
    const cacheLoaded = await loadGradesFromCache(formatted.code);

    if (cacheLoaded) {
      console.log('✅ Cached grades loaded, starting background refresh');
      // Show update indicator and fetch fresh data in background
      setIsUpdatingGrades(true);
      startRotationAnimation(); // Start rotation animation for refresh button
      // Inject JavaScript to set the dropdown value and trigger change
      selectCourseInWebView(option.value);
    } else {
      console.log('📭 No cache found, loading fresh grades');
      // No cache, load normally
      setIsLoadingGrades(true);
      setGradesData([]); // Clear previous grades
      setMidtermGrade(null); // Clear previous midterm grade
      setHasCachedGrades(false);
      // Inject JavaScript to set the dropdown value and trigger change
      selectCourseInWebView(option.value);
    }
  };

  // Function to select course in WebView and wait for results
  const selectCourseInWebView = (courseValue) => {
    if (webViewRef.current) {
      console.log('🔄 Phase 1: Setting course value in WebView:', courseValue);

      // Phase 1: Select dropdown and dispatch events
      const phase1JS = `
        (function() {
          try {

            // Set the value of the dropdown
            const dropdown = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_smCrsLst');

            if (dropdown) {
              dropdown.value = '${courseValue}';

              // Trigger multiple events to ensure it works
              dropdown.dispatchEvent(new Event('change', { bubbles: true }));
              dropdown.dispatchEvent(new Event('input', { bubbles: true }));

              // Notify React Native that phase 1 is complete
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'phase1_complete',
                message: 'Dropdown selection completed, waiting for page reload...'
              }));
            } else {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'course_result_error',
                error: 'Dropdown not found'
              }));
            }
          } catch (error) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'course_result_error',
              error: error.message
            }));
          }
        })();
        true;
      `;

      // Inject Phase 1 code
      webViewRef.current.injectJavaScript(phase1JS);

      // Wait longer for iOS compatibility
      safeSetTimeout(() => {
        console.log('� Phase 2: Extracting table data after delay (iOS compatible)...');
        injectPhase2ExtractionCode();
      }, 4000); // Increased to 4 seconds for iOS
    }
  };

  // Phase 2: Extract table data from the newly loaded page
  const injectPhase2ExtractionCode = () => {
    if (webViewRef.current) {
      const phase2JS = `
        (function() {
          try {
            // Monitor the specific nttTr div for table appearance
            const targetDiv = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_nttTr');
          
            if (targetDiv) {
              // Function to extract table data
              function extractTableData() {
                const table = targetDiv.querySelector('table.table.table-bordered');

                if (table) {

                  // Get all table rows except the header row
                  const rows = table.querySelectorAll('tbody tr');
                  const data = [];

                  // Loop through rows, skipping the header
                  for (let i = 0; i < rows.length; i++) {
                    const cells = rows[i].querySelectorAll('td');

                    if (cells.length < 4) {
                      continue;
                    }

                    // Enhanced text extraction that handles span elements properly
                    const getName = (cell) => {
                      if (cell.innerText) return cell.innerText.trim();
                      if (cell.textContent) return cell.textContent.trim();
                      if (cell.innerHTML) return cell.innerHTML.replace(/<[^>]*>/g, '').trim();
                      return '';
                    };

                    // Special extraction for Quiz/Assignment column (check for span content)
                    const getQuizAssignmentName = (cell) => {
                      // Look for span element first (this contains the actual quiz/assignment name)
                      const span = cell.querySelector('span');
                      if (span && span.textContent && span.textContent.trim() !== '') {
                        const spanText = span.textContent.trim();
                        console.log('🔍 Found span with content:', spanText);
                        return spanText;
                      }

                      // Check if cell has any visible text content (excluding hidden inputs)
                      let cellText = '';
                      if (cell.childNodes) {
                        for (let i = 0; i < cell.childNodes.length; i++) {
                          const node = cell.childNodes[i];
                          // Only get text from text nodes and visible elements (not hidden inputs)
                          if (node.nodeType === 3) { // Text node
                            cellText += node.textContent || '';
                          } else if (node.nodeType === 1 && node.tagName !== 'INPUT') { // Element node, not input
                            cellText += node.textContent || '';
                          }
                        }
                      }

                      cellText = cellText.trim();
                      console.log('🔍 Cell text after filtering:', cellText);

                      // If the cell only contains hidden input or is empty, return empty string
                      if (!cellText || cellText === '') {
                        console.log('🔍 Cell is empty, will use Element Name');
                        return '';
                      }

                      console.log('🔍 Using cell text:', cellText);
                      return cellText;
                    };

                    const QuizAssignment = getQuizAssignmentName(cells[0]); // First column: Quiz/Assignment
                    const ElementName = getName(cells[1]);                  // Second column: Element Name
                    const Grade = getName(cells[2]);                        // Third column: Grade
                    const staff = getName(cells[3]);                        // Fourth column: Staff

                    console.log('🔍 Row data - Quiz/Assignment:', QuizAssignment, 'Element Name:', ElementName, 'Grade:', Grade);

                    // Skip header row
                    if (QuizAssignment === 'Quiz/Assignment' || ElementName === 'Element Name' ||
                        Grade === 'Grade' || staff === 'Prof./Lecturer/TA') {
                      console.log('🔍 Skipping header row');
                      continue;
                    }

                    // Smart name selection logic
                    let Name = '';

                    // Special case: If Quiz/Assignment is generic (like "SM Assignments") and Element Name is more specific, prefer Element Name
                    if (QuizAssignment && QuizAssignment.trim() !== '' && ElementName && ElementName.trim() !== '') {
                      // If Quiz/Assignment is generic and Element Name is more specific, use Element Name
                      if (QuizAssignment.toLowerCase().includes('assignments') &&
                          !ElementName.toLowerCase().includes('assignments') &&
                          ElementName.length > 3) {
                        Name = ElementName;
                        console.log('✅ Using Element Name (more specific than generic assignment):', Name);
                      } else {
                        Name = QuizAssignment;
                        console.log('✅ Using Quiz/Assignment name:', Name);
                      }
                    }
                    // Quiz/Assignment has content but Element Name is empty
                    else if (QuizAssignment && QuizAssignment.trim() !== '') {
                      Name = QuizAssignment;
                      console.log('✅ Using Quiz/Assignment name:', Name);
                    }
                    // Quiz/Assignment is empty, use Element Name
                    else if (ElementName && ElementName.trim() !== '') {
                      Name = ElementName;
                      console.log('✅ Using Element Name (Quiz/Assignment empty):', Name);
                    }
                    else {
                      // Skip rows with no meaningful name in either column
                      console.log('❌ Skipping row - no meaningful name found');
                      continue;
                    }


                    data.push({ Name, Grade, staff });
                  }

                  // Sort alphabetically by Name
                  data.sort((a, b) => a.Name.localeCompare(b.Name));

                  // Extract midDg data before sending
                  let midDgRowsArray = [];
                  const midDgDiv = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_midDg');

                  // iOS debugging - check if div exists and has content

                  if (midDgDiv) {
                    const midDgTable = midDgDiv.querySelector('tbody');

                    if (midDgTable) {
                      const midDgRows = midDgTable.querySelectorAll('tr');

                      for (let i = 0; i < midDgRows.length; i++) {
                        const row = midDgRows[i];
                        const cells = row.querySelectorAll('td, th');
                        const rowData = [];

                        for (let j = 0; j < cells.length; j++) {
                          // iOS-compatible text extraction with multiple fallbacks
                          let cellText = '';
                          if (cells[j].innerText) {
                            cellText = cells[j].innerText.trim();
                          } else if (cells[j].textContent) {
                            cellText = cells[j].textContent.trim();
                          } else if (cells[j].innerHTML) {
                            // Remove HTML tags and get text
                            cellText = cells[j].innerHTML.replace(/<[^>]*>/g, '').trim();
                          }
                          rowData.push(cellText);
                          console.log('📱 iOS Cell', j, 'text:', cellText);
                        }

                        midDgRowsArray.push(rowData);
                        console.log('📝 Added midDg row', i, ':', rowData);
                      }

                      console.log('📊 FINAL midDg table rows array:', midDgRowsArray);
                    } else {
                      console.log('❌ No table found in midDg div');
                    }
                  } else {
                    console.log('❌ midDg div not found');
                  }

                  // Send both grades data and midDg data
                  window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'grades_data_extracted',
                    gradesData: data,
                    totalGrades: data.length,
                    midDgData: midDgRowsArray,
                    totalMidDgRows: midDgRowsArray.length
                  }));

                  return true; // Found and processed
                }
                return false; // Not found yet
              }

              // Try to extract table data immediately
              if (!extractTableData()) {
                console.log('🔍 Phase 2: Table not found immediately, will retry...');
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'course_result_error',
                  error: 'Table not found in Phase 2'
                }));
              }


            } else {
              console.log('❌ Phase 2: Target div (nttTr) not found');
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'course_result_error',
                error: 'Target div (nttTr) not found in Phase 2'
              }));
            }
          } catch (error) {
            console.log('❌ Error in Phase 2:', error);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'course_result_error',
              error: error.message
            }));
          }
        })();
        true;
      `;

      webViewRef.current.injectJavaScript(phase2JS);
    }
  };

  // Toggle dropdown visibility
  const toggleDropdown = () => {
    setIsDropdownVisible(!isDropdownVisible);
  };

  // Rotation animation functions
  const startRotationAnimation = () => {
    refreshRotation.setValue(0);
    Animated.loop(
      Animated.timing(refreshRotation, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    ).start();
  };

  const stopRotationAnimation = () => {
    Animated.timing(refreshRotation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  // Calculator Functions
  const initializeCalculator = async () => {
    console.log('🚀 Initializing calculator...');
    console.log('🚀 Midterm grade:', midtermGrade);
    console.log('🚀 Midterm grade type:', typeof midtermGrade);

    // Load cached calculator data for this course
    const courseKey = selectedCourse?.main || 'unknown';
    const cachedSections = await loadCalculatorCache(courseKey);

    console.log('🚀 Cached sections:', cachedSections);
    console.log('🚀 Course key:', courseKey);

    // Always use cached sections if available, otherwise start with empty array
    const sectionsToUse = cachedSections.length > 0 ? cachedSections : [];
    console.log('🚀 Sections to use:', sectionsToUse);
    setCalculatorSections(sectionsToUse);

    // Check if there's a midterm grade and no existing midterm section
    const hasMidtermGrade = midtermGrade && (
      (typeof midtermGrade === 'string' && midtermGrade.trim() !== '') ||
      (typeof midtermGrade === 'number' && !isNaN(midtermGrade))
    );
    const hasMidtermSection = sectionsToUse.some(section => section.isAutoGenerated);

    console.log('🚀 Has midterm grade:', hasMidtermGrade);
    console.log('🚀 Has midterm section:', hasMidtermSection);

    if (hasMidtermGrade && !hasMidtermSection) {
      console.log('🚀 Midterm grade found, prompting for weight...');
      // Prompt user for midterm weight
      promptForMidtermWeight();
    } else {
      console.log('🚀 No midterm prompt needed');
      if (!hasMidtermGrade) {
        console.log('🚀 Reason: No midterm grade');
      }
      if (hasMidtermSection) {
        console.log('🚀 Reason: Midterm section already exists');
      }
    }
  };

  const promptForMidtermWeight = () => {
    console.log('🎯 promptForMidtermWeight called');
    console.log('🎯 About to show weight input modal');
    console.log('🎯 Current showWeightInput state:', showWeightInput);
    console.log('🎯 Platform:', Platform.OS);

    // Try modal approach first
    setWeightInputValue('');
    setShowWeightInput(true);
    console.log('🎯 showWeightInput set to true');

    // For iOS, also try Alert.prompt as a fallback after a delay
    if (Platform.OS === 'ios') {
      setTimeout(() => {
        console.log('🍎 iOS: Checking if modal appeared...');
        // If modal didn't appear, use Alert.prompt as fallback
        Alert.prompt(
          'Midterm Weight',
          `Your midterm grade is ${typeof midtermGrade === 'number' ? `${midtermGrade}%` : midtermGrade}. Enter the weight percentage (e.g., 30 for 30%):`,
          [
            {
              text: 'Cancel',
              style: 'cancel',
              onPress: () => {
                console.log('🍎 iOS Alert: User cancelled');
                setShowWeightInput(false);
              }
            },
            {
              text: 'Add',
              onPress: (weight) => {
                console.log('🍎 iOS Alert: User entered weight:', weight);
                if (weight && !isNaN(weight) && parseFloat(weight) > 0 && parseFloat(weight) <= 100) {
                  createMidtermSection(weight);
                  setShowWeightInput(false);
                } else {
                  Alert.alert('Invalid Weight', 'Please enter a valid weight between 1 and 100.');
                }
              }
            }
          ],
          'plain-text',
          '20'
        );
      }, 200); // Give modal 1 second to appear
    }
  };

  const handleWeightSubmit = () => {
    const weight = weightInputValue.trim();
    if (weight && !isNaN(weight) && parseFloat(weight) > 0 && parseFloat(weight) <= 100) {
      createMidtermSection(weight);
      setShowWeightInput(false);
      setWeightInputValue('');
      console.log('🎯 Weight submitted and modal closed');
    } else {
      Alert.alert('Invalid Weight', 'Please enter a valid weight between 1 and 100.');
    }
  };

  const handleWeightCancel = () => {
    console.log('🎯 handleWeightCancel called');
    setShowWeightInput(false);
    setWeightInputValue('');
    console.log('🎯 Weight input cancelled and modal closed');
  };

  // Test function to manually trigger modal (for debugging)
  const testModal = () => {
    console.log('🧪 Test modal triggered');
    setShowWeightInput(true);
  };

  // Cache calculator sections per course
  const saveCalculatorCache = async (courseKey, sections) => {
    try {
      const cacheKey = `calculator_${courseKey}`;
      await AsyncStorage.setItem(cacheKey, JSON.stringify(sections));
      console.log('💾 Calculator cache saved for course:', courseKey);
    } catch (error) {
      console.error('❌ Failed to save calculator cache:', error);
    }
  };

  const loadCalculatorCache = async (courseKey) => {
    try {
      const cacheKey = `calculator_${courseKey}`;
      const cachedData = await AsyncStorage.getItem(cacheKey);
      if (cachedData) {
        const sections = JSON.parse(cachedData);
        console.log('📂 Calculator cache loaded for course:', courseKey, sections);
        return sections;
      }
    } catch (error) {
      console.error('❌ Failed to load calculator cache:', error);
    }
    return [];
  };

  const clearCalculatorCache = async (courseKey) => {
    try {
      const cacheKey = `calculator_${courseKey}`;
      await AsyncStorage.removeItem(cacheKey);
      console.log('🗑️ Calculator cache cleared for course:', courseKey);
    } catch (error) {
      console.error('❌ Failed to clear calculator cache:', error);
    }
  };

  const createMidtermSection = (weight) => {
    // Convert midterm grade to proper format for Grade field
    const gradeValue = typeof midtermGrade === 'number' ? `${midtermGrade}/100` : midtermGrade;

    const midtermSection = {
      id: Date.now(),
      title: 'Midterm',
      weight: weight.toString(),
      gradeCount: '1',
      assignedGrades: [{
        Grade: gradeValue,
        Name: 'Midterm Exam'
      }],
      isAutoGenerated: true
    };

    console.log('🚀 Creating midterm section with weight:', weight);
    console.log('🚀 Midterm grade value:', gradeValue);
    setCalculatorSections([...calculatorSections, midtermSection]);
  };

  const addNewCoursework = () => {
    // Reset editing flag for new coursework
    setIsEditingSection(false);
    // Reset current coursework data
    setCurrentCoursework({
      title: '',
      weight: '',
      gradeCount: '',
      assignedGrades: []
    });
    // Switch to add coursework mode
    setCalculatorMode('addCoursework');
  };

  const saveCoursework = () => {
    console.log('💾 Saving coursework...');
    console.log('💾 Current coursework:', currentCoursework);
    console.log('💾 Current sections before save:', calculatorSections);

    if (currentCoursework.id) {
      // Update existing section
      console.log('💾 Updating existing section with ID:', currentCoursework.id);
      setCalculatorSections(sections =>
        sections.map(section =>
          section.id === currentCoursework.id
            ? { ...currentCoursework, isAutoGenerated: section.isAutoGenerated } // Preserve isAutoGenerated
            : section
        )
      );
    } else {
      // Add new coursework section
      const newCoursework = {
        id: Date.now(),
        ...currentCoursework,
        isAutoGenerated: false
      };

      console.log('💾 New coursework to add:', newCoursework);
      setCalculatorSections([...calculatorSections, newCoursework]);
    }

    // Return to main mode
    setCalculatorMode('main');
  };

  const cancelCoursework = () => {
    // Reset current coursework data
    setCurrentCoursework({
      title: '',
      weight: '',
      gradeCount: '',
      assignedGrades: []
    });
    // Return to main mode without saving
    setCalculatorMode('main');
  };

  const updateCurrentCoursework = (field, value) => {
    setCurrentCoursework(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const getTotalWeight = () => {
    return calculatorSections.reduce((total, section) => {
      const weight = parseFloat(section.weight) || 0;
      return total + weight;
    }, 0);
  };

  const validateWeight = (weight) => {
    const numWeight = parseFloat(weight);
    if (isNaN(numWeight) || numWeight < 0 || numWeight > 100) {
      return false;
    }

    const currentTotal = getTotalWeight();
    return (currentTotal + numWeight) <= 100;
  };

  const getAvailableGrades = () => {
    // Get all grades that haven't been used in any section
    const usedGrades = calculatorSections.flatMap(section =>
      section.assignedGrades.map(g => g.Grade) // Fixed: use Grade instead of grade
    );

    console.log('🔍 Used grades in sections:', usedGrades);
    console.log('🔍 All grades data:', gradesData.map(g => g.Grade));

    return gradesData.filter(gradeItem =>
      gradeItem.Grade && !usedGrades.includes(gradeItem.Grade)
    );
  };

  const getAllGradesForCurrentCoursework = () => {
    // FIXED VERSION - Compare by both Grade AND Name to handle duplicate grade values
    // Exclude grades that are used in saved sections OR current coursework

    const usedInSavedSections = calculatorSections.flatMap(section =>
      section.assignedGrades.map(g => `${g.Name}|${g.Grade}`)
    );
    const currentlyUsedGradeKeys = currentCoursework.assignedGrades.map(g => `${g.Name}|${g.Grade}`);
    const allUsedGradeKeys = [...usedInSavedSections, ...currentlyUsedGradeKeys];

    console.log('🔍 ===== FIXED getAllGradesForCurrentCoursework =====');
    console.log('🔍 Calculator sections:', calculatorSections.length);
    console.log('🔍 Current coursework assigned grades:', currentCoursework.assignedGrades);
    console.log('🔍 Used in saved sections (Name|Grade):', usedInSavedSections);
    console.log('🔍 Currently used grade keys (Name|Grade):', currentlyUsedGradeKeys);
    console.log('🔍 All used grade keys:', allUsedGradeKeys);
    console.log('🔍 Total grades data available:', gradesData.length);

    const availableGrades = gradesData.filter(gradeItem => {
      const hasGrade = gradeItem.Grade;
      const gradeKey = `${gradeItem.Name}|${gradeItem.Grade}`;
      const isUsed = allUsedGradeKeys.includes(gradeKey);

      console.log(`🔍 Checking grade "${gradeItem.Name}" (${gradeItem.Grade}): gradeKey=${gradeKey}, isUsed=${isUsed}`);

      return hasGrade && !isUsed;
    });

    console.log('🔍 FIXED Available grades:', availableGrades.map(g => `${g.Name}: ${g.Grade}`));
    console.log('🔍 ===== End FIXED getAllGradesForCurrentCoursework =====');
    return availableGrades;
  };



  const assignGradeToCurrentCoursework = (gradeItem) => {
    const newGrade = {
      Grade: gradeItem.Grade,
      Name: gradeItem.Name || gradeItem.name || 'Unknown'
    };

    setCurrentCoursework(prev => ({
      ...prev,
      assignedGrades: [...prev.assignedGrades, newGrade]
    }));
  };

  // Automatic grade selection based on title keywords
  const getKeywordsForTitle = (title) => {
    const lowerTitle = title.toLowerCase();

    // Define keyword mappings for different coursework types
    const keywordMappings = {
      quiz: ['quiz', 'quizzes'],
      assignment: ['assignment', 'assignments', 'hw', 'homework'],
      lab: ['lab', 'labs', 'laboratory'],
      project: ['project', 'projects', 'milestone', 'milestones'],
      exam: ['exam', 'exams', 'test', 'tests'],
      midterm: ['midterm', 'mid-term', 'mid term'],
      final: ['final', 'finals'],
      presentation: ['presentation', 'presentations', 'demo', 'demos'],
      report: ['report', 'reports', 'paper', 'papers'],
      exercise: ['exercise', 'exercises', 'practice']
    };

    // Find matching keywords
    for (const [, keywords] of Object.entries(keywordMappings)) {
      if (keywords.some(keyword => lowerTitle.includes(keyword))) {
        return keywords;
      }
    }

    // If no specific match, return the title itself as keyword
    return [lowerTitle];
  };

  const autoSelectGrades = () => {
    const { title, gradeCount } = currentCoursework;

    if (!title.trim() || !gradeCount || parseInt(gradeCount) <= 0) {
      return; // Not enough info for auto-selection
    }

    const targetCount = parseInt(gradeCount);
    const keywords = getKeywordsForTitle(title);
    const availableGrades = getAllGradesForCurrentCoursework();

    // Filter grades that match the keywords
    const matchingGrades = availableGrades.filter(gradeItem => {
      const gradeName = (gradeItem.Name || '').toLowerCase();
      return keywords.some(keyword => gradeName.includes(keyword));
    });

    if (matchingGrades.length === 0) {
      return; // No matching grades found
    }

    // Sort matching grades by percentage (highest first)
    const sortedGrades = matchingGrades.sort((a, b) => {
      const percentageA = parseGrade(a.Grade).percentage;
      const percentageB = parseGrade(b.Grade).percentage;
      return percentageB - percentageA; // Descending order
    });

    // Select the top N grades
    const selectedGrades = sortedGrades.slice(0, targetCount);

    // Convert to the format expected by the form
    const formattedGrades = selectedGrades.map(gradeItem => ({
      Grade: gradeItem.Grade,
      Name: gradeItem.Name || gradeItem.name || 'Unknown'
    }));

    // Update the current coursework with auto-selected grades
    setCurrentCoursework(prev => ({
      ...prev,
      assignedGrades: formattedGrades
    }));
  };

  // Ghost Loading Component for Grades
  const GradeGhostCard = ({ index }) => {
    const shimmerAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
      const shimmerAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(shimmerAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(shimmerAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      shimmerAnimation.start();

      return () => shimmerAnimation.stop();
    }, []);

    const shimmerOpacity = shimmerAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0.3, 0.7],
    });

    return (
      <View style={styles.gradeCard}>
        {/* Left: Assignment Name Ghost */}
        <View style={styles.gradeNameContainer}>
          <Animated.View style={[
            styles.ghostText,
            styles.ghostGradeName,
            { opacity: shimmerOpacity }
          ]} />
        </View>

        {/* Middle: Staff Name Ghost */}
        <View style={styles.gradeStaffContainer}>
          <Animated.View style={[
            styles.ghostText,
            styles.ghostStaffName,
            { opacity: shimmerOpacity }
          ]} />
        </View>

        {/* Right: Circular Grade Ghost */}
        <View style={styles.gradeCircleContainer}>
          <Animated.View style={[
            styles.ghostCircle,
            { opacity: shimmerOpacity }
          ]} />
        </View>
      </View>
    );
  };

  // Function to create a unique identifier for a grade
  const createGradeId = (gradeItem) => {
    return `${gradeItem.Name}-${gradeItem.staff}-${gradeItem.Grade}`;
  };

  // Function to determine which grades should animate
  const determineGradesToAnimate = (newGrades) => {
    // If no previous grades exist, animate all (first load or after reset)
    if (previousGradesData.length === 0) {
      console.log('🎬 First load or reset - all grades will animate');
      return new Set(newGrades.map((_, index) => index));
    }

    // Compare with previous grades to find new/changed ones
    const previousGradeIds = new Set(previousGradesData.map(createGradeId));
    const gradesToAnimate = new Set();

    newGrades.forEach((gradeItem, index) => {
      const gradeId = createGradeId(gradeItem);
      if (!previousGradeIds.has(gradeId)) {
        // This is a new or changed grade
        gradesToAnimate.add(index);
        console.log('🆕 New/changed grade detected:', gradeItem.Name, gradeItem.Grade);
      }
    });

    console.log(`🎬 Animation decision: ${gradesToAnimate.size}/${newGrades.length} grades will animate`);
    return gradesToAnimate;
  };

  // Update grades data with animation control
  const updateGradesData = (newGrades) => {
    const gradesToAnimate = determineGradesToAnimate(newGrades);

    // Store the animation decision in ref for immediate access
    animationDecisionRef.current = gradesToAnimate;

    // Update the grades data
    setGradesData(newGrades);

    // Store current grades for future comparison
    setPreviousGradesData([...newGrades]);

    // Store which grades should animate
    setAnimatedGrades(gradesToAnimate);
  };

  // Calculator modal animation functions
  const openCalculatorModal = () => {
    setIsCalculatorVisible(true);

    // Reset animation values
    calculatorModalScale.setValue(0.8);
    calculatorModalOpacity.setValue(0);
    calculatorOverlayOpacity.setValue(0);

    // Animate in with spring effect
    Animated.parallel([
      Animated.spring(calculatorModalScale, {
        toValue: 1,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
      Animated.timing(calculatorModalOpacity, {
        toValue: 1,
        duration: 300,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(calculatorOverlayOpacity, {
        toValue: 1,
        duration: 300,
        easing: Easing.out(Easing.quad),
        useNativeDriver: true,
      }),
    ]).start();
  };

  const closeCalculatorModal = () => {
    // Animate out
    Animated.parallel([
      Animated.timing(calculatorModalScale, {
        toValue: 0.8,
        duration: 200,
        easing: Easing.in(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(calculatorModalOpacity, {
        toValue: 0,
        duration: 200,
        easing: Easing.in(Easing.quad),
        useNativeDriver: true,
      }),
      Animated.timing(calculatorOverlayOpacity, {
        toValue: 0,
        duration: 200,
        easing: Easing.in(Easing.quad),
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsCalculatorVisible(false);
    });
  };

  // Clear calculator sections when course changes
  useEffect(() => {
    console.log('🔄 Course changed to:', selectedCourse?.main);
    console.log('🔄 Previous calculator sections:', calculatorSections);
    console.log('🔄 Clearing calculator sections and closing modal');

    // Close calculator modal if open
    if (isCalculatorVisible) {
      closeCalculatorModal();
    }

    // Clear all calculator state
    setCalculatorSections([]);
    setCalculatorMode('main');
    setIsEditingSection(false);
    setCurrentCoursework({
      title: '',
      weight: '',
      gradeCount: '',
      assignedGrades: []
    });
    console.log('🔄 Calculator state cleared');
  }, [selectedCourse?.main]);

  // Auto-update trigger when assigned grades change
  useEffect(() => {
    console.log('🔄 ===== TRIGGER UPDATE EFFECT =====');
    console.log('🔄 Assigned grades changed:', currentCoursework.assignedGrades);
    console.log('🔄 Previous trigger value:', gradeListUpdateTrigger);

    // Force a re-render by updating the trigger
    const newTrigger = Date.now(); // Use timestamp for unique values
    setGradeListUpdateTrigger(newTrigger);
    console.log('🔄 Updated trigger to:', newTrigger);
    console.log('🔄 ===== END TRIGGER UPDATE EFFECT =====');
  }, [currentCoursework.assignedGrades]);

  // Initialize calculator when modal becomes visible
  useEffect(() => {
    if (isCalculatorVisible && calculatorMode === 'main') {
      // Small delay to ensure modal is fully rendered
      const timer = safeSetTimeout(() => {
        initializeCalculator();
      }, 300); // Increased delay
      return () => clearTimeout(timer);
    }
  }, [isCalculatorVisible, calculatorMode, midtermGrade]); // Added midtermGrade dependency



  // Save calculator sections to cache whenever they change (but not during course switching)
  useEffect(() => {
    if (selectedCourse?.main &&
        selectedCourse.main !== 'Select Course' &&
        isCalculatorVisible &&
        calculatorSections.length > 0 &&
        calculatorMode === 'main') { // Only save when in main mode (not during transitions)
      const courseKey = selectedCourse.main;
      console.log('💾 Saving calculator cache for course:', courseKey, calculatorSections);
      saveCalculatorCache(courseKey, calculatorSections);
    }
  }, [calculatorSections, selectedCourse?.main, isCalculatorVisible, calculatorMode]);

  // Auto-select grades when title and grade count are filled (only for new coursework)
  useEffect(() => {
    if (calculatorMode === 'addCoursework' &&
        !isEditingSection && // Don't autofill when editing existing section
        currentCoursework.title.trim() &&
        currentCoursework.gradeCount &&
        parseInt(currentCoursework.gradeCount) > 0 &&
        currentCoursework.assignedGrades.length === 0) { // Only autofill if no grades assigned yet
      // Small delay to ensure state is updated
      const timer = safeSetTimeout(() => {
        autoSelectGrades();
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [currentCoursework.title, currentCoursework.gradeCount, calculatorMode, isEditingSection]);

  const removeGradeFromCurrentCoursework = (gradeIndex) => {
    console.log('🗑️ ===== REMOVING GRADE =====');
    console.log('🗑️ Removing grade at index:', gradeIndex);
    console.log('🗑️ Current assigned grades before removal:', currentCoursework.assignedGrades);
    console.log('🗑️ Current trigger value before removal:', gradeListUpdateTrigger);

    setCurrentCoursework(prev => {
      const gradeToRemove = prev.assignedGrades[gradeIndex];
      const newAssignedGrades = [...prev.assignedGrades]; // Create a new array
      newAssignedGrades.splice(gradeIndex, 1); // Remove the specific grade

      console.log('🗑️ Removed grade:', gradeToRemove);
      console.log('🗑️ Grades after removal:', newAssignedGrades);
      console.log('🗑️ Available grades should now include:', gradeToRemove?.Grade);

      const newCoursework = {
        ...prev,
        assignedGrades: newAssignedGrades
      };

      console.log('🗑️ New coursework state:', newCoursework);
      console.log('🗑️ ===== END REMOVING GRADE =====');
      return newCoursework;
    });

    // Additional force update
    safeSetTimeout(() => {
      safeSetState(setForceUpdateCounter, prev => prev + 1, 'forceUpdateCounter');
    }, 10);
  };

  const updateSection = (sectionId, field, value) => {
    setCalculatorSections(sections =>
      sections.map(section =>
        section.id === sectionId
          ? { ...section, [field]: value }
          : section
      )
    );
  };

  const removeSection = (sectionId) => {
    setCalculatorSections(sections =>
      sections.filter(section => section.id !== sectionId)
    );
  };

  const editSection = (section) => {
    console.log('✏️ Editing section:', section);
    // Set editing flag to prevent autofill
    setIsEditingSection(true);
    // Load the section data into current coursework for editing
    setCurrentCoursework({
      id: section.id, // Keep the ID for updating
      title: section.title || '',
      weight: section.weight || '',
      gradeCount: section.gradeCount || '',
      assignedGrades: [...section.assignedGrades], // Copy the grades
      isAutoGenerated: section.isAutoGenerated || false // Track if it's midterm
    });
    // Switch to edit mode
    setCalculatorMode('addCoursework');
  };

  // Calculate percentage lost for a coursework section
  const calculatePercentageLost = (section) => {
    if (!section.weight) {
      return 0;
    }

    const expectedGradeCount = parseInt(section.gradeCount) || 1;
    const assignedGradeCount = section.assignedGrades ? section.assignedGrades.length : 0;

    // Calculate total percentage including missing grades as full marks
    let totalPercentage = 0;

    // Add assigned grades
    if (section.assignedGrades) {
      section.assignedGrades.forEach(gradeItem => {
        const parsedGrade = parseGrade(gradeItem.Grade);
        const gradePercentage = parsedGrade.percentage;
        console.log('🔍 Grade item:', gradeItem.Grade);
        console.log('🔍 Parsed grade:', parsedGrade);
        console.log('🔍 Grade percentage:', gradePercentage);
        if (!isNaN(gradePercentage)) {
          totalPercentage += gradePercentage / 100; // Convert to decimal (e.g., 95% -> 0.95)
        }
      });
    }

    // Add missing grades as full marks (100%)
    const missingGradeCount = expectedGradeCount - assignedGradeCount;
    if (missingGradeCount > 0) {
      totalPercentage += missingGradeCount * 1.0; // 1.0 = 100%
      console.log('🔍 Missing grades treated as full marks:', missingGradeCount);
    }

    const averagePercentage = totalPercentage / expectedGradeCount; // Average as decimal
    const sectionWeight = parseFloat(section.weight) || 0;
    const percentageLost = (1 - averagePercentage) * sectionWeight; // How much % lost in this section

    console.log('🔍 Expected grades:', expectedGradeCount);
    console.log('🔍 Assigned grades:', assignedGradeCount);
    console.log('🔍 Missing grades:', missingGradeCount);
    console.log('🔍 Total percentage:', totalPercentage);
    console.log('🔍 Average percentage:', averagePercentage);
    console.log('🔍 Percentage lost:', percentageLost);

    return percentageLost;
  };

  // Calculate total percentage lost across all sections
  const calculateTotalPercentageLost = () => {
    return calculatorSections.reduce((total, section) => {
      return total + calculatePercentageLost(section);
    }, 0);
  };

  // Get letter grade from percentage
  const getLetterGrade = (percentage) => {
    if (percentage >= 94) return 'A+';
    if (percentage >= 90) return 'A';
    if (percentage >= 86) return 'A-';
    if (percentage >= 82) return 'B+';
    if (percentage >= 78) return 'B';
    if (percentage >= 74) return 'B-';
    if (percentage >= 70) return 'C+';
    if (percentage >= 65) return 'C';
    if (percentage >= 60) return 'C-';
    if (percentage >= 55) return 'D+';
    if (percentage >= 50) return 'D';
    return 'F';
  };

  // Calculate highest achievable grade
  const getHighestAchievableGrade = () => {
    const totalLost = calculateTotalPercentageLost();
    const highestPossible = 100 - totalLost;
    return {
      percentage: Math.max(0, highestPossible),
      letter: getLetterGrade(Math.max(0, highestPossible))
    };
  };

  // Get color for percentage display
  const getPercentageColor = (percentage) => {
    if (Math.abs(percentage) < 0.05) { // -0.0% (considering floating point precision)
      return theme.colors.success; // Green
    }
    return theme.colors.error; // Red
  };

  // Format percentage display
  const formatPercentage = (percentage) => {
    if (percentage > 0) {
      return `-${percentage.toFixed(1)}%`; // Lost points should show as negative
    } else if (percentage < 0) {
      return `+${Math.abs(percentage).toFixed(1)}%`; // Gained points should show as positive
    } else {
      return `-${percentage.toFixed(1)}%`; // Zero should show as -0.0%
    }
  };

  const removeGradeFromSection = (sectionId, gradeToRemove) => {
    setCalculatorSections(sections =>
      sections.map(section =>
        section.id === sectionId
          ? {
              ...section,
              assignedGrades: section.assignedGrades.filter(g => g.Grade !== gradeToRemove)
            }
          : section
      )
    );
  };

  const assignGradeToSection = (sectionId, gradeItem) => {
    const newGrade = {
      Grade: gradeItem.Grade,
      Name: gradeItem.Name || 'Unknown'
    };

    setCalculatorSections(sections =>
      sections.map(section =>
        section.id === sectionId
          ? {
              ...section,
              assignedGrades: [...section.assignedGrades, newGrade]
            }
          : section
      )
    );
  };



  // Process all grades data to fix multi-line formats
  const processGradesData = (gradesArray) => {
    if (!Array.isArray(gradesArray)) {
      return gradesArray;
    }

    return gradesArray.map(gradeItem => {
      if (gradeItem && gradeItem.Grade) {
        return {
          ...gradeItem,
          Grade: fixGradeFormat(gradeItem.Grade)
        };
      }
      return gradeItem;
    });
  };

  // Fix multi-line grade format (e.g., "5.25\n/\n5.25" -> "5.25 / 5.25")
  const fixGradeFormat = (gradeString) => {
    if (!gradeString || typeof gradeString !== 'string') {
      return gradeString;
    }

    // Remove extra whitespace and normalize line breaks
    let cleaned = gradeString.trim().replace(/\r\n/g, '\n').replace(/\r/g, '\n');

    // Check if it matches the multi-line fraction pattern
    // Pattern: number, newline(s), /, newline(s), number
    const multiLineFractionPattern = /^(\d+(?:\.\d+)?)\s*\n+\s*\/\s*\n+\s*(\d+(?:\.\d+)?)$/;
    const match = cleaned.match(multiLineFractionPattern);

    if (match) {
      const numerator = match[1];
      const denominator = match[2];
      const fixedFormat = numerator + ' / ' + denominator;
      return fixedFormat;
    }

    // Also handle cases with extra spaces around the slash
    const spacedFractionPattern = /^(\d+(?:\.\d+)?)\s*\n+\s*\/\s*\n+\s*(\d+(?:\.\d+)?)$/;
    const spacedMatch = cleaned.match(spacedFractionPattern);

    if (spacedMatch) {
      const numerator = spacedMatch[1];
      const denominator = spacedMatch[2];
      const fixedFormat = numerator + ' / ' + denominator;
      return fixedFormat;
    }

    // Handle unset grades with multi-line format (e.g., "/\n10" -> "/10")
    const unsetMultiLinePattern = /^\s*\/\s*\n+\s*(\d+(?:\.\d+)?)\s*$/;
    const unsetMatch = cleaned.match(unsetMultiLinePattern);

    if (unsetMatch) {
      const denominator = unsetMatch[1];
      const fixedFormat = '/ ' + denominator;
      return fixedFormat;
    }

    // Return original if no multi-line pattern found
    return gradeString;
  };

  // Parse grade string and calculate percentage
  const parseGrade = (gradeString) => {
    // First fix the format if it's multi-line
    const fixedGradeString = fixGradeFormat(gradeString);

    if (!fixedGradeString || fixedGradeString.trim() === '') {
      return { score: 0, total: 100, percentage: 0, display: 'N/A' };
    }

    // Handle different grade formats: "15/20", "85%", "A+", "/10" (unset grade), etc.
    const fractionMatch = fixedGradeString.match(/(\d+(?:\.\d+)?)\s*\/\s*(\d+(?:\.\d+)?)/);
    const percentageMatch = fixedGradeString.match(/(\d+(?:\.\d+)?)%/);
    const unsetGradeMatch = fixedGradeString.match(/^\s*\/\s*(\d+(?:\.\d+)?)\s*$/); // Match "/10" format

    if (fractionMatch) {
      const score = parseFloat(fractionMatch[1]);
      const total = parseFloat(fractionMatch[2]);
      const percentage = (score / total) * 100;
      const result = { score, total, percentage, display: fixedGradeString.trim() };
      return result;
    } else if (unsetGradeMatch) {
      // Handle unset grades like "/10" - treat as 0 score but keep original display with space
      const total = parseFloat(unsetGradeMatch[1]);
      const result = { score: 0, total, percentage: 0, display: `/ ${total}` };
      return result;
    } else if (percentageMatch) {
      const percentage = parseFloat(percentageMatch[1]);
      const result = { score: percentage, total: 100, percentage, display: fixedGradeString.trim() };
      return result;
    } else {
      // Handle letter grades or other formats - might be just a number
      const numberMatch = fixedGradeString.match(/^(\d+(?:\.\d+)?)$/);
      if (numberMatch) {
        // If it's just a number, assume it's out of 10 (common for labs)
        const score = parseFloat(numberMatch[1]);
        const total = 10; // Assume out of 10 for labs
        const percentage = (score / total) * 100;
        const result = { score, total, percentage, display: score + '/' + total };
        return result;
      } else {
        const result = { score: 0, total: 100, percentage: 0, display: fixedGradeString.trim() };
        return result;
      }
    }
  };

  // Get color based on grade percentage using 10-step gradient (Green to Red)
  const getGradeColor = (percentage) => {
    // For pink/colorful theme, ALL grades are turquoise regardless of value
    if (safeCurrentThemeName === 'colorful') {
      return '#00CED1'; // Dark Turquoise for all grades in pink mode
    }

    // For navy theme, use elegant red gradient that complements the navy theme
    if (safeCurrentThemeName === 'navy') {
      // Navy theme uses a sophisticated red-to-gold gradient
      const navyGradientColors = [
        '#DC2626', // 0-9%: Deep Red (matches theme primary)
        '#EF4444', // 10-19%: Red
        '#F87171', // 20-29%: Light Red
        '#FB923C', // 30-39%: Red-Orange
        '#F97316', // 40-49%: Orange
        '#EA580C', // 50-59%: Dark Orange
        '#D97706', // 60-69%: Amber (matches theme warning)
        '#EAB308', // 70-79%: Gold
        '#059669', // 80-89%: Emerald (matches theme success)
        '#10B981', // 90-100%: Emerald Green
      ];

      const colorIndex = Math.min(Math.floor(percentage / 10), 9);
      return navyGradientColors[colorIndex];
    }

    // 10-step gradient colors from green to red (every 10%) - NO BLUE TINTS
    // Pure green-to-red spectrum matching the provided gradient
    const gradientColors = [
      '#DC2626', // 0-9%: Deep Red
      '#EF4444', // 10-19%: Red
      '#F87171', // 20-29%: Light Red
      '#FB923C', // 30-39%: Red-Orange
      '#F97316', // 40-49%: Orange
      '#EA580C', // 50-59%: Dark Orange
      '#D97706', // 60-69%: Orange-Yellow
      '#EAB308', // 70-79%: Yellow
      '#84CC16', // 80-89%: Lime Green (pure green, no blue)
      '#22C55E', // 90-100%: Green (pure green, no blue)
    ];

    // Determine color based on percentage (every 10%)
    const colorIndex = Math.min(Math.floor(percentage / 10), 9);
    return gradientColors[colorIndex];
  };

  // Get color for animation with smooth interpolation between gradient steps
  const getAnimatedColor = (animatedPercentage) => {
    // For pink/colorful theme, always return turquoise
    if (safeCurrentThemeName === 'colorful') {
      return '#00CED1';
    }

    // For navy theme, use the same sophisticated gradient as static colors
    if (safeCurrentThemeName === 'navy') {
      const navyGradientColors = [
        '#DC2626', // 0-9%: Deep Red (matches theme primary)
        '#EF4444', // 10-19%: Red
        '#F87171', // 20-29%: Light Red
        '#FB923C', // 30-39%: Red-Orange
        '#F97316', // 40-49%: Orange
        '#EA580C', // 50-59%: Dark Orange
        '#D97706', // 60-69%: Amber (matches theme warning)
        '#EAB308', // 70-79%: Gold
        '#059669', // 80-89%: Emerald (matches theme success)
        '#10B981', // 90-100%: Emerald Green
      ];

      const colorIndex = Math.min(Math.floor(animatedPercentage / 10), 9);
      return navyGradientColors[colorIndex];
    }

    // Same 10-step gradient for smooth animation - NO BLUE TINTS
    const gradientColors = [
      '#DC2626', // 0-9%: Deep Red
      '#EF4444', // 10-19%: Red
      '#F87171', // 20-29%: Light Red
      '#FB923C', // 30-39%: Red-Orange
      '#F97316', // 40-49%: Orange
      '#EA580C', // 50-59%: Dark Orange
      '#D97706', // 60-69%: Orange-Yellow
      '#EAB308', // 70-79%: Yellow
      '#84CC16', // 80-89%: Lime Green (pure green, no blue)
      '#22C55E', // 90-100%: Green (pure green, no blue)
    ];

    // For smoother animation, interpolate between adjacent colors
    const exactIndex = animatedPercentage / 10;
    const lowerIndex = Math.floor(exactIndex);
    const upperIndex = Math.min(lowerIndex + 1, gradientColors.length - 1);

    // If we're at exact boundaries or at the end, return the exact color
    if (lowerIndex === upperIndex || animatedPercentage >= 100) {
      return gradientColors[Math.min(lowerIndex, gradientColors.length - 1)];
    }

    // For now, return the lower color (we can add interpolation later if needed)
    return gradientColors[lowerIndex];
  };

  // Extract first and last name from staff name
  const formatStaffName = (fullName) => {
    if (!fullName || fullName.trim() === '') return '';

    const nameParts = fullName.trim().split(' ').filter(part => part.length > 0);
    if (nameParts.length === 1) {
      return nameParts[0];
    } else if (nameParts.length >= 2) {
      return `${nameParts[0]} ${nameParts[nameParts.length - 1]}`;
    }
    return fullName;
  };

  // Animated Circular Progress Component
  const CircularProgress = ({ grade, index = 0 }) => {
    const { percentage, display } = parseGrade(grade);

    // Create unique identifier for this grade to check completion status
    const gradeId = createGradeId({ Name: grade.split(' / ')[0] || 'Unknown', staff: '', Grade: grade });

    // Initialize animation value based on completion status (immediate access via ref)
    const initialValue = completedAnimationsRef.current.has(gradeId) ? percentage : 0;
    const animatedValue = useRef(new Animated.Value(initialValue)).current;
    const [currentAnimatedPercentage, setCurrentAnimatedPercentage] = useState(initialValue);

    const size = Math.max(65, Math.min(70, Dimensions.get('window').width * 0.16));
    const strokeWidth = Math.max(4, size * 0.08);
    const radius = (size - strokeWidth) / 2;
    const circumference = radius * 2 * Math.PI;

    // Background circle color
    const backgroundStroke = safeCurrentThemeName === 'colorful' ? '#8B5A8C' : theme.colors.border;

    // Start animation when component mounts
    useEffect(() => {
      // Check if this animation has already been completed
      if (completedAnimationsRef.current.has(gradeId)) {
        // Animation already completed - ensure values are set correctly
        if (animatedValue._value !== percentage) {
          animatedValue.setValue(percentage);
        }
        if (currentAnimatedPercentage !== percentage) {
          setCurrentAnimatedPercentage(percentage);
        }
        console.log(`✅ Animation already completed for grade ${index}: ${grade}`);
        return;
      }

      // Use ref for immediate access to latest animation decision
      const currentAnimationDecision = animationDecisionRef.current || animatedGrades;

      // Check if this specific grade index should animate
      if (!currentAnimationDecision.has(index)) {
        // Skip animation - set final values immediately
        animatedValue.setValue(percentage);
        setCurrentAnimatedPercentage(percentage);
        console.log(`⏭️ Skipping animation for grade ${index}: ${grade} (unchanged)`);
        return;
      }

      console.log(`🎬 Starting animation for grade ${index}: ${grade} (new/changed)`);

      // Reset animation value
      animatedValue.setValue(0);
      setCurrentAnimatedPercentage(0);

      // Add listener to track animated value
      const listener = animatedValue.addListener(({ value }) => {
        setCurrentAnimatedPercentage(value);
      });

      // Start animation with staggered delay based on index
      const staggerDelay = 500 + (index * 200); // Base delay of 500ms + 200ms per item
      const timer = safeSetTimeout(() => {
        const animation = Animated.timing(animatedValue, {
          toValue: percentage,
          duration: 2000, // 2 second animation
          easing: Easing.out(Easing.cubic),
          useNativeDriver: false, // We need to access the value
        });

        // Track this animation for cleanup
        gradeAnimationRefs.current.set(gradeId, {
          animatedValue,
          listener,
          animation
        });

        animation.start(() => {
          // Mark this animation as completed
          completedAnimationsRef.current = new Set([...completedAnimationsRef.current, gradeId]);
          console.log(`✅ Animation completed for grade ${index}: ${grade}`);
          // Remove from tracking
          gradeAnimationRefs.current.delete(gradeId);
        });
      }, staggerDelay);

      return () => {
        clearTimeout(timer);
        animatedValue.removeListener(listener);
        // Remove from tracking
        gradeAnimationRefs.current.delete(gradeId);
      };
    }, [percentage, index, animatedGrades, grade, gradeId]);

    // Calculate current stroke dash offset based on animated value
    const animatedStrokeDashoffset = circumference - (currentAnimatedPercentage / 100) * circumference;

    // Get current color based on animated percentage
    const currentColor = getAnimatedColor(currentAnimatedPercentage);

    return (
      <View style={styles.circularProgressContainer}>
        <Svg width={size} height={size} style={styles.svgStyle}>
          {/* Background circle */}
          <Circle
            cx={size / 2}
            cy={size / 2}
            r={radius}
            stroke={backgroundStroke}
            strokeWidth={strokeWidth}
            fill="none"
          />
          {/* Animated progress circle */}
          {currentAnimatedPercentage > 0 && (
            <Circle
              cx={size / 2}
              cy={size / 2}
              r={radius}
              stroke={currentColor}
              strokeWidth={strokeWidth}
              fill="none"
              strokeDasharray={circumference}
              strokeDashoffset={animatedStrokeDashoffset}
              strokeLinecap="round"
              transform={`rotate(-90 ${size / 2} ${size / 2})`}
            />
          )}
        </Svg>

        {/* Grade text in center - animated color */}
        <View style={styles.gradeTextContainer}>
          <Text
            style={[styles.gradeText, { color: currentColor }]}
            numberOfLines={2}
            adjustsFontSizeToFit={true}
            minimumFontScale={0.7}
          >
            {display}
          </Text>
        </View>
      </View>
    );
  };

  // Function to clear all grade caches (both demo and real)
  const clearAllGradeCaches = async () => {
    try {
      console.log('🧹 Clearing all grade caches...');
      const keys = await AsyncStorage.getAllKeys();
      const gradeCacheKeys = keys.filter(key =>
        key.startsWith('grades_cache_') ||
        key.startsWith('demo_grades_cache_') ||
        key.startsWith('grades_cache_timestamp_') ||
        key.startsWith('demo_grades_cache_timestamp_')
      );

      if (gradeCacheKeys.length > 0) {
        await AsyncStorage.multiRemove(gradeCacheKeys);
        console.log(`🧹 Cleared ${gradeCacheKeys.length} grade cache entries`);
      }
    } catch (error) {
      console.log('❌ Error clearing grade caches:', error);
    }
  };

  const loadCredentialsAndSetupGrades = async () => {
    try {
      console.log('🔄 Loading credentials for grades...');

      // Wait a moment to ensure account mode initialization is complete
      await new Promise(resolve => setTimeout(resolve, 100));

      // Clear all caches to prevent demo/real data mixing
      await clearAllGradeCaches();

      // Check if we're in demo mode
      const isDemoMode = await checkDemoMode();
      if (isDemoMode) {
        console.log('🎭 Demo mode detected - loading demo grades data');

        // Set demo dropdown options
        setDropdownOptions(getDemoGradesDropdown());
        setIsDropdownLoading(false);
        setIsLoading(false);

        // Set a dummy URL to satisfy the loading condition
        setGradesUrl('demo://grades');

        console.log('🎭 Demo grades setup complete');
        return;
      }

      // Check if we're in offline mode
      if (isOfflineMode) {
        console.log('📴 Offline mode detected - loading cached grades courses');

        try {
          const cachedCourses = await getCachedGradesCourses();
          console.log('📦 Cached grades courses:', cachedCourses);

          if (cachedCourses.length > 0) {
            // Convert cached course codes to dropdown format
            const offlineDropdownOptions = cachedCourses.map(courseCode => ({
              value: courseCode,
              text: courseCode // We'll format this properly when displaying
            }));

            setDropdownOptions(offlineDropdownOptions);
            setIsDropdownLoading(false);
            setIsLoading(false);
            setGradesUrl('offline://grades');

            console.log('📴 Offline grades setup complete');
            return;
          } else {
            console.log('📭 No cached grades found in offline mode');
            setDropdownOptions([]);
            setIsDropdownLoading(false);
            setIsLoading(false);
            return;
          }
        } catch (error) {
          console.log('❌ Error loading cached grades courses:', error);
          setDropdownOptions([]);
          setIsDropdownLoading(false);
          setIsLoading(false);
          return;
        }
      }

      // Clear WebView session before setting up grades
      await clearWebViewSession();

      // Get stored credentials from localStorage
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('❌ No stored credentials found');
        Alert.alert(
          'Authentication Required',
          'Please login first to access grades.',
          [
            {
              text: 'Go to Login',
              onPress: () => navigation.navigate('Login')
            }
          ]
        );
        return;
      }

      console.log('✅ Credentials loaded, setting up grades URL...');
      setCredentials({ username: storedUsername, password: storedPassword });

      // Create URL with embedded credentials - properly encode username and password
      const encodedUsername = encodeURIComponent(storedUsername);
      const encodedPassword = encodeURIComponent(storedPassword);
      const urlWithCredentials = `https://${encodedUsername}:${encodedPassword}@apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx`;
      setGradesUrl(urlWithCredentials);

      console.log('🎯 Grades URL prepared');
      setIsLoading(false);
      
    } catch (error) {
      console.error('❌ Error loading credentials:', error);
      Alert.alert(
        'Error',
        'Failed to load credentials. Please try again.',
        [
          {
            text: 'Go Back',
            onPress: () => navigation.goBack()
          }
        ]
      );
    }
  };

  const handleWebViewLoad = () => {
    console.log('📚 Grades page loaded successfully');

    // Add a timeout fallback in case extraction fails
    safeSetTimeout(() => {
      if (isDropdownLoading) {
        console.log('⏰ Dropdown loading timeout, stopping loading state');
        safeSetState(setIsDropdownLoading, false, 'isDropdownLoading');
      }
    }, 10000); // 10 second timeout

    // Add a delay to ensure the page is fully loaded
    safeSetTimeout(() => {
      // Inject JavaScript to extract dropdown options
      const jsCode = `
        (function() {
          try {

            // Log all select elements on the page
            const allSelects = document.querySelectorAll('select');

            const dropdown = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_smCrsLst');
            if (dropdown) {
              const options = [];
              for (let i = 0; i < dropdown.options.length; i++) {
                const option = dropdown.options[i];
                options.push({
                  value: option.value,
                  text: option.text
                });
              }
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'dropdown_options',
                options: options
              }));
            } else {
              console.log('❌ Dropdown not found');
              // Try to find it by other means
              const allElements = document.querySelectorAll('[id*="smCrsLst"]');
              console.log('Found', allElements.length, 'elements with smCrsLst in ID');
              allElements.forEach((el, index) => {
                console.log('Element', index, 'ID:', el.id, 'Tag:', el.tagName);
              });

              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'dropdown_error',
                error: 'Dropdown element not found'
              }));
            }
          } catch (error) {
            console.log('❌ Error extracting dropdown:', error);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'dropdown_error',
              error: error.message
            }));
          }
        })();
      `;

      if (webViewRef.current) {
        webViewRef.current.injectJavaScript(jsCode);
      }
    }, 2000); // Wait 2 seconds for the page to fully load
  };

  const handleWebViewMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      //console.log('📨 WebView message received:', data);

      if (data.type === 'dropdown_options') {
        //console.log('📋 Dropdown options extracted:', data.options);
        // Filter out empty options and format the course names
        const filteredOptions = data.options.filter(option =>
          option.value && option.text && option.text.trim() !== ''
        );
        setDropdownOptions(filteredOptions);
        setIsDropdownLoading(false); // Dropdown is now ready
      } else if (data.type === 'dropdown_error') {
        console.log('❌ Dropdown extraction error:', data.error);
        setIsDropdownLoading(false); // Stop loading even on error
      } else if (data.type === 'phase1_complete') {
        console.log('✅ Phase 1 complete:', data.message);
        console.log('⏳ Waiting 2 seconds before Phase 2...');
      } else if (data.type === 'table_data_extracted') {
        console.log('🎯 Table data extracted from nttTr div!');
        console.log('� Number of rows:', data.rows.length);
        //console.log('� Table rows:', data.rows);
        //console.log('📄 Table HTML:', data.tableHTML);
      } else if (data.type === 'grades_data_extracted') {
        console.log('🎯 Grades data extracted!');
        console.log('📊 Total grades found:', data.totalGrades);
        console.log('📊 Raw grades data:', data.gradesData);

        // Process grades data to fix multi-line formats
        const processedGrades = processGradesData(data.gradesData);
        //console.log('📊 Processed grades data:', processedGrades);
        updateGradesData(processedGrades);
        setIsLoadingGrades(false);
        setIsUpdatingGrades(false); // Stop update indicator
        stopRotationAnimation();

        // Also log midDg data if present
        if (data.midDgData && data.midDgData.length > 0) {
          console.log('🎯 MidDg data also extracted!');
          console.log('📊 Total midDg rows found:', data.totalMidDgRows);
          console.log('📊 MidDg array data:');
          console.log(data.midDgData);

          // Search for the current course code in midDg data
          const currentCourseCode = selectedCourse.code; // e.g., "CSEN602"
          console.log('🔍 Searching for course code:', currentCourseCode);

          if (currentCourseCode) {
            const matchingRows = data.midDgData.filter(row => {
              return row.some(cell => cell.includes(currentCourseCode));
            });

            console.log('🎯 Found', matchingRows.length, 'rows containing', currentCourseCode);
            matchingRows.forEach((row, index) => {
              console.log(`📊 Matching row ${index + 1}:`, row);
            });

            // Look for percentage data in matching rows
            let foundMidtermGrade = null;
            matchingRows.forEach((row, index) => {
              row.forEach((cell, cellIndex) => {
                if (cell.includes('%') || cell.match(/\d+\.\d+/) || cell.match(/\d+\/\d+/)) {
                  console.log(`🎯 Found potential percentage in row ${index + 1}, cell ${cellIndex + 1}:`, cell);

                  // Extract numeric value for midterm grade
                  if (cell.includes('%')) {
                    const percentMatch = cell.match(/(\d+(?:\.\d+)?)%/);
                    if (percentMatch) {
                      foundMidtermGrade = percentMatch[1];
                      console.log(`🎯 Extracted midterm percentage: ${foundMidtermGrade}%`);
                    }
                  } else if (cell.match(/^\d+(?:\.\d+)?$/)) {
                    // Pure number, assume it's a percentage
                    foundMidtermGrade = cell;
                    console.log(`🎯 Extracted midterm grade: ${foundMidtermGrade}%`);
                  }
                }
              });
            });

            // Set the midterm grade state
            if (foundMidtermGrade) {
              // Round to nearest hundredth
              const roundedGrade = Math.round(parseFloat(foundMidtermGrade) * 100) / 100;
              setMidtermGrade(roundedGrade);
              console.log(`✅ Set midterm grade to: ${roundedGrade}%`);

              // Save to cache when both grades and midterm data are processed
              saveGradesToCache(selectedCourse.code, processedGrades, roundedGrade);
            } else {
              setMidtermGrade(null);
              console.log('❌ No midterm grade found, clearing state');

              // Save to cache even without midterm grade
              saveGradesToCache(selectedCourse.code, processedGrades, null);
            }
          } else {
            console.log('❌ No course code available to search for');
            // Save to cache without midterm grade
            saveGradesToCache(selectedCourse.code, processedGrades, null);
          }
        } else {
          console.log('❌ No midDg data found in response');
          // Save to cache without midterm grade
          saveGradesToCache(selectedCourse.code, processedGrades, null);
        }
      } else if (data.type === 'course_result_error') {
        console.log('❌ Course result error:', data.error);
        setIsLoadingGrades(false);
        setIsUpdatingGrades(false); // Stop update indicator on error
        stopRotationAnimation();
      } else if (data.type === 'table_extraction_timeout') {
        console.log('⏰ Table extraction timed out:', data.error);
        setIsUpdatingGrades(false); // Stop update indicator on timeout
      }
    } catch (error) {
      console.log('❌ Error processing WebView message:', error);
    }
  };

  const handleWebViewError = (syntheticEvent) => {
    const { nativeEvent } = syntheticEvent;
    console.error('❌ Grades WebView error:', nativeEvent);
    
    Alert.alert(
      'Loading Error',
      'Failed to load grades page. Please check your connection and try again.',
      [
        {
          text: 'Retry',
          onPress: () => {
            setIsLoading(true);
            loadCredentialsAndSetupGrades();
          }
        },
        {
          text: 'Go Back',
          onPress: () => navigation.goBack()
        }
      ]
    );
  };



  return (
    <>
      {/* Weight Input Modal - Render at root level to avoid interference */}
      {console.log('🎯 Rendering modal with showWeightInput:', showWeightInput)}
      {showWeightInput && (
        <Modal
          visible={true}
          transparent={true}
          animationType="slide"
          onRequestClose={handleWeightCancel}
          presentationStyle="overFullScreen"
          statusBarTranslucent={true}
        >
          <View style={{
            flex: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            justifyContent: 'center',
            alignItems: 'center',
            padding: 20,
          }}>
            <View style={[styles.weightInputContainer, {
              backgroundColor: theme.colors.surface,
              borderRadius: 20,
              padding: 25,
              width: '90%',
              maxWidth: 400,
              borderWidth: 2,
              borderColor: '#EAB308',
              shadowColor: '#000',
              shadowOffset: { width: 0, height: 2 },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              elevation: 5,
            }]}>
              <Text style={styles.weightInputTitle}>Midterm Weight</Text>
              <Text style={styles.weightInputSubtitle}>
                {midtermGrade ? `Your midterm grade is ${typeof midtermGrade === 'number' ? `${midtermGrade}%` : midtermGrade}.` : ''} Enter the weight percentage (e.g., 30 for 30%):
              </Text>

              <TextInput
                style={styles.weightInput}
                value={weightInputValue}
                onChangeText={setWeightInputValue}
                placeholder="20"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
                autoFocus={true}
              />

              <View style={styles.weightInputButtons}>
                <TouchableOpacity
                  style={styles.weightCancelButton}
                  onPress={handleWeightCancel}
                  activeOpacity={0.7}
                >
                  <Text style={styles.weightCancelButtonText}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.weightSubmitButton}
                  onPress={handleWeightSubmit}
                  activeOpacity={0.7}
                >
                  <Text style={styles.weightSubmitButtonText}>Add</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </Modal>
      )}

      <ThemeTransitionWrapper>
        <View style={{ flex: 1 }} {...swipeGestureHandler.panHandlers}>
        <View style={styles.container}>
      {/* Sidebar Button */}
      <View style={styles.sidebarButtonContainer} pointerEvents="box-none">
        <TouchableOpacity
          style={styles.sidebarButton}
          onPress={openSidebar}
        >
          <HamburgerIcon size={20} color={theme.colors.primary} strokeWidth={3} />
        </TouchableOpacity>
      </View>

      {/* Grades Title */}
      <View style={styles.gradesTitleContainer} pointerEvents="box-none">
        <Text style={styles.gradesTitle}>Grades</Text>
      </View>

      {/* Calculator Button */}
      <View style={styles.calculatorButtonContainer} pointerEvents="box-none">
        <TouchableOpacity
          style={[
            styles.calculatorButton,
            (!selectedCourse || selectedCourse.main === 'Select Course') && styles.calculatorButtonDisabled
          ]}
          onPress={() => {
            if (selectedCourse && selectedCourse.main !== 'Select Course') {
              console.log('Calculator button pressed for course:', selectedCourse.main);
              // Reset to main mode
              setCalculatorMode('main');
              // Reset current coursework
              setCurrentCoursework({
                title: '',
                weight: '',
                gradeCount: '',
                assignedGrades: []
              });
              // Open calculator with animation - initialization will be handled by useEffect
              openCalculatorModal();
            }
          }}
          disabled={!selectedCourse || selectedCourse.main === 'Select Course'}
          activeOpacity={0.7}
        >
          <MaterialIcons
            name="calculate"
            size={24}
            color={(!selectedCourse || selectedCourse.main === 'Select Course') ? theme.colors.textSecondary : theme.colors.primary}
          />
        </TouchableOpacity>
      </View>

      {/* Refresh Button */}
      <View style={styles.refreshButtonContainer} pointerEvents="box-none">
        <TouchableOpacity
          style={[
            styles.refreshButton,
            (isLoadingGrades || isUpdatingGrades) && styles.refreshButtonLoading,
            isOfflineMode && styles.refreshButtonDisabled
          ]}
          onPress={isOfflineMode ? undefined : handleRefresh}
          disabled={isLoadingGrades || isUpdatingGrades || isOfflineMode}
          activeOpacity={isOfflineMode ? 0.3 : 0.7}
        >
          <Animated.View
            style={{
              transform: [{
                rotate: isOfflineMode ? '0deg' : refreshRotation.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg']
                })
              }]
            }}
          >
            <RefreshIcon
              size={24}
              color={isOfflineMode ? '#808080' : (isLoadingGrades || isUpdatingGrades) ? theme.colors.textSecondary : safeCurrentThemeName === 'navy' ? '#DC2626' : '#f1c40f'}
            />
          </Animated.View>
        </TouchableOpacity>


      </View>

      {/* Main Content */}
      <TouchableOpacity
        style={styles.mainContent}
        activeOpacity={1}
        onPress={() => {
          if (isDropdownVisible) {
            console.log('🎯 Main content pressed - closing dropdown');
            setIsDropdownVisible(false);
          }
        }}
      >
        {isLoading || !gradesUrl ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Loading grades...</Text>
          </View>
        ) : (
          <>
            {/* Dropdown Menus Section */}
            <View style={[
              styles.dropdownSection,
              safeCurrentThemeName === 'colorful' && { borderColor: '#FFD700', borderWidth: 3 }
            ]}>
              <Text style={styles.sectionTitle}>Course Selection</Text>

              {/* Course Dropdown */}
              <View style={styles.dropdownContainer}>
                <Text style={styles.dropdownLabel}>Select Course:</Text>
                <View style={styles.dropdownWrapper}>
                  <TouchableOpacity
                    style={[
                      styles.dropdown,
                      isDropdownLoading && styles.dropdownDisabled,
                      safeCurrentThemeName === 'colorful' && { borderColor: '#FFD700', borderWidth: 3 }
                    ]}
                    onPress={toggleDropdown}
                    disabled={isDropdownLoading}
                  >
                    <View style={styles.dropdownContent}>
                      {selectedCourse && selectedCourse.main && selectedCourse.main !== 'Select Course' ? (
                        <View style={styles.selectedCourseContainer}>
                          <Text style={styles.dropdownText}>{selectedCourse.main}</Text>
                          <Text style={styles.courseCode}>{selectedCourse.code}</Text>
                        </View>
                      ) : (
                        <Text style={styles.dropdownPlaceholder}>Select Course</Text>
                      )}
                    </View>
                    <Text style={[
                      styles.dropdownArrow,
                      { transform: [{ rotate: isDropdownVisible ? '180deg' : '0deg' }] }
                    ]}>
                      ▼
                    </Text>
                  </TouchableOpacity>

                  {/* Dropdown Options List moved to end of JSX for proper layering */}
                  {/* Hidden WebView for data extraction - Only render in non-demo mode */}
            {gradesUrl && gradesUrl !== 'demo://grades' && (
              <View style={styles.hiddenWebView}>
                <WebView
                  ref={webViewRef}
                  source={{ uri: gradesUrl }}
                  onLoad={handleWebViewLoad}
                  onMessage={handleWebViewMessage}
                  onError={handleWebViewError}
                  javaScriptEnabled={true}
                  domStorageEnabled={true}
                  mixedContentMode="compatibility"
                />
              </View>
            )}

              {/* Dropdown Options List - Rendered at end for proper layering */}
              {isDropdownVisible && dropdownOptions.length > 0 && (
                <View
                  style={[
                    styles.dropdownListFloating,
                    safeCurrentThemeName === 'colorful' && { borderColor: '#FFD700', borderWidth: 3 }
                  ]}
                >
                  <ScrollView
                    style={styles.optionsList}
                    showsVerticalScrollIndicator={true}
                    nestedScrollEnabled={true}
                    keyboardShouldPersistTaps="handled"
                  >
                    {dropdownOptions.map((option, index) => {
                      const formatted = formatCourseName(option.text);
                      return (
                        <TouchableOpacity
                          key={index}
                          style={styles.optionItem}
                          onPress={() => {
                            console.log('🎯 TouchableOpacity pressed for option:', option.text);
                            handleCourseSelect(option);
                          }}
                          activeOpacity={0.7}
                        >
                          <View style={styles.optionContent}>
                            <Text style={styles.optionText}>{formatted.main}</Text>
                            <Text style={styles.optionCode}>{formatted.code}</Text>
                          </View>
                        </TouchableOpacity>
                      );
                    })}
                  </ScrollView>
                </View>
              )}
                </View>

                {/* Options count indicator */}
                {isDropdownLoading ? (
                  <View style={styles.statusLoadingContainer}>
                    <ActivityIndicator size="small" color={theme.colors.textSecondary} />
                    <Text style={styles.optionsCount}>Loading</Text>
                  </View>
                ) : dropdownOptions.length > 0 ? (
                  <Text style={styles.optionsCount}>
                    {dropdownOptions.length} {dropdownOptions.length === 1 ? 'course' : 'courses'} available
                  </Text>
                ) : (
                  <Text style={styles.optionsCount}>
                    No courses found
                  </Text>
                )}
              </View>
            </View>



            {/* Grades Display Section */}
            {(isLoadingGrades && !hasCachedGrades) ? (
              <View style={[
                styles.gradesDisplaySection,
                safeCurrentThemeName === 'colorful' && { borderColor: '#FFD700', borderWidth: 3 },
                { zIndex: -5, elevation: -5 }
              ]}>
                <Text style={styles.gradesDisplayTitle}>Course Grades</Text>
                <ScrollView
                  style={styles.gradesList}
                  showsVerticalScrollIndicator={false}
                  keyboardShouldPersistTaps="handled"
                >
                  {Array.from({ length: 10 }, (_, index) => (
                    <GradeGhostCard key={`ghost-${index}`} index={index} />
                  ))}
                </ScrollView>
              </View>
            ) : gradesData.length > 0 ? (
              <View style={[
                styles.gradesDisplaySection,
                safeCurrentThemeName === 'colorful' && { borderColor: '#FFD700', borderWidth: 3 },
                { zIndex: -5, elevation: -5 } // Push this section below dropdown
              ]}>
                {midtermGrade ? (
                  <View style={styles.courseGradesTitleContainer}>
                    <Text style={[styles.gradesDisplayTitle, styles.gradesDisplayTitleLeft]}>
                      Course Grades
                    </Text>
                    <Text style={styles.midtermGradeText}>
                      Midterm: {midtermGrade}%
                    </Text>
                  </View>
                ) : (
                  <Text style={styles.gradesDisplayTitle}>
                    Course Grades
                  </Text>
                )}
                <ScrollView
                  style={styles.gradesList}
                  showsVerticalScrollIndicator={false}
                  keyboardShouldPersistTaps="handled"
                >
                  {gradesData.map((gradeItem, index) => (
                    <View key={index} style={styles.gradeCard}>
                      {/* Left: Assignment/Exam Name */}
                      <View style={styles.gradeNameContainer}>
                        <Text style={styles.gradeName}>{gradeItem.Name}</Text>
                      </View>

                      {/* Middle: Staff Name */}
                      <View style={styles.gradeStaffContainer}>
                        <Text style={styles.gradeStaff}>{formatStaffName(gradeItem.staff)}</Text>
                      </View>

                      {/* Right: Circular Grade */}
                      <View style={styles.gradeCircleContainer}>
                        <CircularProgress grade={gradeItem.Grade} index={index} />
                      </View>
                    </View>
                  ))}
                </ScrollView>
              </View>
            ) : selectedCourse.main !== 'Select Course' ? (
              <View style={styles.noGradesContainer}>
                {midtermGrade ? (
                  <View style={styles.noGradesWithMidtermContainer}>
                    <Text style={styles.noGradesText}>No course grades found for this course</Text>
                    <View style={styles.midtermOnlyContainer}>
                      <Text style={styles.midtermOnlyGrade}>{midtermGrade}%</Text>
                      <Text style={styles.midtermOnlyLabel}>Midterm Grade:</Text>
                    </View>
                  </View>
                ) : (
                  <Text style={styles.noGradesText}>No grades found for this course</Text>
                )}
              </View>
            ) : null}
          </>
        )}
      </TouchableOpacity>



      {/* Calculator Modal */}
      <Modal
        visible={isCalculatorVisible}
        transparent={true}
        animationType="none"
        onRequestClose={closeCalculatorModal}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <Animated.View style={[
            styles.calculatorModalOverlay,
            { opacity: calculatorOverlayOpacity }
          ]} pointerEvents="box-none">
            <Animated.View style={[
              styles.calculatorModalContainer,
              {
                transform: [{ scale: calculatorModalScale }],
                opacity: calculatorModalOpacity,
              }
            ]}>
              {/* Close Button */}
              <TouchableOpacity
                style={styles.calculatorCloseButton}
                onPress={closeCalculatorModal}
                activeOpacity={0.7}
              >
                <Text style={styles.calculatorCloseButtonText}>✕</Text>
              </TouchableOpacity>

              {/* Modal Content Area */}
              <View style={styles.modalContentArea}>

            {/* Calculator Content */}
            <View style={styles.calculatorContent}>
              {calculatorMode === 'main' ? (
                <>
                  <Text style={styles.calculatorTitle}>Grade Calculator</Text>
                  <Text style={styles.calculatorSubtitle}>
                    Course: {selectedCourse?.main || 'No course selected'}
                  </Text>
                </>
              ) : (
                <>
                  <Text style={styles.calculatorTitle}>Add Coursework</Text>
                  <Text style={styles.calculatorSubtitle}>
                    Enter coursework details
                  </Text>
                </>
              )}

              {calculatorMode === 'main' ? (
                <>


                  {/* Coursework List - Full Available Space */}
                  <ScrollView
                    style={{
                      height: '51%', // Fixed large height
                      paddingHorizontal: 20,
                      paddingTop: 10,
                    }}
                    contentContainerStyle={{
                      minHeight: 400, // Ensure minimum height
                      paddingBottom: 20,
                    }}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled"
                  >
                    {calculatorSections.length > 0 ? (
                      calculatorSections.map((section, index) => (
                        <TouchableOpacity
                          key={section.id || index}
                          style={{
                            backgroundColor: theme.colors.surface,
                            borderRadius: 12,
                            marginBottom: 12,
                            borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 1,
                            borderColor: safeCurrentThemeName === 'colorful' ? '#00CED1' : theme.colors.border,
                            padding: 16,
                          }}
                          onPress={() => editSection(section)}
                          activeOpacity={0.7}
                        >
                          <View style={{
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                          }}>
                            {/* Left: Title */}
                            <Text style={{
                              fontSize: 16,
                              color: theme.colors.text,
                              fontWeight: '600',
                              flex: 1,
                            }}>
                              {section.isAutoGenerated ? 'Midterm' : (section.title || `Section ${index + 1}`)}
                            </Text>

                            {/* Right: Percentage Lost and Delete */}
                            <View style={{
                              flexDirection: 'row',
                              alignItems: 'center',
                              gap: 12,
                            }}>
                              <Text style={{
                                fontSize: 16,
                                color: safeCurrentThemeName === 'colorful' ? '#00CED1' : getPercentageColor(calculatePercentageLost(section)),
                                fontWeight: 'bold',
                              }}>
                                {formatPercentage(calculatePercentageLost(section))}
                              </Text>
                              {!section.isAutoGenerated && (
                                <TouchableOpacity
                                  style={{
                                    backgroundColor: theme.colors.error,
                                    width: Math.max(20, Dimensions.get('window').width * 0.055),
                                    height: Math.max(20, Dimensions.get('window').width * 0.055),
                                    borderRadius: Math.max(10, Dimensions.get('window').width * 0.0275), // Perfect circle
                                    justifyContent: 'center',
                                    alignItems: 'center',
                                  }}
                                  onPress={(e) => {
                                    e.stopPropagation(); // Prevent triggering edit
                                    removeSection(section.id);
                                  }}
                                >
                                  <Text style={{
                                    color: theme.colors.text,
                                    fontSize: Math.max(10, Math.min(12, Dimensions.get('window').width * 0.032)),
                                    fontWeight: 'bold',
                                  }}>✕</Text>
                                </TouchableOpacity>
                              )}
                            </View>
                          </View>
                        </TouchableOpacity>
                      ))
                    ) : (
                      <View style={{
                        height: 360, // Large height for empty state
                        alignItems: 'center',
                        justifyContent: 'center',
                        paddingVertical: 60,
                      }}>
                        <Text style={{
                          fontSize: Math.max(16, Math.min(20, Dimensions.get('window').width * 0.05)),
                          color: theme.colors.textSecondary,
                          marginBottom: 8,
                          textAlign: 'center',
                          paddingHorizontal: Math.max(15, Dimensions.get('window').width * 0.04),
                        }}>No coursework added yet</Text>
                        <Text style={{
                          fontSize: Math.max(12, Math.min(16, Dimensions.get('window').width * 0.04)),
                          color: theme.colors.textSecondary,
                          textAlign: 'center',
                          paddingHorizontal: Math.max(15, Dimensions.get('window').width * 0.04),
                        }}>Click the Add Coursework button to get started</Text>
                      </View>
                    )}
                  </ScrollView>

                </>
              ) : (
                <View style={[
                  styles.addCourseworkContainer,
                  safeCurrentThemeName === 'colorful' && { borderColor: '#FFD700', borderWidth: 3 }
                ]}>
                  <ScrollView
                    style={styles.addCourseworkScroll}
                    showsVerticalScrollIndicator={false}
                    contentContainerStyle={{ flexGrow: 1 }}
                    keyboardShouldPersistTaps="handled"
                  >
                    <View style={styles.addCourseworkContent}>
                    {/* Title Input - Read-only for midterm */}
                    <View style={styles.formInputGroup}>
                      <Text style={styles.formLabel}>Title:</Text>
                      <TextInput
                        style={[
                          styles.formTextInput,
                          currentCoursework.isAutoGenerated && styles.formTextInputDisabled
                        ]}
                        value={currentCoursework.title}
                        onChangeText={(text) => updateCurrentCoursework('title', text)}
                        placeholder="Enter coursework title (e.g., Quizzes, Assignments)"
                        placeholderTextColor={theme.colors.textSecondary}
                        editable={!currentCoursework.isAutoGenerated}
                      />
                    </View>

                    {/* Weight Input */}
                    <View style={styles.formInputGroup}>
                      <Text style={styles.formLabel}>Weight (%):</Text>
                      <View style={styles.formWeightContainer}>
                        <TextInput
                          style={[styles.formTextInput, styles.formWeightInput]}
                          value={currentCoursework.weight}
                          onChangeText={(text) => {
                            if (validateWeight(text) || text === '') {
                              updateCurrentCoursework('weight', text);
                            }
                          }}
                          placeholder="0-100"
                          placeholderTextColor={theme.colors.textSecondary}
                          keyboardType="numeric"
                          maxLength={3}
                        />
                        <Text style={styles.formWeightTotal}>
                          Total: {getTotalWeight().toFixed(1)}%
                        </Text>
                      </View>
                    </View>

                    {/* Grade Count Input - Hidden for midterm */}
                    {!currentCoursework.isAutoGenerated && (
                      <View style={styles.formInputGroup}>
                        <Text style={styles.formLabel}>Number of Grades:</Text>
                        <TextInput
                          style={[styles.formTextInput, styles.formGradeCountInput]}
                          value={currentCoursework.gradeCount}
                          onChangeText={(text) => updateCurrentCoursework('gradeCount', text)}
                          placeholder="1"
                          placeholderTextColor={theme.colors.textSecondary}
                          keyboardType="numeric"
                          maxLength={2}
                        />
                      </View>
                    )}

                    {/* Assigned Grades - Hidden for midterm */}
                    {!currentCoursework.isAutoGenerated && (
                      <View style={styles.formInputGroup}>
                      <Text style={styles.formLabel}>
                        Assigned Grades ({currentCoursework.assignedGrades.length}
                        {currentCoursework.gradeCount ? `/${currentCoursework.gradeCount}` : ''}):
                      </Text>

                      {currentCoursework.assignedGrades.length > 0 && (
                        <View style={styles.formAssignedGrades}>
                          {currentCoursework.assignedGrades.map((gradeItem, gradeIndex) => (
                            <View key={`assigned-${gradeItem.Grade}-${gradeIndex}`} style={[
                              styles.formAssignedGradeItem,
                              { borderLeftColor: getGradeColor(parseGrade(gradeItem.Grade).percentage), borderLeftWidth: 4 }
                            ]}>
                              <View style={styles.formAssignedGradeInfo}>
                                <Text style={styles.formAssignedGradeName}>{gradeItem.Name || 'Unknown'}</Text>
                                <Text style={[
                                  styles.formAssignedGradeValue,
                                  { color: getGradeColor(parseGrade(gradeItem.Grade).percentage) }
                                ]}>
                                  {gradeItem.Grade}
                                </Text>
                              </View>
                              {!currentCoursework.isAutoGenerated && (
                                <TouchableOpacity
                                  style={styles.formRemoveGradeButton}
                                  onPress={() => removeGradeFromCurrentCoursework(gradeIndex)}
                                >
                                  <Text style={styles.formRemoveGradeText}>×</Text>
                                </TouchableOpacity>
                              )}
                            </View>
                          ))}
                        </View>
                      )}

                      {(!currentCoursework.gradeCount || currentCoursework.assignedGrades.length < parseInt(currentCoursework.gradeCount)) && (
                        <View key={`grade-selection-${currentCoursework.assignedGrades.length}-${gradeListUpdateTrigger}-${forceUpdateCounter}`} style={styles.formGradeSelection}>
                          <View style={styles.gradeSelectionHeader}>
                            <Text style={styles.formGradeSelectionLabel}>Select Grade:</Text>
                          </View>
                          <ScrollView
                            key={`available-grades-${currentCoursework.assignedGrades.length}-${gradeListUpdateTrigger}-${forceUpdateCounter}`}
                            style={styles.formAvailableGradesList}
                            showsVerticalScrollIndicator={true}
                            nestedScrollEnabled={true}
                            keyboardShouldPersistTaps="handled"
                            scrollEnabled={true}
                            bounces={true}
                          >
                            {getAllGradesForCurrentCoursework().map((gradeItem, gradeIndex) => (
                              <TouchableOpacity
                                key={`${gradeItem.Grade}-${gradeItem.Name}`}
                                style={styles.formAvailableGradeItem}
                                onPress={() => assignGradeToCurrentCoursework(gradeItem)}
                              >
                                <Text style={styles.formAvailableGradeName}>{gradeItem.Name || 'Unknown'}</Text>
                                <Text style={[
                                  styles.formAvailableGradeValue,
                                  { color: getGradeColor(parseGrade(gradeItem.Grade).percentage) }
                                ]}>
                                  {gradeItem.Grade}
                                </Text>
                              </TouchableOpacity>
                            ))}
                          </ScrollView>
                        </View>
                      )}


                      </View>
                    )}
                    </View>
                  </ScrollView>
                </View>
              )}
            </View>

            {/* Bottom Fixed Section - Grade Summary and Action Buttons */}
            {calculatorMode === 'main' ? (
              <View style={styles.bottomFixedSection}>
                {/* Total Summary - Only show when there are sections */}
                {calculatorSections.length > 0 && (
                  <View style={styles.totalSummaryContainer}>
                    <View style={styles.totalSummaryCard}>
                      <Text style={styles.totalSummaryTitle}>Grade Summary</Text>

                      <View style={styles.totalSummaryRow}>
                        <Text style={styles.totalSummaryLabel}>Total Lost:</Text>
                        <Text style={[
                          styles.totalSummaryValue,
                          { color: safeCurrentThemeName === 'colorful' ? '#00CED1' : (calculateTotalPercentageLost() === 0 ? theme.colors.success : theme.colors.error) }
                        ]}>
                          -{calculateTotalPercentageLost().toFixed(1)}%
                        </Text>
                      </View>

                      <View style={styles.totalSummaryRow}>
                        <Text style={styles.totalSummaryLabel}>Highest Achievable: </Text>
                        <View style={styles.achievableGradeContainer}>
                          <Text style={[
                            styles.achievablePercentage,
                            { color: safeCurrentThemeName === 'colorful' ? '#00CED1' : getGradeColor(getHighestAchievableGrade().percentage) }
                          ]}>
                            {getHighestAchievableGrade().percentage.toFixed(1)}%
                          </Text>
                          <Text style={[
                            styles.achievableLetterGrade,
                            { color: safeCurrentThemeName === 'colorful' ? '#00CED1' : getGradeColor(getHighestAchievableGrade().percentage) }
                          ]}>
                            ({getHighestAchievableGrade().letter})
                          </Text>
                        </View>
                      </View>
                    </View>
                  </View>
                )}

                {/* Add Coursework Button */}
                <View style={styles.addButtonContainer}>
                  <TouchableOpacity
                    style={styles.addSectionButton}
                    onPress={addNewCoursework}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.addSectionButtonText}>+ Add Coursework</Text>
                  </TouchableOpacity>
                </View>
              </View>
            ) : (
              <View style={styles.bottomFixedSection}>
                <View style={styles.formButtonsContainer}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={cancelCoursework}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[
                      styles.saveButton,
                      (!currentCoursework.title || !currentCoursework.weight) && styles.saveButtonDisabled
                    ]}
                    onPress={saveCoursework}
                    disabled={!currentCoursework.title || !currentCoursework.weight}
                    activeOpacity={0.7}
                  >
                    <Text style={styles.saveButtonText}>Save</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
            </View>
          </Animated.View>
        </Animated.View>
        </TouchableWithoutFeedback>
      </Modal>



      {/* Sidebar Component */}
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={closeSidebar}
        sidebarAnim={sidebarAnim}
        navigation={navigation}
        currentScreen="Grades"
      />
    </View>
      </View>
    </ThemeTransitionWrapper>
    </>
  );
};

// Create styles function that uses theme
const createStyles = (theme, safeCurrentThemeName) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingTop: 40, // Add top padding to account for status bar
  },
  gradesTitleContainer: {
    position: 'absolute',
    top: 55, // Raised higher
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 10,
  },
  gradesTitle: {
    fontSize: 30, // Increased font size slightly
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
  },
  sidebarButtonContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 10,
  },
  sidebarButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  refreshButtonContainer: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 10,
  },
  refreshButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  refreshButtonLoading: {
    backgroundColor: theme.colors.surfaceSecondary,
    opacity: 0.7,
  },
  refreshButtonDisabled: {
    opacity: 0.4,
    backgroundColor: theme.colors.surface + '80',
  },

  mainContent: {
    flex: 1,
    paddingTop: 100, // Space for header elements
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 15,
  },
  dropdownSection: {
    backgroundColor: theme.colors.surface,
    borderRadius: 15,
    padding: Math.max(15, Dimensions.get('window').width * 0.04),
    marginBottom: 15,
    marginHorizontal: Math.max(12, Dimensions.get('window').width * 0.03),
    borderWidth: 1,
    borderColor: theme.colors.border,
    zIndex: 9998,
    elevation: 9998,
    overflow: 'visible',
  },
  sectionTitle: {
    fontSize: Math.min(22, Dimensions.get('window').width * 0.055), // Responsive font size
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: Math.max(15, Dimensions.get('window').width * 0.04), // Responsive margin
    textAlign: 'center',
  },
  dropdownContainer: {
    marginBottom: 20,
    overflow: 'visible',
  },
  dropdownLabel: {
    fontSize: 16,
    color: theme.colors.text,
    marginBottom: 8,
    fontWeight: '600',
  },
  dropdownWrapper: {
    position: 'relative',
    zIndex: 9999,
    elevation: 9999,
  },
  dropdown: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 10,
    paddingVertical: 18,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.primary, // Yellow border for dropdown
    minHeight: 55,
  },
  dropdownContent: {
    flex: 1,
    justifyContent: 'center',
    minHeight: 20,
  },
  selectedCourseContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: theme.colors.text,
    flex: 1,
    textAlignVertical: 'center',
  },
  dropdownPlaceholder: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    flex: 1,
    fontStyle: 'italic',
    textAlignVertical: 'center',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  dropdownLoadingText: {
    fontSize: 16,
    color: theme.colors.primary,
    marginLeft: 8,
    fontStyle: 'italic',
  },
  statusLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  dropdownDisabled: {
    opacity: 0.6,
    borderColor: theme.colors.textSecondary,
  },
  courseCode: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    marginLeft: 10,
  },
  dropdownArrow: {
    fontSize: 14,
    color: theme.colors.primary,
    marginLeft: 15,
    textAlignVertical: 'center',
  },
  dropdownList: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: theme.colors.primary, // Yellow border for dropdown list
    borderTopWidth: 0,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    maxHeight: 200,
    zIndex: 101, // Higher than wrapper
    elevation: 101, // For Android
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  dropdownListFloating: {
    position: 'absolute',
    marginTop: 60,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    borderTopWidth: 0,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    maxHeight: 200,
    zIndex: 99999,
    elevation: 99999,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.44,
    shadowRadius: 10.32,
  },
  optionsCount: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 5,
    marginLeft: 8,
    fontStyle: 'italic',
  },
  optionsList: {
    flex: 1,
    overflow: 'hidden', // Clip content to container bounds
    borderBottomLeftRadius: 10, // Add bottom border radius to container
    borderBottomRightRadius: 10, // Add bottom border radius to container
  },
  optionItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  optionItemLast: {
    padding: 15,
    borderBottomWidth: 0, // Remove bottom border for last item
    borderBottomLeftRadius: 10, // Add bottom left radius
    borderBottomRightRadius: 10, // Add bottom right radius
  },
  optionContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  optionText: {
    fontSize: 16,
    color: theme.colors.text,
    fontWeight: '600',
    flex: 1,
  },
  optionCode: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    marginLeft: 10,
  },
  dropdownOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 50, // Much lower than dropdown list to avoid interference
    elevation: 50, // For Android
  },

  calculatorButtonContainer: {
    position: 'absolute',
    top: 50,
    right: 80, // Position to the left of refresh button (refresh is at right: 20, width: 50, so 20 + 50 + 10 = 80)
    zIndex: 10,
  },
  calculatorButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : 'transparent',
  },
  calculatorButtonDisabled: {
    backgroundColor: theme.colors.surfaceSecondary,
    opacity: 0.7,
  },
  hiddenWebView: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
    opacity: 0,
  },

  // Ghost Loading Styles
  ghostText: {
    backgroundColor: theme.colors.border,
    borderRadius: 4,
  },
  ghostGradeName: {
    height: 16,
    width: '80%',
    marginBottom: 4,
  },
  ghostStaffName: {
    height: 14,
    width: '70%',
  },
  ghostCircle: {
    width: Math.max(65, Math.min(70, Dimensions.get('window').width * 0.16)),
    height: Math.max(65, Math.min(70, Dimensions.get('window').width * 0.16)),
    borderRadius: Math.max(32.5, Math.min(35, Dimensions.get('window').width * 0.08)),
    backgroundColor: theme.colors.border,
  },
  gradesDisplaySection: {
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: 15,
    borderTopRightRadius: 15,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    padding: Math.max(15, Dimensions.get('window').width * 0.04),
    paddingBottom: Math.max(30, Dimensions.get('window').height * 0.05),
    marginTop: 10,
    marginHorizontal: Math.max(12, Dimensions.get('window').width * 0.03),
    marginBottom: 0,
    borderWidth: 1,
    zIndex: -1,
    elevation: -1,
    borderBottomWidth: 0, // Remove bottom border to make it open-ended
    borderColor: theme.colors.border,
    flex: 1, // Keep flex to stretch to end
  },
  gradesDisplayTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
    marginBottom: 15,
    textAlign: 'center',
  },
  courseGradesTitleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  gradesDisplayTitleLeft: {
    textAlign: 'left',
    marginBottom: 0,
    flex: 1,
  },
  gradesDisplayTitleCenter: {
    textAlign: 'center',
    marginBottom: 0,
  },
  midtermGradeText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.success,
    textAlign: 'right',
  },
  gradesList: {
    flex: 1,
    marginTop: 10
  },
  gradeCard: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 12,
    padding: Math.max(6, Dimensions.get('window').width * 0.02), // Further reduced padding for more compact cards
    marginBottom: Math.max(4, Dimensions.get('window').width * 0.01), // Further reduced margin
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 1, // Thicker border for pink mode, elegant border for navy
    borderColor: safeCurrentThemeName === 'colorful' ? '#00CED1' : safeCurrentThemeName === 'navy' ? '#DC2626' : theme.colors.border, // Turquoise border for pink mode, red border for navy
    minHeight: Math.max(45, Dimensions.get('window').height * 0.055), // Further reduced minimum height
  },
  gradeNameContainer: {
    flex: 2,
  },
  gradeName: {
    fontSize: Math.min(15, Dimensions.get('window').width * 0.038), // Slightly smaller responsive font size
    fontWeight: '600',
    color: theme.colors.text,
    lineHeight: Math.min(18, Dimensions.get('window').width * 0.045), // Reduced line height for compactness
  },
  gradeStaffContainer: {
    flex: 3,
    paddingHorizontal: 10,
    alignItems: 'center',
  },
  gradeStaff: {
    fontSize: Math.min(13, Dimensions.get('window').width * 0.032), // Smaller responsive font size
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: Math.min(16, Dimensions.get('window').width * 0.04), // Reduced line height for compactness
    fontWeight: '500',
  },
  gradeCircleContainer: {
    flex: 1.3,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noGradesContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 15,
    padding: 30,
    marginTop: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  noGradesText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  noGradesWithMidtermContainer: {
    alignItems: 'center',
  },
  midtermOnlyContainer: {
    marginTop: 20,
    padding: 15,
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: theme.colors.success,
    alignItems: 'center',
  },
  midtermOnlyLabel: {
    fontSize: 14,
    color: '#9CA3AF',
    marginBottom: 5,
    fontWeight: '500',
  },
  midtermOnlyGrade: {
    fontSize: 20,
    color: '#10B981',
    fontWeight: 'bold',
  },

  // Circular Progress Styles (SVG-based Final)
  circularProgressContainer: {
    width: Math.max(65, Math.min(75, Dimensions.get('window').width * 0.17)), // Slightly larger than circle to prevent cutoff
    height: Math.max(65, Math.min(75, Dimensions.get('window').width * 0.17)), // Slightly larger than circle to prevent cutoff
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
    overflow: 'visible', // Ensure nothing gets clipped
  },
  svgStyle: {
    position: 'absolute',
  },
  gradeTextContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    width: Math.max(65, Math.min(70, Dimensions.get('window').width * 0.16)), // Match the responsive circle size
    height: Math.max(65, Math.min(70, Dimensions.get('window').width * 0.16)), // Match the responsive circle size
    paddingHorizontal: Math.max(6, Dimensions.get('window').width * 0.015), // Responsive padding
    zIndex: 10,
  },
  gradeText: {
    fontSize: Math.max(12, Math.min(14, Dimensions.get('window').width * 0.035)), // Responsive and smaller font size
    fontWeight: 'bold',
    textAlign: 'center',
    lineHeight: Math.max(13, Math.min(15, Dimensions.get('window').width * 0.038)), // Responsive line height
  },

  // Calculator Modal Styles
  calculatorModalOverlay: {
    flex: 1,
    backgroundColor: theme.colors.modalOverlay,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Math.max(10, Dimensions.get('window').width * 0.025), // Responsive padding for small screens
  },
  calculatorModalContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 20,
    padding: Math.max(15, Dimensions.get('window').width * 0.04), // Responsive padding
    width: Math.max(320, Math.min(Dimensions.get('window').width * 0.95, 500)), // Better responsive width
    height: Math.max(500, Math.min(Dimensions.get('window').height * 0.85, 700)), // Better responsive height
    maxWidth: '95%', // Ensure it doesn't exceed screen width
    maxHeight: '90%', // Ensure it doesn't exceed screen height
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
    flexDirection: 'column',
  },
  modalContentArea: {
    flex: 1,
    marginBottom: 10,
    overflow: 'visible',
    paddingBottom: 20, // Minimal padding - let form stretch closer to buttons
  },
  calculatorCloseButton: {
    position: 'absolute',
    top: 15,
    right: 15,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: theme.colors.surfaceSecondary,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 20,
  },
  calculatorCloseButtonText: {
    fontSize: 18,
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  calculatorContent: {
    flex: 1, // Fill available space in modalContentArea
    marginTop: 20,
  },
  calculatorTitle: {
    fontSize: Math.max(18, Math.min(26, Dimensions.get('window').width * 0.06)), // Responsive font size
    fontWeight: 'bold',
    color: theme.colors.primary,
    textAlign: 'center',
    marginBottom: Math.max(8, Dimensions.get('window').height * 0.01), // Responsive margin
  },
  calculatorSubtitle: {
    fontSize: Math.max(14, Math.min(18, Dimensions.get('window').width * 0.045)), // Responsive font size
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: Math.max(15, Dimensions.get('window').height * 0.02), // Responsive margin
    paddingHorizontal: Math.max(10, Dimensions.get('window').width * 0.025), // Responsive padding to prevent text cutoff
  },
  sectionsContainer: {
    flex: 1,
    marginBottom: 20,
  },
  courseworkCard: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  courseworkCardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  courseworkTitle: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
    flex: 1,
  },
  courseworkRightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  percentageLostText: {
    fontSize: 16,
    color: '#F44336',
    fontWeight: 'bold',
  },
  deleteCourseworkButton: {
    backgroundColor: '#F44336',
    borderRadius: 6,
    padding: 6,
    minWidth: 24,
    minHeight: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  deleteCourseworkButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  sectionCard: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 12,
    padding: 25,
    marginBottom: 15,
    borderWidth: 2,
    borderColor: theme.colors.primary,
    minHeight: 200,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#EAB308',
  },
  removeSectionButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeSectionButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  inputGroup: {
    marginBottom: 25,
  },
  inputLabel: {
    fontSize: 16,
    color: '#EAB308',
    marginBottom: 10,
    fontWeight: '600',
  },
  textInput: {
    backgroundColor: '#3A3A3A', // This is already the correct new surface grey
    borderRadius: 8,
    padding: 15,
    fontSize: 16,
    color: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#EAB308',
    minHeight: 50,
    marginBottom: 5,
  },
  disabledInput: {
    backgroundColor: '#555555', // Updated to new border grey
    color: '#999999',
  },
  weightInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  weightInput: {
    flex: 1,
    maxWidth: 80,
  },
  invalidInput: {
    borderColor: '#FF4444',
    backgroundColor: '#2A1A1A',
  },
  totalWeightText: {
    fontSize: 14,
    color: '#EAB308',
    fontWeight: '600',
  },
  gradeCountInput: {
    maxWidth: 100,
  },
  evaluationTypeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  evaluationTypeButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 6,
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  evaluationTypeButtonSelected: {
    backgroundColor: '#EAB308',
    borderColor: '#EAB308',
  },
  evaluationTypeButtonText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  evaluationTypeButtonTextSelected: {
    color: theme.colors.primaryText,
    fontWeight: 'bold',
  },
  assignedGradesContainer: {
    marginTop: 8,
  },
  assignedGradeItem: {
    backgroundColor: theme.colors.surface,
    borderRadius: 6,
    padding: 10,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: theme.colors.primary,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  assignedGradeInfo: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  assignedGradeName: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
    flex: 1,
  },
  assignedGradeText: {
    fontSize: 14,
    color: '#EAB308',
    fontWeight: '600',
  },
  removeGradeButton: {
    width: Math.max(18, Dimensions.get('window').width * 0.05), // Responsive size
    height: Math.max(18, Dimensions.get('window').width * 0.05), // Responsive size, using width for perfect circle
    borderRadius: Math.max(9, Dimensions.get('window').width * 0.025), // Half of width/height for perfect circle
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeGradeButtonText: {
    fontSize: Math.max(10, Math.min(12, Dimensions.get('window').width * 0.032)), // Responsive font size
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  gradeSelectionContainer: {
    marginTop: 10,
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 8,
    padding: 10,
  },
  gradeSelectionLabel: {
    fontSize: 14,
    color: '#FFFFFF',
    marginBottom: 8,
    fontWeight: '600',
  },
  availableGradesList: {
    maxHeight: 120,
  },
  availableGradeItem: {
    backgroundColor: theme.colors.surface,
    borderRadius: 6,
    padding: 10,
    marginBottom: 5,
    borderWidth: 1,
    borderColor: theme.colors.border,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  availableGradeName: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '600',
    flex: 1,
  },
  availableGradeText: {
    fontSize: 14,
    color: '#EAB308',
    fontWeight: '600',
  },
  noGradesText: {
    fontSize: 14,
    color: '#666666',
    fontStyle: 'italic',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  emptyStateText: {
    fontSize: 18,
    color: '#666666',
    marginBottom: 8,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#999999',
    textAlign: 'center',
  },
  addSectionButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 10,
    paddingVertical: Math.max(12, Dimensions.get('window').height * 0.018), // Responsive vertical padding
    paddingHorizontal: Math.max(25, Dimensions.get('window').width * 0.06), // Responsive horizontal padding
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: Math.max(180, Dimensions.get('window').width * 0.45), // Responsive min width
    width: '85%', // Slightly wider for better fit
    maxWidth: 300, // Max width to prevent stretching on large screens
  },
  addSectionButtonText: {
    fontSize: Math.max(14, Math.min(16, Dimensions.get('window').width * 0.04)), // Responsive font size
    color: theme.colors.primaryText,
    fontWeight: 'bold',
  },

  // New Add Coursework Form Styles
  addCourseworkContainer: {
    flex: 1,
    backgroundColor: theme.colors.surface,
    borderRadius: 15,
    borderWidth: 2,
    borderColor: theme.colors.primary, // Yellow border
    marginHorizontal: 15, // Fixed horizontal margin
    marginTop: 10, // Fixed top margin
    marginBottom: 10, // Minimal bottom margin - let it stretch to buttons
    minHeight: 300, // Ensure minimum height so it's always visible
  },
  addCourseworkScroll: {
    flex: 1,
  },
  addCourseworkContent: {
    padding: 20, // Fixed padding for consistent spacing
    flex: 1, // Fill the available space in ScrollView
    justifyContent: 'flex-start', // Align content to top
  },
  formInputGroup: {
    marginBottom: 20, // Fixed margin for consistent spacing
  },
  formLabel: {
    fontSize: 16, // Fixed font size
    color: theme.colors.primary, // Yellow color for labels
    marginBottom: 8, // Fixed margin
    fontWeight: '600',
  },
  formTextInput: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 10,
    padding: 15, // Fixed padding
    fontSize: 16, // Fixed font size
    color: theme.colors.text,
    borderWidth: 2,
    borderColor: theme.colors.primary, // Yellow border
    minHeight: 50, // Fixed minimum height
  },
  formTextInputDisabled: {
    backgroundColor: theme.colors.surface,
    borderColor: theme.colors.textSecondary,
    opacity: 0.7,
  },
  formWeightContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
  },
  formWeightInput: {
    flex: 1,
    maxWidth: 120,
    borderColor: theme.colors.primary, // Yellow border
  },
  formWeightTotal: {
    fontSize: 14,
    color: theme.colors.primary, // Yellow color
    fontWeight: '600',
  },
  formGradeCountInput: {
    maxWidth: 120,
  },
  formAssignedGrades: {
    marginTop: 10,
  },
  formAssignedGradeItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  formAssignedGradeInfo: {
    flex: 1,
  },
  formAssignedGradeName: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: '500',
  },
  formAssignedGradeValue: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  formRemoveGradeButton: {
    width: Math.max(20, Dimensions.get('window').width * 0.055), // Responsive size
    height: Math.max(20, Dimensions.get('window').width * 0.055), // Responsive size, using width for perfect circle
    borderRadius: Math.max(10, Dimensions.get('window').width * 0.0275), // Half of width/height for perfect circle
    backgroundColor: '#FF4444',
    justifyContent: 'center',
    alignItems: 'center',
  },
  formRemoveGradeText: {
    fontSize: Math.max(12, Math.min(16, Dimensions.get('window').width * 0.04)), // Responsive font size
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  formGradeSelection: {
    marginTop: 15,
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 8,
    padding: 15,
    borderWidth: 1,
    borderColor: theme.colors.border,
    flex: 1, // Allow it to expand
    minHeight: 200, // Ensure minimum height for scrolling
  },
  gradeSelectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  formGradeSelectionLabel: {
    fontSize: 14,
    color: '#CCCCCC',
    fontWeight: '600',
  },

  formAvailableGradesList: {
    maxHeight: 250, // Increased height for better scrolling
    minHeight: 150, // Increased minimum height
    flex: 1, // Allow it to take available space
  },
  formAvailableGradeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 6,
    padding: 12,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  formAvailableGradeName: {
    fontSize: 14,
    color: theme.colors.text, // Use theme-aware text color
    flex: 1,
  },
  formAvailableGradeValue: {
    fontSize: 16,
    color: '#F0C674',
    fontWeight: 'bold',
  },
  formNoGradesText: {
    fontSize: 14,
    color: '#888888',
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: 10,
  },
  weightInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  weightInput: {
    flex: 1,
    maxWidth: 80,
  },
  invalidInput: {
    borderColor: '#FF4444',
    backgroundColor: '#2A1A1A',
  },
  totalWeightText: {
    fontSize: 14,
    color: '#EAB308',
    fontWeight: '600',
  },
  gradeCountInput: {
    maxWidth: 100,
  },
  assignedGradesContainer: {
    marginTop: 8,
  },
  assignedGradeInfo: {
    flex: 1,
  },

  gradeSelectionContainer: {
    marginTop: 10,
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 8,
    padding: 10,
  },
  gradeSelectionLabel: {
    fontSize: 14,
    color: '#FFFFFF',
    marginBottom: 8,
    fontWeight: '600',
  },
  availableGradesList: {
    maxHeight: 120,
  },
  availableGradeItem: {
    backgroundColor: theme.colors.surface,
    borderRadius: 6,
    padding: 10,
    marginBottom: 5,
    borderWidth: 1,
    borderColor: theme.colors.border,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  // Bottom Fixed Section - Absolute positioning at bottom
  bottomFixedSection: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: '#555555', // Updated to new border grey
    paddingHorizontal: Math.max(15, Dimensions.get('window').width * 0.04), // Responsive horizontal padding
    paddingBottom: Math.max(15, Dimensions.get('window').height * 0.02), // Responsive bottom padding
    paddingTop: Math.max(10, Dimensions.get('window').height * 0.015), // Add top padding
  },

  // Separate button containers
  addButtonContainer: {
    paddingTop: Math.max(10, Dimensions.get('window').height * 0.015), // Responsive top padding
    justifyContent: 'center',
    alignItems: 'center',
  },
  formButtonsContainer: {
    flexDirection: 'row',
    gap: Math.max(12, Dimensions.get('window').width * 0.03), // Responsive gap between buttons
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cancelButton: {
    flex: 1,
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 10,
    paddingVertical: 12, // Fixed padding to prevent size changes
    paddingHorizontal: 8, // Reduced horizontal padding
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
    minHeight: 44, // Fixed minimum height
  },
  cancelButtonText: {
    fontSize: 16, // Fixed font size
    color: theme.colors.text,
    fontWeight: '600',
    textAlign: 'center',
  },
  saveButton: {
    flex: 1,
    backgroundColor: theme.colors.primary,
    borderRadius: 10,
    paddingVertical: 12, // Fixed padding to prevent size changes
    paddingHorizontal: 8, // Reduced horizontal padding
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: theme.colors.primary,
    minHeight: 44, // Fixed minimum height
  },
  saveButtonDisabled: {
    backgroundColor: theme.colors.textSecondary,
    opacity: 0.5,
  },
  saveButtonText: {
    fontSize: 16, // Fixed font size
    color: theme.colors.primaryText,
    fontWeight: 'bold',
    textAlign: 'center',
  },

  // Weight Input Modal Styles
  weightInputContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 20,
    padding: 25,
    width: '80%',
    maxWidth: 400,
    borderWidth: 2,
    borderColor: '#EAB308',
  },
  weightInputTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#EAB308',
    textAlign: 'center',
    marginBottom: 10,
  },
  weightInputSubtitle: {
    fontSize: 14,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 20,
  },
  weightInput: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 10,
    padding: 15,
    fontSize: 16,
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: '#404040',
    marginBottom: 20,
    textAlign: 'center',
  },
  weightInputButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 15,
  },
  weightCancelButton: {
    flex: 1,
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  weightCancelButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  weightSubmitButton: {
    flex: 1,
    backgroundColor: '#EAB308',
    borderRadius: 10,
    padding: 15,
    alignItems: 'center',
  },
  weightSubmitButtonText: {
    fontSize: 16,
    color: theme.colors.primaryText,
    fontWeight: 'bold',
  },

  // Total Summary Styles
  totalSummaryContainer: {
    paddingTop: Math.max(20, Dimensions.get('window').height * 0.025), // More space at top to push it lower
    paddingBottom: Math.max(10, Dimensions.get('window').height * 0.015), // Responsive bottom padding
  },
  totalSummaryCard: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 15,
    padding: Math.max(15, Dimensions.get('window').width * 0.04), // Responsive padding
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 2, // Thicker border for pink mode
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary, // Yellow border for pink mode
    alignSelf: 'center', // Center the card
    width: '105%', // Increased width to make it bigger
  },
  totalSummaryTitle: {
    fontSize: Math.max(14, Math.min(18, Dimensions.get('window').width * 0.042)), // More responsive, smaller on small screens
    fontWeight: 'bold',
    color: '#EAB308',
    textAlign: 'center',
    marginBottom: Math.max(8, Dimensions.get('window').height * 0.012), // Responsive margin
  },
  totalSummaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Math.max(6, Dimensions.get('window').height * 0.01), // Responsive margin
  },
  totalSummaryLabel: {
    fontSize: Math.max(12, Math.min(16, Dimensions.get('window').width * 0.038)), // More responsive, smaller on small screens
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text, // White in pink mode, theme text in others
    fontWeight: '600',
  },
  totalSummaryValue: {
    fontSize: Math.max(12, Math.min(16, Dimensions.get('window').width * 0.038)), // More responsive, smaller on small screens
    fontWeight: 'bold',
  },
  achievableGradeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Math.max(4, Dimensions.get('window').width * 0.012), // Responsive gap
  },
  achievablePercentage: {
    fontSize: Math.max(12, Math.min(16, Dimensions.get('window').width * 0.038)), // More responsive, smaller on small screens
    fontWeight: 'bold',
  },
  achievableLetterGrade: {
    fontSize: Math.max(12, Math.min(16, Dimensions.get('window').width * 0.038)), // More responsive, smaller on small screens
    fontWeight: 'bold',
  },

});

export default GradesScreen;