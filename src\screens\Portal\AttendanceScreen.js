import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Animated,
  PanResponder,
  Dimensions,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import Sidebar from '../../components/Sidebar';
import HamburgerIcon from '../../components/HamburgerIcon';
import RefreshIcon from '../../components/RefreshIcon';
import { clearWebViewSession, disposeWebView } from '../../utils/WebViewUtils';
import { useTheme } from '../../contexts/ThemeContext';
import { useOffline } from '../../contexts/OfflineContext';
import { handleDemoModeInit, handleDemoModeRefresh, getDemoAttendanceData } from '../../utils/DemoModeHelper';
import { getCachedAttendanceCourses } from '../../utils/OfflineUtils';
import ThemeTransitionWrapper from '../../components/ThemeTransitionWrapper';

const AttendanceScreen = ({ navigation }) => {
  // Theme context
  const { theme, currentThemeName, transitionAnim, fadeAnim } = useTheme();
  const safeCurrentThemeName = currentThemeName || 'dark';

  // Offline context
  const { isOfflineMode } = useOffline();

  const [isLoading, setIsLoading] = useState(true);
  const [credentials, setCredentials] = useState(null);
  const [attendanceUrl, setAttendanceUrl] = useState('');
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const [sidebarAnim] = useState(new Animated.Value(-300));
  const [dropdownOptions, setDropdownOptions] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState({ main: 'Select Course', code: '' });
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [isDropdownLoading, setIsDropdownLoading] = useState(true);
  const [attendanceData, setAttendanceData] = useState([]);
  const [isLoadingAttendance, setIsLoadingAttendance] = useState(false);
  const [refreshRotation] = useState(new Animated.Value(0)); // For rotating refresh arrow
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [hasCachedData, setHasCachedData] = useState(false);
  const [attendanceWarnings, setAttendanceWarnings] = useState([]);
  const [isLoadingWarnings, setIsLoadingWarnings] = useState(false);
  const webViewRef = useRef(null);

  // Refs for tracking background operations and component mount state
  const isMountedRef = useRef(true);
  const activeTimeoutsRef = useRef(new Set());
  const retryAttemptsRef = useRef(0);

  // Generate styles based on current theme
  const styles = createStyles(theme, safeCurrentThemeName);

  // Comprehensive function to kill all background operations
  const killAllBackgroundOperations = async () => {
    console.log('🛑 AttendanceScreen: Killing all background operations...');

    // Mark component as unmounted to prevent state updates
    isMountedRef.current = false;

    // Clear all active timeouts
    activeTimeoutsRef.current.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    activeTimeoutsRef.current.clear();

    // Stop all animations
    refreshRotation.stopAnimation();
    sidebarAnim.stopAnimation();

    // Reset all loading states to prevent cache corruption
    setIsLoading(false);
    setIsLoadingAttendance(false);
    setIsLoadingWarnings(false);
    setIsRefreshing(false);
    setIsDropdownLoading(false);

    // Dispose of WebView
    await disposeWebView(webViewRef, 'attendance-webview');

    console.log('✅ AttendanceScreen: All background operations killed');
  };

  // Safe state setter that checks if component is still mounted
  const safeSetState = (setter, value, stateName) => {
    if (isMountedRef.current) {
      setter(value);
    } else {
      console.log(`⚠️ AttendanceScreen: Prevented ${stateName} state update after unmount`);
    }
  };

  // Safe timeout wrapper that tracks timeouts for cleanup
  const safeSetTimeout = (callback, delay) => {
    const timeoutId = setTimeout(() => {
      activeTimeoutsRef.current.delete(timeoutId);
      if (isMountedRef.current) {
        callback();
      }
    }, delay);
    activeTimeoutsRef.current.add(timeoutId);
    return timeoutId;
  };

  // Get warning level color
  const getWarningLevelColor = (level) => {
    switch (level) {
      case 1:
        return '#3B82F6'; // Blue
      case 2:
        return '#F59E0B'; // Orange
      case 3:
        return '#EF4444'; // Red
      default:
        return theme.colors.textSecondary;
    }
  };

  useEffect(() => {
    loadCredentialsAndSetupAttendance();

    // Cleanup function
    return () => {
      console.log('🧹 AttendanceScreen: Component unmounting - cleaning up...');
      killAllBackgroundOperations();
    };
  }, []);

  // Handle navigation focus/blur - kill background operations when losing focus
  useFocusEffect(
    useCallback(() => {
      console.log('🔄 AttendanceScreen: Screen focused');

      // Mark component as mounted when focused
      isMountedRef.current = true;

      // Return cleanup function that runs when screen loses focus
      return () => {
        console.log('🔄 AttendanceScreen: Screen losing focus - killing background operations...');
        killAllBackgroundOperations();
      };
    }, [])
  );

  // Kill background operations when navigating away completely
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      console.log('🧹 AttendanceScreen: Screen unmounting - Killing all background operations...');
      await killAllBackgroundOperations();
    });

    return unsubscribe;
  }, [navigation]);

  const openSidebar = () => {
    setIsSidebarVisible(true);
    Animated.timing(sidebarAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeSidebar = () => {
    Animated.timing(sidebarAnim, {
      toValue: -300,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsSidebarVisible(false);
    });
  };

  // Swipe gesture handler for opening/closing sidebar - Enhanced for Android
  const swipeGestureHandler = PanResponder.create({
    onStartShouldSetPanResponder: () => false, // Don't capture immediately
    onStartShouldSetPanResponderCapture: () => false, // Don't capture on start
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to significant horizontal swipes
      const { dx, dy } = gestureState;

      // Only capture if it's a clear horizontal swipe with significant movement
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
      // Only capture significant horizontal swipes to prevent Android system gestures
      const { dx, dy } = gestureState;
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onPanResponderGrant: () => {
      // Gesture has been granted - prevent other handlers
      return true;
    },
    onPanResponderMove: () => {
      // Optional: Add visual feedback during swipe
    },
    onPanResponderRelease: (evt, gestureState) => {
      const { dx, dy } = gestureState;
      const isHorizontalSwipe = Math.abs(dx) > Math.abs(dy);
      const swipeDistance = Math.abs(dx);

      if (isHorizontalSwipe && swipeDistance > 100) {
        if (dx > 0) {
          // Swipe right - open sidebar
          openSidebar();
        } else {
          // Swipe left - close sidebar if it's open
          if (isSidebarVisible) {
            closeSidebar();
          }
        }
      }
    },
    onPanResponderTerminationRequest: () => false, // Don't allow termination once we have it
    onShouldBlockNativeResponder: () => true, // Block native responders only when we have the gesture
  });

  const startRefreshAnimation = () => {
    refreshRotation.setValue(0);
    Animated.loop(
      Animated.timing(refreshRotation, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    ).start();
  };

  const stopRefreshAnimation = () => {
    refreshRotation.stopAnimation();
    refreshRotation.setValue(0);
  };

  // Format course name: what's after the dash is the main name, what's between dashes is the code
  const formatCourseName = (courseName) => {
    if (!courseName) return { main: '', code: '' };

    // Find all dashes in the course name
    const dashIndices = [];
    for (let i = 0; i < courseName.length; i++) {
      if (courseName.substring(i, i + 2) === '- ') {
        dashIndices.push(i);
      }
    }

    if (dashIndices.length >= 2) {
      // Extract what's between the first and second dash as code
      const code = courseName.substring(dashIndices[0] + 2, dashIndices[1]).trim();
      // Extract what's after the second dash as main name
      const main = courseName.substring(dashIndices[1] + 2).trim();
      return { main: main, code: code };
    } else if (dashIndices.length === 1) {
      // If only one dash, everything after it is the main name
      const main = courseName.substring(dashIndices[0] + 2).trim();
      return { main: main, code: '' };
    }

    return { main: courseName, code: '' };
  };

  // Handle dropdown option selection
  const handleCourseSelect = async (option) => {
    const formatted = formatCourseName(option.text);
    setSelectedCourse(formatted);
    setIsDropdownVisible(false);
    setIsLoadingAttendance(true);
    setIsLoadingWarnings(true);
    setAttendanceData([]); // Clear previous attendance
    setAttendanceWarnings([]); // Clear previous warnings
    console.log('📊 Course selected:', formatted.main, formatted.code);

    // Check for demo mode
    const isDemoMode = await AsyncStorage.getItem('is_demo_user');
    if (isDemoMode === 'true') {
      console.log('🎭 Demo mode - loading demo attendance for course:', option.text);

      const demoData = getDemoAttendanceData(option.text);

      // Simulate loading delay
      setTimeout(() => {
        setAttendanceData(demoData.attendanceData);
        setAttendanceWarnings(demoData.attendanceWarnings);
        setIsLoadingAttendance(false);
        setIsLoadingWarnings(false);
        console.log('🎭 Demo attendance loaded:', demoData.attendanceData.length, 'records');
      }, 1000);

      return;
    }

    console.log('📊 About to trigger course selection in WebView with value:', option.value);

    // Inject JavaScript to set the dropdown value and trigger change
    selectCourseInWebView(option.value);
  };

  // Toggle dropdown visibility
  const toggleDropdown = () => {
    setIsDropdownVisible(!isDropdownVisible);
  };

  // Format date from YYYY.MM.DD to readable format with day name
  const formatDateWithDay = (dateString) => {
    if (!dateString) return 'Unknown Date';

    try {
      // Convert YYYY.MM.DD to YYYY-MM-DD for proper Date parsing
      const formattedDateString = dateString.replace(/\./g, '-');
      const date = new Date(formattedDateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        return dateString; // Return original if parsing fails
      }

      const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

      const dayName = days[date.getDay()];
      const day = date.getDate();
      const month = months[date.getMonth()];
      const year = date.getFullYear();

      return `${dayName}, ${day} ${month} ${year}`;
    } catch (error) {
      console.error('Error formatting date:', error);
      return dateString;
    }
  };

  // Sort attendance data by row number (descending order - highest first)
  const sortAttendanceByRowNumber = (data) => {
    return [...data].sort((a, b) => {
      const rowA = parseInt(a.rowNumber) || 0;
      const rowB = parseInt(b.rowNumber) || 0;
      return rowB - rowA; // Descending order (5, 4, 3, 2, 1...)
    });
  };

  // Extract session type from session description
  const getSessionType = (sessionDescription) => {
    if (!sessionDescription) return '';

    const sessionTypes = ['Tutorial', 'Lecture', 'Lab', 'Practical'];
    const lowerDesc = sessionDescription.toLowerCase();

    for (const type of sessionTypes) {
      if (lowerDesc.includes(type.toLowerCase())) {
        return type;
      }
    }

    return '';
  };

  // Format slot number with ordinal suffix
  const formatSlotNumber = (slotNumber) => {
    if (!slotNumber) return 'Unknown Slot';

    const num = parseInt(slotNumber);
    let suffix = 'th';

    if (num % 10 === 1 && num % 100 !== 11) suffix = 'st';
    else if (num % 10 === 2 && num % 100 !== 12) suffix = 'nd';
    else if (num % 10 === 3 && num % 100 !== 13) suffix = 'rd';

    return `${num}${suffix} Slot`;
  };

  // Count total absences
  const getTotalAbsences = (data) => {
    return data.filter(record => record.attendance === 'Absent').length;
  };

  // Ghost Loading Component for Attendance
  const AttendanceGhostCard = ({ index }) => {
    const shimmerAnim = useRef(new Animated.Value(0)).current;

    useEffect(() => {
      const shimmerAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(shimmerAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(shimmerAnim, {
            toValue: 0,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      shimmerAnimation.start();

      return () => shimmerAnimation.stop();
    }, []);

    const shimmerOpacity = shimmerAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0.3, 0.7],
    });

    return (
      <View style={styles.attendanceCard}>
        {/* Header: Date and Row Number */}
        <View style={styles.cardHeader}>
          <Animated.View style={[
            styles.ghostText,
            styles.ghostDate,
            { opacity: shimmerOpacity }
          ]} />
          <Animated.View style={[
            styles.ghostText,
            styles.ghostRowNumber,
            { opacity: shimmerOpacity }
          ]} />
        </View>

        {/* Content: Session Type, Status, and Slot - Horizontal Layout */}
        <View style={styles.cardContent}>
          <Animated.View style={[
            styles.ghostText,
            styles.ghostSessionType,
            { opacity: shimmerOpacity }
          ]} />
          <Animated.View style={[
            styles.ghostText,
            styles.ghostStatus,
            { opacity: shimmerOpacity }
          ]} />
          <Animated.View style={[
            styles.ghostText,
            styles.ghostSlot,
            { opacity: shimmerOpacity }
          ]} />
        </View>
      </View>
    );
  };

  // AttendanceCard component
  const AttendanceCard = ({ record }) => {
    const isAttended = record.attendance === 'Attended';
    const formattedDate = formatDateWithDay(record.date);
    const formattedSlot = formatSlotNumber(record.slot);
    const sessionType = getSessionType(record.sessionDescription);

    return (
      <View style={[
        styles.attendanceCard,
        isAttended ? styles.attendanceCardAttended : styles.attendanceCardAbsent
      ]}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardDate}>{formattedDate}</Text>
          <Text style={styles.cardRowNumber}>#{record.rowNumber}</Text>
        </View>

        <View style={styles.cardContent}>
          <Text style={styles.cardSessionType}>{sessionType}</Text>
          <Text style={[
            styles.attendanceStatus,
            isAttended ? styles.attendanceStatusAttended : styles.attendanceStatusAbsent
          ]}>
            {record.attendance}
          </Text>
          <Text style={styles.cardSlot}>{formattedSlot}</Text>
        </View>
      </View>
    );
  };

  // Function to select course in WebView and wait for results
  const selectCourseInWebView = (courseValue) => {
    if (webViewRef.current) {
      console.log('🔄 Phase 1: Setting course value in WebView:', courseValue);

      // Phase 1: Select dropdown and dispatch events
      const phase1JS = `
        (function() {
          try {
            // Set the value of the dropdown
            const dropdown = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_DDL_Courses');

            if (dropdown) {
              dropdown.value = '${courseValue}';

              // Trigger multiple events to ensure it works
              dropdown.dispatchEvent(new Event('change', { bubbles: true }));
              dropdown.dispatchEvent(new Event('input', { bubbles: true }));

              // Notify React Native that phase 1 is complete
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'phase1_complete',
                message: 'Dropdown selection completed, waiting for page reload...'
              }));
            } else {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'course_result_error',
                error: 'Dropdown not found'
              }));
            }
          } catch (error) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'course_result_error',
              error: error.message
            }));
          }
        })();
        true;
      `;

      // Inject Phase 1 code
      webViewRef.current.injectJavaScript(phase1JS);

      // Wait for iOS compatibility (optimized from 4s to 2s)
      safeSetTimeout(() => {
        console.log('🔄 Phase 2: Extracting attendance data after delay (iOS compatible)...');
        injectPhase2ExtractionCode();
      }, 2000); // Optimized from 4 seconds to 2 seconds
    }
  };

  // Phase 2: Extract attendance data from the newly loaded page
  const injectPhase2ExtractionCode = () => {
    if (webViewRef.current) {
      const phase2JS = `
        (function() {
          try {
            // Find the attendance table by ID
            const attendanceTable = document.getElementById('DG_StudentCourseAttendance');

            if (attendanceTable) {
              console.log('✅ Attendance table found');

              // Get all rows except the header row
              const rows = attendanceTable.querySelectorAll('tbody tr');
              const attendanceData = [];

              // Skip the first row (header) and process data rows
              for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.querySelectorAll('td');

                if (cells.length >= 3) {
                  const rowNumber = cells[0].textContent.trim();
                  const attendance = cells[1].textContent.trim(); // "Attended" or "Absent"
                  const sessionDesc = cells[2].textContent.trim(); // Full session description

                  // Extract slot information (look for "Slot" followed by number)
                  const slotMatch = sessionDesc.match(/Slot(\\d+)/);
                  const slot = slotMatch ? slotMatch[1] : null;

                  // Extract date (look for @ followed by date)
                  const dateMatch = sessionDesc.match(/@([0-9]{4}\\.[0-9]{2}\\.[0-9]{2})/);
                  const date = dateMatch ? dateMatch[1] : null;

                  // Create attendance record
                  const attendanceRecord = {
                    rowNumber: rowNumber,
                    attendance: attendance,
                    sessionDescription: sessionDesc,
                    slot: slot,
                    date: date
                  };

                  attendanceData.push(attendanceRecord);
                  console.log('📊 Attendance record:', attendanceRecord);
                }
              }

              console.log('📊 Total attendance records extracted:', attendanceData.length);

              // Send the extracted data back to React Native
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'attendance_result',
                data: attendanceData,
                message: 'Attendance data extracted successfully'
              }));

            } else {
              console.log('❌ Attendance table not found');
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'course_result_error',
                error: 'Attendance table (DG_StudentCourseAttendance) not found'
              }));
            }
          } catch (error) {
            console.log('❌ Error in Phase 2:', error);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'course_result_error',
              error: error.message
            }));
          }
        })();
        true;
      `;

      webViewRef.current.injectJavaScript(phase2JS);
    }
  };

  // Save data to cache
  const saveCacheData = async (dropdownData = null, attendanceRecords = null, selectedCourseData = null, warningsData = null) => {
    try {
      const cacheData = {
        timestamp: Date.now(),
        dropdownOptions: dropdownData || dropdownOptions,
        attendanceData: attendanceRecords || attendanceData,
        selectedCourse: selectedCourseData || selectedCourse,
        attendanceWarnings: warningsData || attendanceWarnings,
      };

      await AsyncStorage.setItem('attendance_cache', JSON.stringify(cacheData));
      console.log('💾 Attendance data cached successfully');
    } catch (error) {
      console.log('❌ Error saving attendance cache:', error);
    }
  };

  const handleRefresh = async () => {
    if (isRefreshing) return;

    console.log('🔄 Refreshing attendance data...');

    // Check if a course is selected
    if (selectedCourse.main === 'Select Course' || !selectedCourse.main) {
      console.log('⚠️ No course selected, nothing to refresh');
      return;
    }

    setIsRefreshing(true);
    setIsLoadingWarnings(true);
    startRefreshAnimation();

    try {
      // Find the selected course option from dropdownOptions
      const selectedOption = dropdownOptions.find(option => {
        const formatted = formatCourseName(option.text);
        return formatted.main === selectedCourse.main && formatted.code === selectedCourse.code;
      });

      if (selectedOption) {
        console.log('🔄 Re-fetching attendance for selected course:', selectedCourse.main);
        // Re-trigger the course selection process to fetch fresh attendance data
        selectCourseInWebView(selectedOption.value);
      } else {
        console.log('⚠️ Selected course not found in dropdown options');
        setIsRefreshing(false);
        stopRefreshAnimation();
        // Alert.alert('Error', 'Unable to refresh. Please reselect the course.');
      }

    } catch (error) {
      console.error('❌ Error refreshing attendance:', error);
      setIsRefreshing(false);
      stopRefreshAnimation();
      // Alert.alert('Error', 'Failed to refresh attendance data. Please try again.');
    }
  };

  // Load cached attendance data
  const loadCachedData = async () => {
    try {
      const cachedData = await AsyncStorage.getItem('attendance_cache');
      if (cachedData) {
        const parsed = JSON.parse(cachedData);
        const cacheAge = Date.now() - parsed.timestamp;
        const maxCacheAge = 24 * 60 * 60 * 1000; // 24 hours

        if (cacheAge < maxCacheAge) {
          console.log('✅ Loading attendance from cache (age:', Math.round(cacheAge / 1000 / 60), 'minutes)');

          if (parsed.dropdownOptions) {
            setDropdownOptions(parsed.dropdownOptions);
            setIsDropdownLoading(false);
          }

          if (parsed.attendanceData) {
            setAttendanceData(parsed.attendanceData);
          }

          if (parsed.selectedCourse) {
            setSelectedCourse(parsed.selectedCourse);
          }

          if (parsed.attendanceWarnings) {
            console.log('📊 Loading attendance warnings from cache:', parsed.attendanceWarnings.length, 'warnings');
            console.log('📊 Cached warnings data:', parsed.attendanceWarnings);
            setAttendanceWarnings(parsed.attendanceWarnings);
          } else {
            console.log('ℹ️ No attendance warnings found in cache');
          }

          setHasCachedData(true);
          return true;
        }
      }
      return false;
    } catch (error) {
      console.log('❌ Error loading cached attendance data:', error);
      return false;
    }
  };

  const loadCredentialsAndSetupAttendance = async () => {
    try {
      console.log('🔄 Loading credentials for attendance...');

      // Check for demo mode
      const demoHandled = await handleDemoModeInit(
        setIsLoading,
        (demoData) => {
          setDropdownOptions(demoData.dropdownOptions);
          setAttendanceData([]);
          setAttendanceWarnings(demoData.attendanceWarnings);
        },
        getDemoAttendanceData()
      );

      if (demoHandled) {
        setIsDropdownLoading(false);
        return;
      }

      // Check if we're in offline mode
      if (isOfflineMode) {
        console.log('📴 Offline mode detected - loading cached attendance courses');

        try {
          const cachedCourses = await getCachedAttendanceCourses();
          console.log('📦 Cached attendance courses:', cachedCourses);

          if (cachedCourses.length > 0) {
            // Convert cached course codes to dropdown format
            const offlineDropdownOptions = cachedCourses.map(courseCode => ({
              value: courseCode,
              text: courseCode // We'll format this properly when displaying
            }));

            setDropdownOptions(offlineDropdownOptions);
            setIsDropdownLoading(false);
            setIsLoading(false);
            setAttendanceUrl('offline://attendance');

            console.log('📴 Offline attendance setup complete');
            return;
          } else {
            console.log('📭 No cached attendance found in offline mode');
            setDropdownOptions([]);
            setIsDropdownLoading(false);
            setIsLoading(false);
            return;
          }
        } catch (error) {
          console.log('❌ Error loading cached attendance courses:', error);
          setDropdownOptions([]);
          setIsDropdownLoading(false);
          setIsLoading(false);
          return;
        }
      }

      // Load cached data first
      const hasCached = await loadCachedData();

      // Clear WebView session before setting up attendance
      await clearWebViewSession();

      // Get stored credentials from localStorage
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('❌ No stored credentials found');
        // Alert.alert(
        //   'Authentication Required',
        //   'Please login first to access attendance.',
        //   [
        //     {
        //       text: 'Go to Login',
        //       onPress: () => navigation.navigate('Login')
        //     }
        //   ]
        // );
        return;
      }

      console.log('✅ Credentials loaded, setting up attendance URL...');
      setCredentials({ username: storedUsername, password: storedPassword });

      // Create URL with embedded credentials - properly encode username and password
      const encodedUsername = encodeURIComponent(storedUsername);
      const encodedPassword = encodeURIComponent(storedPassword);
      const urlWithCredentials = `https://${encodedUsername}:${encodedPassword}@apps.guc.edu.eg/student_ext/Attendance/ClassAttendance_ViewStudentAttendance_001.aspx`;
      setAttendanceUrl(urlWithCredentials);

      console.log('🎯 Attendance URL prepared');
      setIsLoading(false);

      // If we have cached data, start background refresh
      if (hasCached) {
        console.log('🔄 Starting background refresh...');
        // Don't set loading states, just refresh in background
      }

    } catch (error) {
      console.error('❌ Error loading credentials:', error);
      Alert.alert(
        'Error',
        'Failed to load credentials. Please try again.',
        [
          {
            text: 'Go Back',
            onPress: () => navigation.goBack()
          }
        ]
      );
    }
  };

  const handleWebViewLoad = () => {
    console.log('📊 Attendance page loaded successfully');

    // Set loading state for warnings extraction
    setIsLoadingWarnings(true);

    // Add a timeout fallback in case extraction fails (optimized progressive timeouts)
    safeSetTimeout(() => {
      if (isDropdownLoading) {
        console.log('⏰ Dropdown loading timeout, stopping loading state');
        safeSetState(setIsDropdownLoading, false, 'isDropdownLoading');
        safeSetState(setIsLoadingWarnings, false, 'isLoadingWarnings');
      }
    }, 8000); // Optimized from 15 seconds to 8 seconds

    // Start dropdown extraction with retry logic
    attemptDropdownExtraction(0);
  };

  // Dropdown extraction with retry logic for iOS compatibility
  const attemptDropdownExtraction = (attemptNumber) => {
    const maxAttempts = 5;
    // Progressive delays: 1s, 2s, 4s, 6s, 8s
    const delays = [1000, 2000, 4000, 6000, 8000];
    const delay = delays[attemptNumber] || 8000;

    safeSetTimeout(() => {
      console.log(`📊 Dropdown extraction attempt ${attemptNumber + 1}/${maxAttempts}`);

      const jsCode = `
        (function() {
          try {
            // Log all select elements on the page for debugging
            const allSelects = document.querySelectorAll('select');
            console.log('Found', allSelects.length, 'select elements on page');

            const dropdown = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_DDL_Courses');
            if (dropdown) {
              console.log('✅ Dropdown found on attempt ${attemptNumber + 1}');
              const options = [];
              for (let i = 0; i < dropdown.options.length; i++) {
                const option = dropdown.options[i];
                // Skip placeholder options like "Choose a course" or empty values
                if (option.value && option.value.trim() !== '' &&
                    !option.text.toLowerCase().includes('choose') &&
                    !option.text.toLowerCase().includes('select')) {
                  options.push({
                    value: option.value,
                    text: option.text
                  });
                }
              }
              // Extract attendance warnings from DG_AbsenceReport table
              console.log('🔍 Looking for attendance warnings table with ID: DG_AbsenceReport');
              const warningsTable = document.getElementById('DG_AbsenceReport');
              const warningsData = [];

              if (warningsTable) {
                console.log('✅ Attendance warnings table found!');
                console.log('📋 Table HTML:', warningsTable.outerHTML.substring(0, 500) + '...');

                const warningRows = warningsTable.querySelectorAll('tbody tr');
                console.log('📊 Total rows in warnings table:', warningRows.length);

                // Skip the first row (header) and process data rows
                for (let i = 1; i < warningRows.length; i++) {
                  const row = warningRows[i];
                  const cells = row.querySelectorAll('td');
                  console.log('📊 Row', i, 'has', cells.length, 'cells');

                  if (cells.length >= 4) {
                    const settingsTitle = cells[0].textContent.trim();
                    const code = cells[1].textContent.trim();
                    const name = cells[2].textContent.trim();
                    const absenceLevel = parseInt(cells[3].textContent.trim());

                    console.log('📊 Raw cell data:', {
                      cell0: cells[0].textContent,
                      cell1: cells[1].textContent,
                      cell2: cells[2].textContent,
                      cell3: cells[3].textContent
                    });

                    const warningRecord = {
                      settingsTitle: settingsTitle,
                      code: code,
                      name: name,
                      absenceLevel: absenceLevel
                    };

                    warningsData.push(warningRecord);
                    console.log('⚠️ Warning record added:', warningRecord);
                  } else {
                    console.log('⚠️ Row', i, 'skipped - insufficient cells:', cells.length);
                  }
                }

                console.log('⚠️ Total warning records extracted:', warningsData.length);
                console.log('⚠️ Final warnings data:', warningsData);
              } else {
                console.log('❌ No attendance warnings table found with ID: DG_AbsenceReport');
                console.log('🔍 Available tables on page:');
                const allTables = document.querySelectorAll('table');
                allTables.forEach((table, index) => {
                  console.log('Table', index, '- ID:', table.id, 'Class:', table.className);
                });
              }

              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'dropdown_options',
                options: options,
                warnings: warningsData,
                attempt: ${attemptNumber + 1}
              }));
            } else {
              console.log('❌ Dropdown not found on attempt ${attemptNumber + 1}');
              // Try to find it by other means
              const allElements = document.querySelectorAll('[id*="DDL_Courses"]');
              console.log('Found', allElements.length, 'elements with DDL_Courses in ID');
              allElements.forEach((el, index) => {
                console.log('Element', index, 'ID:', el.id, 'Tag:', el.tagName);
              });

              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'dropdown_retry_needed',
                attempt: ${attemptNumber + 1},
                maxAttempts: ${maxAttempts}
              }));
            }
          } catch (error) {
            console.log('❌ Error extracting dropdown on attempt ${attemptNumber + 1}:', error);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'dropdown_retry_needed',
              attempt: ${attemptNumber + 1},
              maxAttempts: ${maxAttempts},
              error: error.message
            }));
          }
        })();
      `;

      if (webViewRef.current) {
        webViewRef.current.injectJavaScript(jsCode);
      }
    }, delay);
  };

  const handleWebViewMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('📨 Attendance WebView message received:', data.type);

      switch (data.type) {
        case 'dropdown_options':
          console.log(`📋 Dropdown options received on attempt ${data.attempt || 1}:`, data.options.length, 'courses');
          safeSetState(setDropdownOptions, data.options, 'dropdownOptions');
          safeSetState(setIsDropdownLoading, false, 'isDropdownLoading');

          // Process warnings data
          const phase1Warnings = data.warnings || [];
          console.log('⚠️ Attendance warnings received in Phase 1:', phase1Warnings.length, 'warnings');
          console.log('⚠️ Raw warnings data from WebView:', data.warnings);
          console.log('⚠️ Processed warnings array:', phase1Warnings);

          if (phase1Warnings.length > 0) {
            phase1Warnings.forEach((warning, index) => {
              console.log(`⚠️ Warning ${index + 1}:`, {
                settingsTitle: warning.settingsTitle,
                code: warning.code,
                name: warning.name,
                absenceLevel: warning.absenceLevel
              });
            });
          } else {
            console.log('ℹ️ No attendance warnings found on the page');
          }

          console.log('📊 Setting attendance warnings state with:', phase1Warnings.length, 'warnings');
          safeSetState(setAttendanceWarnings, phase1Warnings, 'attendanceWarnings');
          safeSetState(setIsLoadingWarnings, false, 'isLoadingWarnings');

          // Save dropdown options and warnings to cache
          console.log('💾 Saving to cache - warnings:', phase1Warnings.length);
          saveCacheData(data.options, null, null, phase1Warnings);
          break;

        case 'dropdown_retry_needed':
          console.log(`🔄 Dropdown not found on attempt ${data.attempt}/${data.maxAttempts}, retrying...`);
          if (data.attempt < data.maxAttempts) {
            // Retry after 1 second
            attemptDropdownExtraction(data.attempt);
          } else {
            console.error('❌ Dropdown extraction failed after all attempts');
            setIsDropdownLoading(false);
            Alert.alert(
              'Error',
              'Failed to load course options after multiple attempts. Please refresh and try again.',
              [
                {
                  text: 'Refresh',
                  onPress: () => {
                    if (webViewRef.current && attendanceUrl) {
                      setIsDropdownLoading(true);
                      webViewRef.current.reload();
                    }
                  }
                },
                { text: 'Cancel' }
              ]
            );
          }
          break;

        case 'dropdown_error':
          console.error('❌ Dropdown extraction error:', data.error);
          setIsDropdownLoading(false);
          Alert.alert('Error', 'Failed to load course options. Please refresh and try again.');
          break;

        case 'phase1_complete':
          console.log('✅ Phase 1 complete:', data.message);
          break;

        case 'attendance_result':
          console.log('📊 Attendance data received:', data.data.length, 'records');
          console.log('📊 Full attendance data:', data.data);

          // Log each attendance record for debugging
          data.data.forEach((record, index) => {
            console.log(`📊 Record ${index + 1}:`, {
              rowNumber: record.rowNumber,
              attendance: record.attendance,
              slot: record.slot,
              date: record.date,
              sessionDescription: record.sessionDescription
            });
          });

          setAttendanceData(data.data);
          setIsLoadingAttendance(false);
          setIsRefreshing(false);
          stopRefreshAnimation();

          // Save attendance data to cache (warnings are already cached from Phase 1)
          saveCacheData(null, data.data, selectedCourse, null);
          break;

        case 'course_result_error':
          console.error('❌ Course selection error:', data.error);
          setIsLoadingAttendance(false);
          stopRefreshAnimation();
          Alert.alert('Error', 'Failed to load attendance data. Please try again.');
          break;

        default:
          console.log('📨 Unknown message type:', data.type);
      }
    } catch (error) {
      console.error('❌ Error parsing WebView message:', error);
    }
  };

  const handleWebViewError = (error) => {
    console.error('❌ Attendance WebView error:', error);
    setIsLoadingAttendance(false);
    stopRefreshAnimation();
    Alert.alert('Error', 'Failed to load attendance page. Please check your connection and try again.');
  };

  if (isLoading) {
    return (
      <ThemeTransitionWrapper>
        <SafeAreaView style={styles.safeAreaTop} edges={['top']}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Loading attendance...</Text>
          </View>
        </SafeAreaView>
      </ThemeTransitionWrapper>
    );
  }

  return (
    <ThemeTransitionWrapper>
      <View style={{ flex: 1 }} {...swipeGestureHandler.panHandlers}>
        <SafeAreaView style={styles.safeAreaTop} edges={['top']}>
      <View style={styles.container}>
        {/* Sidebar Button */}
        <View style={styles.sidebarButtonContainer} pointerEvents="box-none">
          <TouchableOpacity style={styles.sidebarButton} onPress={openSidebar}>
            <HamburgerIcon size={20} color={theme.colors.primary} strokeWidth={3} />
          </TouchableOpacity>
        </View>

        {/* Attendance Title */}
        <View style={styles.attendanceTitleContainer} pointerEvents="box-none">
          <Text style={styles.attendanceTitle}>Attendance</Text>
        </View>

        {/* Refresh Button */}
        <View style={styles.refreshButtonContainer} pointerEvents="box-none">
          <TouchableOpacity
            style={[
              styles.refreshButton,
              isRefreshing && styles.refreshButtonLoading,
              isOfflineMode && styles.refreshButtonDisabled
            ]}
            onPress={isOfflineMode ? undefined : handleRefresh}
            disabled={isRefreshing || isOfflineMode}
            activeOpacity={isOfflineMode ? 0.3 : 0.7}
          >
            <Animated.View
              style={{
                transform: [{
                  rotate: isOfflineMode ? '0deg' : refreshRotation.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0deg', '360deg']
                  })
                }]
              }}
            >
              <RefreshIcon
                size={24}
                color={isOfflineMode ? '#808080' : isRefreshing && safeCurrentThemeName !== 'colorful' ? '#9CA3AF' : safeCurrentThemeName === 'navy' ? '#DC2626' : '#f1c40f'}
              />
            </Animated.View>
          </TouchableOpacity>
        </View>

        {/* Main Content */}
        <TouchableOpacity
          style={styles.mainContent}
          activeOpacity={1}
          onPress={() => {
            if (isDropdownVisible) {
              console.log('🎯 Main content pressed - closing dropdown');
              setIsDropdownVisible(false);
            }
          }}
        >
          {!isLoading && (
            <>
              {/* Course Selection Section */}
              <View style={styles.dropdownSection}>
                <Text style={styles.sectionTitle}>Course Selection</Text>

                {/* Course Dropdown */}
                <View style={styles.dropdownContainer}>
                  <Text style={styles.dropdownLabel}>Select Course:</Text>
                  <View style={styles.dropdownWrapper}>
                    <TouchableOpacity
                      style={[styles.dropdown, isDropdownLoading && styles.dropdownDisabled]}
                      onPress={toggleDropdown}
                      disabled={isDropdownLoading}
                    >
                      <View style={styles.dropdownContent}>
                        {selectedCourse && selectedCourse.main && selectedCourse.main !== 'Select Course' ? (
                          <View style={styles.selectedCourseContainer}>
                            <Text style={styles.dropdownText}>{selectedCourse.main}</Text>
                            <Text style={styles.courseCode}>{selectedCourse.code}</Text>
                          </View>
                        ) : (
                          <Text style={styles.dropdownPlaceholder}>Select Course</Text>
                        )}
                      </View>
                      <Text style={[
                        styles.dropdownArrow,
                        { transform: [{ rotate: isDropdownVisible ? '180deg' : '0deg' }] }
                      ]}>
                        ▼
                      </Text>
                    </TouchableOpacity>

{/* Dropdown Options List - Rendered at end for proper layering */}
        {isDropdownVisible && dropdownOptions.length > 0 && (
          <View style={styles.dropdownListFloating}>
            <ScrollView
              style={styles.optionsList}
              showsVerticalScrollIndicator={false}
              nestedScrollEnabled={true}
              keyboardShouldPersistTaps="handled"
            >
              {dropdownOptions.map((option, index) => {
                const formatted = formatCourseName(option.text);
                return (
                  <TouchableOpacity
                    key={index}
                    style={styles.optionItem}
                    onPress={() => handleCourseSelect(option)}
                  >
                    <View style={styles.optionContent}>
                      <Text style={styles.optionText}>{formatted.main}</Text>
                      <Text style={styles.optionCode}>{formatted.code}</Text>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>
        )}
                          </View>

                  {/* Loading/Status Text */}
                  {isDropdownLoading && (
                    <View style={styles.statusLoadingContainer}>
                      <ActivityIndicator size="small" color={theme.colors.textSecondary} />
                      <Text style={styles.dropdownLoadingText}>Loading courses...</Text>
                    </View>
                  )}

                  {dropdownOptions.length > 0 && !isDropdownLoading && (
                    <Text style={styles.optionsCount}>
                      {dropdownOptions.length} {dropdownOptions.length === 1 ? 'course' : 'courses'} available
                    </Text>
                  )}
                </View>
              </View>

              {/* Attendance Display Section */}
              {isLoadingAttendance ? (
                <View style={styles.attendanceDisplaySection}>
                  {/* Fixed Header with Title and Ghost Total Absences */}
                  <View style={styles.attendanceHeader}>
                    <Text style={styles.attendanceDisplayTitle}>Attendance Records</Text>
                    <View style={[styles.ghostText, styles.ghostTotalAbsences]} />
                  </View>

                  {/* Scrollable Ghost Cards Container */}
                  <ScrollView
                    style={styles.attendanceCardsContainer}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled"
                    contentContainerStyle={styles.attendanceCardsContentContainer}
                  >
                    {Array.from({ length: 10 }, (_, index) => (
                      <AttendanceGhostCard key={`ghost-${index}`} index={index} />
                    ))}
                  </ScrollView>
                </View>
              ) : attendanceData.length > 0 ? (
                <View style={styles.attendanceDisplaySection}>
                  {/* Fixed Header with Title and Total Absences */}
                  <View style={styles.attendanceHeader}>
                    <Text style={styles.attendanceDisplayTitle}>Attendance Records</Text>
                    <Text style={styles.totalAbsencesText}>
                      Total Absences: {getTotalAbsences(attendanceData)}
                    </Text>
                  </View>

                  {/* Scrollable Cards Container - Flex:1 to fill available space */}
                  <ScrollView
                    style={styles.attendanceCardsContainer}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps="handled"
                    contentContainerStyle={styles.attendanceCardsContentContainer}
                  >
                    {sortAttendanceByRowNumber(attendanceData).map((record, index) => (
                      <AttendanceCard key={index} record={record} />
                    ))}
                  </ScrollView>
                </View>
              ) : selectedCourse.main !== 'Select Course' ? (
                <View style={styles.noDataContainer}>
                  <Text style={styles.noDataText}>No attendance data found</Text>
                  <Text style={styles.noDataSubtext}>Try selecting a different course</Text>
                </View>
              ) : (
                <View style={styles.summarySection}>
                  <View style={styles.summaryCard}>
                    <Text style={styles.summaryTitle}>Attendance Summary</Text>
                    {isLoadingWarnings ? (
                      <ActivityIndicator size="small" color={theme.colors.primary} style={{ marginTop: 10 }} />
                    ) : attendanceWarnings.length > 0 ? (
                      <View style={styles.warningsContainer}>
                        {attendanceWarnings.map((warning, index) => (
                          <View key={index} style={styles.warningItem}>
                            <View style={styles.warningContent}>
                              <View style={styles.warningLeft}>
                                <Text style={styles.warningCourseName}>{warning.name}</Text>
                                <Text style={styles.warningCourseCode}>{warning.code}</Text>
                              </View>
                              <View style={styles.warningRight}>
                                <Text style={[
                                  styles.warningLevel,
                                  { color: getWarningLevelColor(warning.absenceLevel) }
                                ]}>
                                  Warning Level {warning.absenceLevel}
                                </Text>
                              </View>
                            </View>
                          </View>
                        ))}
                      </View>
                    ) : (
                      <Text style={styles.noWarningsText}>You don't have attendance warnings!</Text>
                    )}
                  </View>
                </View>
              )}
            </>
          )}
        </TouchableOpacity>

        {/* Hidden WebView for data extraction */}
        <View style={styles.hiddenWebView}>
          <WebView
            ref={webViewRef}
            source={{ uri: attendanceUrl }}
            onLoad={handleWebViewLoad}
            onMessage={handleWebViewMessage}
            onError={handleWebViewError}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            mixedContentMode="compatibility"
            cacheEnabled={false}
            incognito={true}
          />
        </View>
      </View>


      {/* Sidebar Component */}
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={closeSidebar}
        sidebarAnim={sidebarAnim}
        navigation={navigation}
        currentScreen="Attendance"
      />
    </SafeAreaView>
    </View>
    </ThemeTransitionWrapper>
  );
};

// Create styles function that uses theme
const createStyles = (theme, safeCurrentThemeName) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  safeAreaTop: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  attendanceTitleContainer: {
    position: 'absolute',
    top: 25, // Raised higher
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 10,
  },
  attendanceTitle: {
    fontSize: 30, // Increased font size slightly
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
  },
  sidebarButtonContainer: {
    position: 'absolute',
    top: 20,
    left: 20,
    zIndex: 10,
  },
  sidebarButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  refreshButtonContainer: {
    position: 'absolute',
    top: 20,
    right: 20,
    zIndex: 10,
  },
  refreshButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  refreshButtonLoading: {
    backgroundColor: safeCurrentThemeName === 'colorful' ? theme.colors.surface : '#666666',
  },
  refreshButtonDisabled: {
    opacity: 0.4,
    backgroundColor: theme.colors.surface + '80',
  },
  mainContent: {
    flex: 1,
    paddingTop: 90,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 15,
  },
  dropdownSection: {
    backgroundColor: theme.colors.surface,
    borderRadius: 15,
    padding: 18,
    marginBottom: 16,
    marginHorizontal: Math.max(12, Dimensions.get('window').width * 0.03),
    width: '95%',
    alignSelf: 'center',
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : theme.colors.border,
    zIndex: 9998,
    elevation: 9998,
    overflow: 'visible',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : theme.colors.primary,
    marginBottom: 16,
    textAlign: 'center',
  },
  dropdownContainer: {
    marginBottom: 12,
    overflow: 'visible',
  },
  dropdownLabel: {
    fontSize: 16,
    color: theme.colors.text,
    marginBottom: 8,
    fontWeight: '600',
  },
  dropdownLoadingText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    marginLeft: 8,
  },
  dropdownWrapper: {
    position: 'relative',
    zIndex: 9999,
    elevation: 9999,
  },
  dropdown: {
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: 10,
    paddingVertical: 18,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    minHeight: 55,
  },
  dropdownContent: {
    flex: 1,
    justifyContent: 'center',
    minHeight: 20,
  },
  selectedCourseContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: theme.colors.text,
    flex: 1,
    textAlignVertical: 'center',
  },
  dropdownPlaceholder: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    flex: 1,
    fontStyle: 'italic',
    textAlignVertical: 'center',
  },
  statusLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 5,
  },
  dropdownDisabled: {
    opacity: 0.6,
    borderColor: '#666666',
  },
  courseCode: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    marginLeft: 10,
  },
  dropdownArrow: {
    fontSize: 14,
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    marginLeft: 15,
    textAlignVertical: 'center',
  },
  dropdownList: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: theme.colors.surfaceVariant,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    borderTopWidth: 0,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    maxHeight: 200,
    zIndex: 101,
    elevation: 101,
  },
  dropdownListFloating: {
    position: 'absolute',
    marginTop: 60,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.surface,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    borderTopWidth: 0,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    maxHeight: 200,
    zIndex: 99999,
    elevation: 99999,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.44,
    shadowRadius: 10.32,
  },
  optionsCount: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 5,
    marginLeft: 8,
    fontStyle: 'italic',
  },
  optionsList: {
    flex: 1,
    overflow: 'hidden', // Clip content to container bounds
    borderBottomLeftRadius: 10, // Add bottom border radius to container
    borderBottomRightRadius: 10, // Add bottom border radius to container
  },
  optionItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.surface, // Added to ensure option items have solid background
  },
  optionItemLast: {
    padding: 15,
    borderBottomWidth: 0, // Remove bottom border for last item
    backgroundColor: theme.colors.surface, // Added to ensure option items have solid background
    borderBottomLeftRadius: 10, // Add bottom left radius
    borderBottomRightRadius: 10, // Add bottom right radius
  },
  optionContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  optionText: {
    fontSize: 16,
    color: theme.colors.text,
    fontWeight: '600',
    flex: 1,
  },
  optionCode: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    marginLeft: 10,
  },
  dropdownLoadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginLeft: 8,
    fontStyle: 'italic',
  },
  dropdownOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 50,
    elevation: 50,
  },
  attendanceLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  // Ghost Loading Styles for Attendance
  ghostText: {
    backgroundColor: theme.colors.border,
    borderRadius: 4,
  },
  ghostDate: {
    height: 16,
    width: 120, // Fixed width for date
  },
  ghostRowNumber: {
    height: 16,
    width: 40, // Fixed width for row number
  },
  ghostSessionType: {
    height: 14,
    width: 60, // Fixed width for left element
  },
  ghostStatus: {
    height: 18,
    width: 70, // Fixed width for center element
  },
  ghostSlot: {
    height: 14,
    width: 50, // Fixed width for right element
  },
  ghostTotalAbsences: {
    height: 16,
    width: '45%',
  },
  attendanceDisplaySection: {
    flex: 1,
    marginHorizontal: Math.max(12, Dimensions.get('window').width * 0.03),
    width: '95%',
    alignSelf: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 15,
    padding: 15,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border,
    marginBottom: -15,
    zIndex: -1,
    elevation: -1,
  },
  attendanceDisplayTitle: {
    fontSize: Math.max(14, Math.min(18, Dimensions.get('window').width * 0.045)), // Smaller responsive font size to fit on one line
    fontWeight: 'bold',
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    textAlign: 'left',
    flex: 1, // Allow title to take available space
  },
  attendanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'baseline',
    marginBottom: 20,
    paddingHorizontal: 4,
    flexWrap: 'wrap', // Allow wrapping on smaller screens
  },
  totalAbsencesText: {
    fontSize: Math.max(12, Math.min(14, Dimensions.get('window').width * 0.035)), // Responsive font size
    fontWeight: '600',
    color: safeCurrentThemeName === 'colorful' ? '#00CED1' : '#EF4444', // Turquoise in pink mode, red in others
    marginLeft: 8, // Add spacing from title
  },
  attendanceCardsContainer: {
    flex: 1,
  },
  attendanceCardsContentContainer: {
    flexGrow: 1,
    paddingBottom: 20, // Add padding at bottom for better scrolling experience
  },
  attendanceCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1, // Reduced shadow opacity
    shadowRadius: 1.5, // Reduced shadow radius
    elevation: 2, // Reduced elevation for Android
  },
  attendanceCardAttended: {
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : '#10B981', // Yellow in pink mode, green in others
  },
  attendanceCardAbsent: {
    borderColor: safeCurrentThemeName === 'colorful' ? '#00CED1' : '#EF4444', // Turquoise in pink mode, red in others
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardDate: {
    fontSize: Math.max(14, Math.min(16, Dimensions.get('window').width * 0.04)), // Responsive font size
    fontWeight: '600',
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    flex: 1,
  },
  cardRowNumber: {
    fontSize: Math.max(10, Math.min(12, Dimensions.get('window').width * 0.03)), // Responsive font size
    fontWeight: '500',
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
    backgroundColor: safeCurrentThemeName === 'colorful' ? 'rgba(0,0,0,0.3)' : theme.colors.surfaceVariant,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    marginLeft: 8, // Add some spacing from the date
  },
  cardContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  cardSessionType: {
    fontSize: Math.max(10, Math.min(12, Dimensions.get('window').width * 0.03)), // Responsive font size
    fontWeight: '500',
    color: theme.colors.textSecondary,
    backgroundColor: safeCurrentThemeName === 'colorful' ? 'rgba(0,0,0,0.3)' : theme.colors.surfaceVariant,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  cardSlot: {
    fontSize: Math.max(12, Math.min(14, Dimensions.get('window').width * 0.035)), // Responsive font size
    fontWeight: '500',
    color: theme.colors.textSecondary,
    backgroundColor: safeCurrentThemeName === 'colorful' ? 'rgba(0,0,0,0.3)' : theme.colors.surfaceVariant,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 6,
  },
  attendanceStatus: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  attendanceStatusAttended: {
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : '#10B981', // Yellow in pink mode, green in others
  },
  attendanceStatusAbsent: {
    color: safeCurrentThemeName === 'colorful' ? '#00CED1' : '#EF4444', // Turquoise in pink mode, red in others
  },
  placeholderText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 50,
  },
  summarySection: {
    marginTop: 20,
    marginHorizontal: Math.max(12, Dimensions.get('window').width * 0.03),
    width: '95%',
    alignSelf: 'center',
    zIndex: -1,
    elevation: -1,
  },
  summaryCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 20,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : theme.colors.border,
    alignItems: 'center',
  },
  summaryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : theme.colors.primary,
    marginBottom: 8,
    textAlign: 'center',
  },
  summarySubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  warningsContainer: {
    width: '100%',
    marginTop: 10,
  },
  warningItem: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderWidth: safeCurrentThemeName === 'colorful' ? 2 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#00CED1' : theme.colors.border,
  },
  warningContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  warningLeft: {
    flex: 1,
  },
  warningRight: {
    alignItems: 'flex-end',
  },
  warningCourseName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    marginBottom: 4,
  },
  warningCourseCode: {
    fontSize: 14,
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
  },
  warningLevel: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'right',
  },
  noWarningsText: {
    fontSize: 16,
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.success,
    textAlign: 'center',
    marginTop: 10,
    fontWeight: '500',
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noDataText: {
    fontSize: 18,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: 10,
  },
  noDataSubtext: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  hiddenWebView: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
    opacity: 0,
  },
});

export default AttendanceScreen;
