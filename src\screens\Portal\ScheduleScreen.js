import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Animated,
  Platform,
  PanResponder,
  Dimensions,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import Sidebar from '../../components/Sidebar';
import HamburgerIcon from '../../components/HamburgerIcon';
import RefreshIcon from '../../components/RefreshIcon';
import ThemeTransitionWrapper from '../../components/ThemeTransitionWrapper';
import { clearWebViewSession, disposeWebView } from '../../utils/WebViewUtils';
import { useTheme } from '../../contexts/ThemeContext';

const ScheduleScreen = ({ navigation }) => {
  // Theme context with fallbacks
  const { theme, currentThemeName } = useTheme();
  const safeCurrentThemeName = currentThemeName || 'dark';

  const [isLoading, setIsLoading] = useState(true);
  const [credentials, setCredentials] = useState(null);
  const [scheduleUrl, setScheduleUrl] = useState('');
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const [sidebarAnim] = useState(new Animated.Value(-300));
  const [scheduleData, setScheduleData] = useState([]);
  const [isLoadingSchedule, setIsLoadingSchedule] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false); // Added missing state variable
  const [refreshRotation] = useState(new Animated.Value(0)); // For rotating refresh arrow
  // Removed isUpdatingData - using refresh button loading instead
  const [hasCachedData, setHasCachedData] = useState(false);

  // Function to get today's day name
  const getTodayDayName = () => {
    const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const today = new Date();
    const todayName = days[today.getDay()];
    console.log('📅 Today is:', todayName, '- Auto-selecting this day for schedule');
    return todayName;
  };

  const [selectedDay, setSelectedDay] = useState(getTodayDayName()); // Start with today's day
  const webViewRef = useRef(null);
  const daysScrollViewRef = useRef(null);

  // Refs for tracking background operations and component mount state
  const isMountedRef = useRef(true);
  const activeTimeoutsRef = useRef(new Set());

  // Comprehensive function to kill all background operations
  const killAllBackgroundOperations = async () => {
    console.log('🛑 ScheduleScreen: Killing all background operations...');

    // Mark component as unmounted to prevent state updates
    isMountedRef.current = false;

    // Clear all active timeouts
    activeTimeoutsRef.current.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    activeTimeoutsRef.current.clear();

    // Stop all animations
    refreshRotation.stopAnimation();
    sidebarAnim.stopAnimation();

    // Reset all loading states to prevent cache corruption
    setIsLoading(false);
    setIsLoadingSchedule(false);
    setIsRefreshing(false);

    // Dispose of WebView
    await disposeWebView(webViewRef, 'schedule-webview');

    console.log('✅ ScheduleScreen: All background operations killed');
  };

  // Safe state setter that checks if component is still mounted
  const safeSetState = (setter, value, stateName) => {
    if (isMountedRef.current) {
      setter(value);
    } else {
      console.log(`⚠️ ScheduleScreen: Prevented ${stateName} state update after unmount`);
    }
  };

  // Safe timeout wrapper that tracks timeouts for cleanup
  const safeSetTimeout = (callback, delay) => {
    const timeoutId = setTimeout(() => {
      activeTimeoutsRef.current.delete(timeoutId);
      if (isMountedRef.current) {
        callback();
      }
    }, delay);
    activeTimeoutsRef.current.add(timeoutId);
    return timeoutId;
  };

  const days = ['Saturday', 'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday'];
  const periods = ['First Slot', 'Second Slot', 'Third Slot', 'Fourth Slot', 'Fifth Slot'];

  // Get screen dimensions for responsive sizing
  const screenWidth = Dimensions.get('window').width;
  const screenHeight = Dimensions.get('window').height;
  const isSmallScreen = screenWidth < 375 || screenHeight < 667; // iPhone SE size or smaller

  // Generate styles based on current theme and screen size
  const styles = createStyles(theme, safeCurrentThemeName, isSmallScreen, screenWidth, screenHeight);

  // Function to scroll to selected day
  const scrollToSelectedDay = (dayName) => {
    const dayIndex = days.indexOf(dayName);
    if (dayIndex !== -1 && daysScrollViewRef.current) {
      // Calculate scroll position (button width + margin)
      const buttonWidth = 120; // Updated minWidth from styles
      const marginRight = 8; // marginRight from styles
      const scrollX = dayIndex * (buttonWidth + marginRight);

      daysScrollViewRef.current.scrollTo({
        x: scrollX,
        animated: true
      });
    }
  };

  useEffect(() => {
    initializeScreenWithCache();

    // Cleanup function
    return () => {
      console.log('🧹 ScheduleScreen: Component unmounting - cleaning up...');
      killAllBackgroundOperations();
    };
  }, []);

  // Handle navigation focus/blur - kill background operations when losing focus
  useFocusEffect(
    useCallback(() => {
      console.log('🔄 ScheduleScreen: Screen focused');

      // Mark component as mounted when focused
      isMountedRef.current = true;

      // Return cleanup function that runs when screen loses focus
      return () => {
        console.log('🔄 ScheduleScreen: Screen losing focus - killing background operations...');
        killAllBackgroundOperations();
      };
    }, [])
  );

  // Kill background operations when navigating away completely
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      console.log('🧹 ScheduleScreen: Screen unmounting - Killing all background operations...');
      await killAllBackgroundOperations();
    });

    return unsubscribe;
  }, [navigation]);

  // Scroll to selected day when it changes
  useEffect(() => {
    // Add a small delay to ensure the ScrollView is rendered (optimized)
    const timer = safeSetTimeout(() => {
      scrollToSelectedDay(selectedDay);
    }, 50); // Optimized from 100ms to 50ms

    return () => clearTimeout(timer);
  }, [selectedDay]);

  // Initialize screen with cache-first strategy
  const initializeScreenWithCache = async () => {
    console.log('🚀 Initializing ScheduleScreen with cache-first strategy');

    // Try to load cached data first
    const cacheLoaded = await loadFromCache();

    if (cacheLoaded) {
      console.log('✅ Cached schedule data loaded, starting background refresh');
      // Fetch fresh data in background (using refresh button loading state)
      await loadCredentialsAndSetupSchedule(true); // Pass true to indicate background refresh
    } else {
      console.log('📭 No cache found, loading fresh schedule data');
      // No cache, load normally
      await loadCredentialsAndSetupSchedule(false);
    }
  };

  const openSidebar = () => {
    setIsSidebarVisible(true);
    Animated.timing(sidebarAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeSidebar = () => {
    Animated.timing(sidebarAnim, {
      toValue: -300,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsSidebarVisible(false);
    });
  };

  // Swipe gesture handler for opening/closing sidebar - Enhanced for Android
  const swipeGestureHandler = PanResponder.create({
    onStartShouldSetPanResponder: () => false, // Don't capture immediately
    onStartShouldSetPanResponderCapture: () => false, // Don't capture on start
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to significant horizontal swipes
      const { dx, dy } = gestureState;
      const { pageY } = evt.nativeEvent;
      
      // Exclude the days selector area (approximately top 200px of main content)
      // Main content starts at paddingTop: 100, days selector is around 60px height + margins
      const daysSelectorAreaHeight = 200;
      if (pageY < daysSelectorAreaHeight) {
        return false; // Don't capture swipes in the days selector area
      }

      // Only capture if it's a clear horizontal swipe with significant movement
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
      // Only capture significant horizontal swipes to prevent Android system gestures
      const { dx, dy } = gestureState;
      const { pageY } = evt.nativeEvent;
      
      // Exclude the days selector area
      const daysSelectorAreaHeight = 200;
      if (pageY < daysSelectorAreaHeight) {
        return false; // Don't capture swipes in the days selector area
      }
      
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onPanResponderGrant: () => {
      // Gesture has been granted - prevent other handlers
      return true;
    },
    onPanResponderMove: () => {
      // Optional: Add visual feedback during swipe
    },
    onPanResponderRelease: (evt, gestureState) => {
      const { dx, dy } = gestureState;
      const isHorizontalSwipe = Math.abs(dx) > Math.abs(dy);
      const swipeDistance = Math.abs(dx);

      if (isHorizontalSwipe && swipeDistance > 100) {
        if (dx > 0) {
          // Swipe right - open sidebar
          openSidebar();
        } else {
          // Swipe left - close sidebar if it's open
          if (isSidebarVisible) {
            closeSidebar();
          }
        }
      }
    },
    onPanResponderTerminationRequest: () => false, // Don't allow termination
    onShouldBlockNativeResponder: () => true, // Block native responders
  });

  // Cache management functions
  const CACHE_KEY = 'schedule_cache';
  const CACHE_TIMESTAMP_KEY = 'schedule_cache_timestamp';

  const saveToCache = async (data) => {
    try {
      const cacheData = {
        scheduleData: data,
        timestamp: Date.now()
      };
      await AsyncStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
      await AsyncStorage.setItem(CACHE_TIMESTAMP_KEY, Date.now().toString());
      console.log('📦 Schedule data cached successfully');
    } catch (error) {
      console.log('❌ Error saving schedule to cache:', error);
    }
  };

  const loadFromCache = async () => {
    try {
      const cachedData = await AsyncStorage.getItem(CACHE_KEY);
      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        console.log('📦 Loading cached schedule data');

        // Set cached data to state
        setScheduleData(parsedData.scheduleData || []);
        setHasCachedData(true);
        setIsLoading(false);

        return true; // Cache loaded successfully
      }
      return false; // No cache found
    } catch (error) {
      console.log('❌ Error loading schedule from cache:', error);
      return false;
    }
  };

  const clearCache = async () => {
    try {
      await AsyncStorage.removeItem(CACHE_KEY);
      await AsyncStorage.removeItem(CACHE_TIMESTAMP_KEY);
      console.log('🗑️ Schedule cache cleared');
    } catch (error) {
      console.log('❌ Error clearing schedule cache:', error);
    }
  };

  // Normalize schedule data to ensure 6 days with 5 slots each
  const normalizeScheduleData = (rawData) => {
    const days = ['Saturday', 'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday'];
    const periods = ['First Slot', 'Second Slot', 'Third Slot', 'Fourth Slot', 'Fifth Slot'];

    console.log('🔧 Normalizing schedule data...');
    console.log('📊 Raw data received:', rawData);

    const normalizedData = [];

    days.forEach(day => {
      const dayData = {
        day: day,
        slots: []
      };

      // Find existing data for this day
      const existingDay = rawData.find(d => d.day === day);

      periods.forEach((period, periodIndex) => {
        let slotData = {
          period: period,
          course: 'Free',
          location: '',
          type: 'Free',
          tutorialNumber: null
        };

        // If we have existing data for this day, try to find the slot
        if (existingDay && existingDay.slots && existingDay.slots[periodIndex]) {
          const existingSlot = existingDay.slots[periodIndex];
          if (existingSlot && existingSlot.course && existingSlot.course !== 'Free') {
            slotData = existingSlot;
          }
        }

        dayData.slots.push(slotData);
      });

      normalizedData.push(dayData);
    });

    console.log('✅ Schedule data normalized to 6 days with 5 slots each');
    console.log('📊 Normalized data:', normalizedData);

    return normalizedData;
  };

  const handleRefresh = async () => {
    console.log('🔄 Refresh button pressed - reloading schedule');

    // Clear cache to force fresh data
    await clearCache();

    // Reset data states
    setScheduleData([]);
    setHasCachedData(false);
    // Using refresh button loading state instead of separate update indicator
    setIsLoadingSchedule(true);

    // Start rotation animation
    Animated.loop(
      Animated.timing(refreshRotation, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    ).start();

    // Reload the WebView to fetch fresh schedule data
    if (webViewRef.current && scheduleUrl) {
      webViewRef.current.reload();
    }
  };

  const stopRefreshAnimation = () => {
    refreshRotation.stopAnimation();
    refreshRotation.setValue(0);
    setIsLoadingSchedule(false);
  };

  // Helper function to get slots for a specific day from the new data structure
  const getSlotsForDay = (day) => {
    console.log('🔍 Getting slots for day:', day);
    console.log('📊 Current schedule data:', scheduleData);

    // Find the day object in the schedule data
    const dayData = scheduleData.find(item => item.day === day);

    if (!dayData) {
      console.log('⚠️ No data found for day:', day);
      // Return 5 free slots if no data found
      return Array(5).fill('Free');
    }

    console.log('✅ Found day data:', dayData);

    // Return the slots array directly from the day data
    // The injected JS returns: { day: "Saturday", slots: [...] }
    return dayData.slots || Array(5).fill('Free');
  };



  const loadCredentialsAndSetupSchedule = async (isBackgroundRefresh = false) => {
    try {
      console.log('🔄 Loading credentials for schedule...');

      // Clear WebView session before setting up schedule (only if not background refresh)
      if (!isBackgroundRefresh) {
        await clearWebViewSession();
      }

      // Get stored credentials from localStorage
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('❌ No stored credentials found');
        if (!isBackgroundRefresh) {
          Alert.alert(
            'Authentication Required',
            'Please login first to access schedule.',
            [
              {
                text: 'Go to Login',
                onPress: () => navigation.navigate('Login')
              }
            ]
          );
        }
        // Using refresh button loading state instead
        return;
      }

      console.log('✅ Credentials found, setting up schedule URL...');

      // Store credentials for use
      setCredentials({ username: storedUsername, password: storedPassword });

      // Create authenticated URL for schedule page
      const authenticatedUrl = `https://${encodeURIComponent(storedUsername)}:${encodeURIComponent(storedPassword)}@apps.guc.edu.eg/student_ext/Scheduling/GroupSchedule.aspx`;
      setScheduleUrl(authenticatedUrl);

      console.log('🌐 Schedule URL set, WebView will load...');
      // Only hide loading if we're not doing background refresh or don't have cached data
      if (!isBackgroundRefresh && !hasCachedData) {
        setIsLoading(false);
      }

    } catch (error) {
      console.log('❌ Error loading credentials:', error);
      if (!isBackgroundRefresh) {
        Alert.alert(
          'Error',
          'Failed to load credentials. Please try logging in again.',
          [
            {
              text: 'Go to Login',
              onPress: () => navigation.navigate('Login')
            }
          ]
        );
      }
      // Using refresh button loading state instead
    }
  };

  const handleWebViewLoad = () => {
    console.log('📱 Schedule WebView loaded successfully');

    // Start loading schedule data (only show loading if no cached data)
    if (!hasCachedData) {
      setIsLoadingSchedule(true);
    }

    // Use two-phase approach like GradesScreen for iOS compatibility (optimized)
    // Phase 1: Wait for page to load, then wait for dynamic content
    const phase1Delay = Platform.OS === 'ios' ? 1200 : 600; // Optimized: iOS 2s→1.2s, Android 1s→600ms
    safeSetTimeout(() => {
      console.log('🔄 Phase 1: Waiting for schedule table to appear...');
      injectPhase2ExtractionCode();
    }, phase1Delay);
  };

  // Phase 2: Extract schedule data with your working code
  const injectPhase2ExtractionCode = () => {
    if (webViewRef.current) {
      const jsCode = `
        function extractTimetableData() {
          try {
            // Find the table by its ID
            const table = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_scdTbl').querySelector("tbody");

            if (!table) {
              if (window.ReactNativeWebView) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'schedule_data',
                  error: 'Table not found'
                }));
              }
              return null;
            }

            const rows = table.querySelectorAll(':scope > tr');
            const trimmedNodes = Array.from(rows).slice(1, -1);
            const schedule = [];

            for(let i = 0; i < 6; i++){
              const slots = [];
              const children = trimmedNodes[i].children;

              for(let j = 0; j < Array.from(children).length; j++){
                const cleanText = children[j].textContent
                  .replace(/\\n+/g, '\\n')        // Normalize multiple newlines to single newline
                  .replace(/\\r/g, '')           // Remove carriage returns
                  .replace(/[ \\t\\f\\v]+/g, ' ')  // Replace multiple spaces/tabs with single space
                  .replace(/\\n /g, '\\n')        // Remove spaces after newlines
                  .replace(/ \\n/g, '\\n')        // Remove spaces before newlines
                  .trim();                      // Remove leading/trailing whitespace

                const splittedCleanText = cleanText.split('\\n');
                for(let k = 0; k < splittedCleanText.length; k++){
                  if(splittedCleanText[k] !== ''){
                    slots.push(splittedCleanText[k]);
                  }
                }
              }

              if(slots.length === 2){
                schedule.push({day:slots[0], slots: ['Free','Free','Free','Free','Free']});
              }
              else{
                let filteredSlots = [];
                for(let slotIndex = 1; slotIndex < slots.length; slotIndex++){
                  if(slots[slotIndex].includes('Lecture')){
                    const courseMatch = slots[slotIndex].match(/^([A-Z]+\\s\\d+)/);
                    const typeMatch = slots[slotIndex].match(/\\s(Lecture|Lab|Tutorial|Seminar)\\s/);
                    const groupMatch = slots[slotIndex].match(/L(\\d{3})/);
                    const locationMatch = slots[slotIndex].match(/\\)(\\w+\\d+)$/);

                    const slot = {
                      course: courseMatch ? courseMatch[1] : null,
                      group: groupMatch ? String(parseInt(groupMatch[1], 10)) : null,
                      location: locationMatch ? locationMatch[1] : null,
                      type: typeMatch ? typeMatch[1] : null
                    };
                    filteredSlots.push(slot);
                  }
                  else if(slots[slotIndex].includes('Free')){
                    filteredSlots.push('Free');
                  }
                  else{
                    const nameWithGroup = slots[slotIndex++];
                    const location = slots[slotIndex++];
                    const courseName = slots[slotIndex++];
                    const courseType = slots[slotIndex];

                    const slot = {
                      course: courseName,
                      group: (function() {
                        const groupMatch = nameWithGroup.match(/[TP](\\d{3})/i);
                        return groupMatch ? String(parseInt(groupMatch[1], 10)) : null;
                      })(),
                      location: location,
                      type: courseType === "Tut" ? "Tutorial" : courseType
                    };
                    filteredSlots.push(slot);
                  }
                }
                schedule.push({day:slots[0], slots: filteredSlots});
              }
            }

            // Send the schedule data back to React Native
            if (window.ReactNativeWebView) {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'schedule_data',
                data: schedule
              }));
            }

          } catch (error) {
            
          }
        }

        // Execute the extraction
        extractTimetableData();
      `;

      if (webViewRef.current) {
        webViewRef.current.injectJavaScript(jsCode);
      }
    }
  };

  const handleWebViewMessage = (event) => {
    try {
      const message = JSON.parse(event.nativeEvent.data);
      console.log('📨 Received message from WebView:', message);

      if (message.type === 'schedule_data') {
        if (message.error) {
          console.log('❌ Schedule parsing error:', message.error);
          safeSetState(setIsLoadingSchedule, false, 'isLoadingSchedule');
          // Using refresh button loading state instead
          Alert.alert(
            'Schedule Error',
            `Failed to parse schedule: ${message.error}`,
            [{ text: 'OK' }]
          );
        } else {
          console.log('📅 Schedule data received:', message.data);
          console.log('📊 Schedule data structure:', JSON.stringify(message.data, null, 2));

          // Normalize the schedule data to ensure 6 days with 5 slots each
          const normalizedData = normalizeScheduleData(message.data);
          safeSetState(setScheduleData, normalizedData, 'scheduleData');
          safeSetState(setIsLoadingSchedule, false, 'isLoadingSchedule');
          // Using refresh button loading state instead

          // Save normalized data to cache
          saveToCache(normalizedData);
        }
        stopRefreshAnimation();
      } else if (message.type === 'debug') {
        console.log('🐛 [WebView Debug]:', message.message);
      } else if (message.type === 'console_log') {
        // Display injected JS console.logs in terminal with special formatting
        console.log('🔧 [Injected JS]:', message.message);
      } else if (message.type === 'console_error') {
        // Display injected JS console.errors in terminal
        console.log('🚨 [Injected JS Error]:', message.message);
      } else if (message.type === 'console_warn') {
        // Display injected JS console.warns in terminal
        console.log('⚠️ [Injected JS Warning]:', message.message);
      }
    } catch (error) {
      console.log('❌ Error parsing WebView message:', error);
      stopRefreshAnimation();
    }
  };

  const handleWebViewError = (error) => {
    console.log('❌ Schedule WebView error:', error);
    stopRefreshAnimation();
    Alert.alert(
      'Connection Error',
      'Failed to load schedule. Please check your internet connection and try again.',
      [
        { text: 'OK' }
      ]
    );
  };

  return (
    <ThemeTransitionWrapper>
      <View style={{ flex: 1 }} {...swipeGestureHandler.panHandlers}>
        <SafeAreaView style={styles.container}>
      {/* Sidebar Button */}
      <View style={styles.sidebarButtonContainer}>
        <TouchableOpacity
          style={styles.sidebarButton}
          onPress={openSidebar}
        >
          <HamburgerIcon size={20} color={theme.colors.primary} strokeWidth={3} />
        </TouchableOpacity>
      </View>

      {/* Schedule Title */}
      <View style={styles.scheduleTitleContainer}>
        <Text style={styles.scheduleTitle}>Schedule</Text>
      </View>

      {/* Refresh Button */}
      <View style={styles.refreshButtonContainer}>
        <TouchableOpacity
          style={[
            styles.refreshButton,
            isLoadingSchedule && styles.refreshButtonLoading
          ]}
          onPress={handleRefresh}
          disabled={isLoadingSchedule}
        >
          <Animated.View
            style={{
              transform: [{
                rotate: refreshRotation.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg']
                })
              }]
            }}
          >
            <RefreshIcon
              size={24}
              color={isLoadingSchedule ? theme.colors.textSecondary : safeCurrentThemeName === 'navy' ? '#DC2626' : '#f1c40f'}
            />
          </Animated.View>
        </TouchableOpacity>
      </View>



      {/* Main Content */}
      <View style={styles.mainContent}>
        {(isLoading && !hasCachedData) ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#EAB308" />
            <Text style={styles.loadingText}>Loading schedule...</Text>
          </View>
        ) : (
          <>
            {/* Removed update indicator - using refresh button loading state instead */}

            {/* Schedule Display Section */}
            {(isLoadingSchedule && !hasCachedData) ? (
              <View style={styles.scheduleLoadingContainer}>
                <ActivityIndicator size="large" color="#EAB308" />
                <Text style={styles.loadingText}>Loading schedule data...</Text>
              </View>
            ) : (
              <View style={styles.scheduleDisplaySection}>
                {/* Horizontal Day Selector */}
                <View style={styles.daysSelectorContainer}>
                  <ScrollView
                    ref={daysScrollViewRef}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.daysScrollContainer}
                    style={styles.daysScrollView}
                    decelerationRate="fast"
                    snapToInterval={128} // Snap to each day button (120 minWidth + 8 marginRight)
                    snapToAlignment="start"
                    scrollEventThrottle={16}
                    bounces={true}
                    bouncesZoom={false}
                    alwaysBounceHorizontal={true}
                    // Gesture handler optimizations for Android
                    simultaneousHandlers={[]}
                    waitFor={[]}
                  >
                    {days.map((day, index) => (
                      <TouchableOpacity
                        key={day}
                        style={[
                          styles.dayButton,
                          selectedDay === day && styles.dayButtonSelected,
                          index === 0 && styles.firstDayButton // Special style for first button
                        ]}
                        onPress={() => {
                          setSelectedDay(day);
                          console.log('📅 Selected day:', day);
                        }}
                      >
                        <Text style={[
                          styles.dayButtonText,
                          selectedDay === day && styles.dayButtonTextSelected
                        ]}>
                          {day}
                        </Text>
                      </TouchableOpacity>
                    ))}
                  </ScrollView>
                </View>

                {/* Selected Day Title */}
                <View style={styles.selectedDayContainer}>
                  <Text style={styles.selectedDayTitle}>{selectedDay}</Text>
                </View>

                {/* Slots for Selected Day - All 5 visible */}
                <View style={styles.slotsContainer}>
                  {getSlotsForDay(selectedDay).map((slot, index) => (
                    <View key={index} style={styles.slotCard}>
                      {(slot === 'Free' || slot?.course === 'Free' || slot?.courseName === 'Free' || !slot || (typeof slot === 'object' && Object.keys(slot).length === 0)) ? (
                        <View style={styles.freeSlotContent}>
                          {/* Only show slot number and Free text - no header line or group */}
                          <Text style={styles.slotPeriod}>Slot {index + 1}</Text>
                          <View style={styles.freeCenterContent}>
                            <Text style={styles.freeSlotText}>Free</Text>
                          </View>
                        </View>
                      ) : (
                        <View style={styles.courseSlotContent}>
                          {/* Top Row: Slot number and Course type */}
                          <View style={styles.slotTopRow}>
                            <Text style={styles.slotPeriod}>Slot {index + 1}</Text>
                            <Text style={styles.courseTypeBadge}>{slot.type}</Text>
                          </View>

                          {/* Center: Large Course Name */}
                          <View style={styles.courseCenterContent}>
                            <Text
                              style={styles.courseNameLarge}
                              numberOfLines={1}
                              ellipsizeMode="tail"
                            >
                              {slot.course || slot.courseName}
                            </Text>
                          </View>

                          {/* Bottom Row: Group and Location */}
                          <View style={styles.courseBottomRow}>
                            <Text style={styles.courseGroup}>
                              Group {slot.group}
                            </Text>
                            <Text style={styles.courseLocation}>
                              {slot.location}
                            </Text>
                          </View>
                        </View>
                      )}
                    </View>
                  ))}
                </View>
              </View>
            )}

            {/* WebView for data extraction - always hidden */}
            <View style={styles.hiddenWebView}>
              <WebView
                ref={webViewRef}
                source={{ uri: scheduleUrl }}
                onLoad={handleWebViewLoad}
                onMessage={handleWebViewMessage}
                onError={handleWebViewError}
                javaScriptEnabled={true}
                domStorageEnabled={true}
                mixedContentMode="compatibility"
                cacheEnabled={false}
                incognito={true}
                // iOS-specific props
                allowsInlineMediaPlayback={true}
                mediaPlaybackRequiresUserAction={false}
                allowsBackForwardNavigationGestures={false}
                // Security and compatibility
                originWhitelist={['*']}
                allowUniversalAccessFromFileURLs={true}
                allowFileAccessFromFileURLs={true}
                // Ensure JavaScript injection works on iOS
                injectedJavaScript=""
                onLoadEnd={handleWebViewLoad}
              />
            </View>
          </>
        )}
      </View>

      {/* Sidebar Component */}
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={closeSidebar}
        sidebarAnim={sidebarAnim}
        navigation={navigation}
        currentScreen="Schedule"
      />
    </SafeAreaView>
      </View>
    </ThemeTransitionWrapper>
  );
};

// Create styles function that uses theme and responsive sizing
const createStyles = (theme, safeCurrentThemeName, isSmallScreen, screenWidth, screenHeight) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  scheduleTitleContainer: {
    position: 'absolute',
    top: 55, // Raised higher
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 999,
  },
  scheduleTitle: {
    fontSize: 30, // Increased font size slightly
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
  },
  sidebarButtonContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 1000,
  },
  sidebarButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  refreshButtonContainer: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1000,
  },
  refreshButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  refreshButtonLoading: {
    backgroundColor: theme.colors.surfaceSecondary,
    opacity: 0.7,
  },
  mainContent: {
    flex: 1,
    paddingTop: 100, // Space for header elements
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: '#9CA3AF',
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 15,
  },
  scheduleLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scheduleDisplaySection: {
    flex: 1,
    paddingHorizontal: 0, // Remove horizontal padding to start from very left
    width: '100%', // Ensure full width
  },
  // Day Selector Styles
  daysSelectorContainer: {
    marginBottom: 20,
    width: '100%', // Take full width
  },
  daysScrollView: {
    width: '100%', // Ensure ScrollView takes full width
    flexGrow: 1,
  },
  daysScrollContainer: {
    paddingLeft: 0, // Start from very left edge
    paddingRight: 20, // Right padding for scroll end
    flexGrow: 1, // Allow container to grow and fill space
  },
  dayButton: {
    backgroundColor: safeCurrentThemeName === 'colorful' ? '#40E0D0' : theme.colors.surface, // Blue/turquoise in pink mode
    paddingHorizontal: 20, // Increased padding for better text fit
    paddingVertical: 12,
    borderRadius: 25,
    marginRight: 8,
    marginLeft: 8, // Add left margin for first button
    borderWidth: safeCurrentThemeName === 'colorful' ? 0 : 1, // No border in pink mode
    borderColor: safeCurrentThemeName === 'colorful' ? 'transparent' : theme.colors.border,
    minWidth: 120, // Increased width for better text fit in pink mode
    justifyContent: 'center', // Center content vertically
    alignItems: 'center', // Center content horizontally
  },
  dayButtonSelected: {
    backgroundColor: safeCurrentThemeName === 'colorful' ? '#20B2AA' : safeCurrentThemeName === 'navy' ? '#DC2626' : '#EAB308', // Darker turquoise in pink mode, red in navy mode
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#EAB308' : '#EAB308', // Yellow border in pink mode, gold border in navy mode
    borderWidth: safeCurrentThemeName === 'colorful' ? 2 : safeCurrentThemeName === 'navy' ? 2 : 1, // Slightly thicker border in special themes
  },
  firstDayButton: {
    marginLeft: 10, // Start first button from left edge with minimal margin
  },
  dayButtonText: {
    fontSize: isSmallScreen ? 12 : 14, // Smaller on small screens
    fontWeight: '600',
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : safeCurrentThemeName === 'navy' ? theme.colors.textSecondary : theme.colors.textSecondary, // White text in pink mode, elegant gray in navy
    textAlign: 'center', // Center text horizontally
  },
  dayButtonTextSelected: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : safeCurrentThemeName === 'navy' ? '#FFFFFF' : '#000000', // White text in pink and navy modes
    fontWeight: 'bold',
    textAlign: 'center', // Center text horizontally
  },
  // Selected Day Title
  selectedDayContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  selectedDayTitle: {
    fontSize: isSmallScreen ? 26 : 32, // Smaller on small screens
    fontWeight: 'bold',
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : '#EAB308', // Yellow in pink mode, red in navy mode
  },
  // Slots Container
  slotsContainer: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: isSmallScreen ? 8 : 10, // Less padding on small screens
  },
  slotCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: isSmallScreen ? 8 : 12, // Smaller radius on small screens
    padding: isSmallScreen ? 8 : 12, // Less padding on small screens
    marginBottom: isSmallScreen ? 4 : 6, // Less margin on small screens
    borderWidth: 1, // Same border width for all themes
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border, // Yellow border in pink mode
    flex: 1,
    maxHeight: isSmallScreen ? Math.max(65, screenHeight * 0.12) : Math.max(80, screenHeight * 0.15), // Responsive max height
    minHeight: isSmallScreen ? 60 : 70, // Smaller minimum height on small screens
    justifyContent: 'space-between',
  },
  slotPeriod: {
    fontSize: isSmallScreen ? 14 : 16, // Smaller on small screens
    fontWeight: 'bold',
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : '#EAB308', // Yellow in pink mode, red in navy mode
  },
  // Free Slot Styles
  freeSlotContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  freeCenterContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  freeSlotText: {
    fontSize: isSmallScreen ? 16 : 20, // Smaller on small screens
    color: safeCurrentThemeName === 'colorful' ? '#D8BFD8' : safeCurrentThemeName === 'navy' ? '#CBD5E0' : theme.colors.textSecondary, // Light greyish-pink in pink mode, elegant light gray in navy
    fontStyle: 'italic',
    fontWeight: '600',
    textAlign: 'center',
  },
  // Course Slot Styles
  courseSlotContent: {
    flex: 1,
    justifyContent: 'space-between',
  },
  slotTopRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingBottom: 6,
    marginBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: safeCurrentThemeName === 'colorful' ? '#d466a1ff' : '#555555', // Deep pink in pink mode, updated dark gray in other modes
  },
  courseCenterContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  courseNameLarge: {
    fontSize: isSmallScreen ? Math.max(16, screenWidth * 0.045) : Math.max(18, screenWidth * 0.055), // Responsive font size
    fontWeight: 'bold',
    color: safeCurrentThemeName === 'light' ? '#000000' : '#FFFFFF', // Black in light mode, white in all other modes (including pink)
    textAlign: 'center',
    lineHeight: isSmallScreen ? Math.max(18, screenWidth * 0.05) : Math.max(22, screenWidth * 0.06), // Responsive line height
  },
  courseBottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  courseGroup: {
    fontSize: isSmallScreen ? 11 : 12, // Smaller on small screens
    color: '#9CA3AF',
    fontWeight: '500',
  },
  courseLocation: {
    fontSize: isSmallScreen ? 11 : 12, // Smaller on small screens
    color: '#9CA3AF',
    fontWeight: '500',
  },
  courseTypeBadge: {
    fontSize: isSmallScreen ? 10 : 11, // Smaller on small screens
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : (safeCurrentThemeName === 'light' ? '#FFFFFF' : '#EAB308'), // White text for pink/light, yellow for dark
    fontWeight: '600',
    backgroundColor: safeCurrentThemeName === 'colorful' ? '#40E0D0' : (safeCurrentThemeName === 'light' ? '#EAB308' : '#2A2A2A'), // Turquoise for pink, yellow for light, dark for dark theme
    paddingHorizontal: isSmallScreen ? 6 : 8, // Less padding on small screens
    paddingVertical: isSmallScreen ? 2 : 3, // Less padding on small screens
    borderRadius: isSmallScreen ? 4 : 6, // Smaller radius on small screens
    textAlign: 'center',
  },
  courseTypeText: {
    fontSize: 11, // Smaller font
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : (safeCurrentThemeName === 'light' ? '#FFFFFF' : '#EAB308'), // White text for pink/light, yellow for dark
    fontWeight: '600',
    backgroundColor: safeCurrentThemeName === 'colorful' ? '#40E0D0' : (safeCurrentThemeName === 'light' ? '#EAB308' : '#2A2A2A'), // Turquoise for pink, yellow for light, dark for dark theme
    paddingHorizontal: 5, // Reduced padding
    paddingVertical: 1, // Reduced padding
    borderRadius: 4, // Smaller radius
  },
  courseDetails: {
    gap: 4,
  },
  courseDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  // New compact styles
  courseDetailsCompact: {
    marginTop: 4,
  },
  courseDetailCompact: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500',
    textAlign: 'left',
  },
  // New spaced styles
  courseDetailsSpaced: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 2,
  },
  courseDetailSpaced: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  courseDetailLabel: {
    fontSize: 12,
    color: '#9CA3AF',
    fontWeight: '500',
  },
  courseDetailValue: {
    fontSize: 12,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  noDataText: {
    fontSize: 18,
    color: '#9CA3AF',
    textAlign: 'center',
    marginBottom: 10,
  },
  noDataSubtext: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  hiddenWebView: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
  },
  // Removed update indicator styles - using refresh button loading state instead

});

export default ScheduleScreen;
