import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  ActivityIndicator,
  Platform,
  PanResponder,
  Dimensions,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import AsyncStorage from '@react-native-async-storage/async-storage';
import Sidebar from '../../components/Sidebar';
import HamburgerIcon from '../../components/HamburgerIcon';
import RefreshIcon from '../../components/RefreshIcon';
import Svg, { Path, Circle, Line } from 'react-native-svg';
import { useTheme } from '../../contexts/ThemeContext';

const TranscriptScreen = ({ navigation }) => {
  // Theme context with defensive programming
  let theme, currentThemeName;
  try {
    const themeContext = useTheme();
    theme = themeContext.theme;
    currentThemeName = themeContext.currentThemeName;
  } catch (error) {
    console.log('Theme context error:', error);
    // Fallback theme - Updated to match new dark theme
    theme = {
      colors: {
        background: '#2C2C2C', // Updated to new dark grey
        surface: '#3A3A3A', // Updated to new surface grey
        surfaceSecondary: '#4A4A4A', // Updated to new surface secondary grey
        text: '#FFFFFF',
        textSecondary: '#B0B0B0', // Lightened
        primary: '#EAB308',
        primaryText: '#2C2C2C', // Updated to match new background
        border: '#555555', // Lightened
        shadow: '#1A1A1A' // Lightened
      }
    };
    currentThemeName = 'dark';
  }

  // Fallback for currentThemeName to prevent undefined errors
  const safeCurrentThemeName = currentThemeName || 'dark';

  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const [sidebarAnim] = useState(new Animated.Value(-300)); // Start off-screen
  const [refreshRotation] = useState(new Animated.Value(0)); // For rotating refresh arrow
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [credentials, setCredentials] = useState(null);
  const [transcriptUrl, setTranscriptUrl] = useState('');
  const [hasEvaluationOptions, setHasEvaluationOptions] = useState(false);
  const [evaluationOptions, setEvaluationOptions] = useState([]);
  const [isCheckingEvaluation, setIsCheckingEvaluation] = useState(false);
  const [academicYears, setAcademicYears] = useState([]);
  const [transcriptData, setTranscriptData] = useState([]);
  const [isLoadingTranscripts, setIsLoadingTranscripts] = useState(false);
  const [transcriptWebViews, setTranscriptWebViews] = useState([]);
  const [cumulativeGPA, setCumulativeGPA] = useState(null);
  const [isGradesVisible, setIsGradesVisible] = useState(false);
  const [revealedGrades, setRevealedGrades] = useState(new Set());
  const [isUpdatingData, setIsUpdatingData] = useState(false);
  const [hasCachedData, setHasCachedData] = useState(false);
  const [failedYears, setFailedYears] = useState(new Set());
  const [retryAttempts, setRetryAttempts] = useState(new Map());
  // New state for independent year loading
  const [yearLoadingStates, setYearLoadingStates] = useState(new Map()); // Track loading state per year
  const [yearDataMap, setYearDataMap] = useState(new Map()); // Store data per year independently
  const [isGPALoading, setIsGPALoading] = useState(false); // Track cumulative GPA loading state
  const [isEvaluationCheckComplete, setIsEvaluationCheckComplete] = useState(false); // Track if evaluation check is done
  // Android-specific dropdown states
  const [selectedYear, setSelectedYear] = useState(null);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [yearDropdownOptions, setYearDropdownOptions] = useState([]);
  const [selectedYearData, setSelectedYearData] = useState(null);
  const [isLoadingSelectedYear, setIsLoadingSelectedYear] = useState(false);
  const webViewRef = useRef(null);
  const transcriptWebViewRefs = useRef([]);
  const formSubmittedFlags = useRef([]);

  // Add ref to track WebView creation state to prevent infinite loops
  const webViewCreationInProgress = useRef(false);
  const lastWebViewCreationTime = useRef(0);

  // Add ref to track background refresh timeout
  const backgroundRefreshTimeoutRef = useRef(null);

  // Generate styles based on current theme
  const styles = createStyles(theme, safeCurrentThemeName);

  useEffect(() => {
    initializeScreenWithCache();

    // Cleanup function to prevent memory leaks and infinite loops
    return () => {
      console.log('🧹 TranscriptScreen cleanup: Clearing WebViews and resetting flags');
      setTranscriptWebViews([]);
      transcriptWebViewRefs.current = [];
      formSubmittedFlags.current = [];
      webViewCreationInProgress.current = false;
      lastWebViewCreationTime.current = 0;

      // Clear background refresh timeout if it exists
      if (backgroundRefreshTimeoutRef.current) {
        clearTimeout(backgroundRefreshTimeoutRef.current);
        backgroundRefreshTimeoutRef.current = null;
        console.log('🧹 Background refresh timeout cleared during cleanup');
      }
    };
  }, []);

  // Handle navigation focus for Android - reset to dropdown view and prevent loops
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      console.log('📱 TranscriptScreen focused');

      // Reset WebView creation flags to prevent stale state
      webViewCreationInProgress.current = false;
      lastWebViewCreationTime.current = 0;

      if (Platform.OS === 'android' && yearDropdownOptions.length > 0) {
        // Reset to dropdown view when navigating back to this screen
        setSelectedYear(null);
        setSelectedYearData(null);
        setIsDropdownVisible(false);
        console.log('📱 Android: Reset to dropdown view on navigation focus');
      }
    });

    return unsubscribe;
  }, [navigation, yearDropdownOptions.length]);

  // Initialize screen with cache-first strategy
  const initializeScreenWithCache = async () => {
    console.log('🚀 Initializing TranscriptScreen with cache-first strategy');

    // Load cached cumulative GPA first (permanent cache)
    const cachedGPA = await loadCumulativeGPAFromCache();
    if (cachedGPA) {
      setCumulativeGPA(cachedGPA);
      console.log('✅ Cached cumulative GPA loaded:', cachedGPA);
    }

    // Try to load cached data first
    const cacheLoaded = await loadFromCache();

    if (cacheLoaded) {
      console.log('✅ Cached data loaded and displayed, starting silent background refresh for last 2 years only');

      // User sees cached data immediately - no loading states
      setIsLoading(false);
      setIsCheckingEvaluation(false);
      setIsEvaluationCheckComplete(true);
      setIsGPALoading(false);

      // Set up credentials for the main WebView even during cache-first loading
      // This prevents "No credentials found" error on subsequent visits
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (storedUsername && storedPassword) {
        const encodedUsername = encodeURIComponent(storedUsername);
        const encodedPassword = encodeURIComponent(storedPassword);
        const authenticatedUrl = `https://${encodedUsername}:${encodedPassword}@apps.guc.edu.eg/student_ext/Grade/Transcript_001.aspx`;
        setTranscriptUrl(authenticatedUrl);
        console.log('✅ Credentials set up for cached data display');
      } else {
        console.log('❌ No credentials found during cache-first loading');
        // Still show cached data but without background refresh
        stopBackgroundRefresh();
        return;
      }

      // Start silent background refresh for last 2 years only
      // Show only refresh button rotation, no other UI changes
      setIsUpdatingData(true);
      startRotationAnimation();

      // Load credentials and start background refresh
      // Pass the cached academic years directly to avoid state timing issues
      const cachedData = await AsyncStorage.getItem(CACHE_KEY);
      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        console.log('📋 Cached academic years for background refresh:', parsedData.academicYears?.length || 0);
        if (parsedData.academicYears && parsedData.academicYears.length > 0) {
          console.log('📋 First few academic years:', parsedData.academicYears.slice(0, 3).map(y => y.text));
        }
        await startSilentBackgroundRefresh(parsedData.academicYears);
      } else {
        console.log('❌ No cached data found for background refresh');
      }
    } else {
      console.log('📭 No cache found, loading fresh data');
      // No cache, load normally with loading states
      setIsGPALoading(true);
      await loadCredentialsAndSetupTranscript(false);
    }
  };

  // Silent background refresh for last 2 years only
  const startSilentBackgroundRefresh = async (cachedAcademicYears = null) => {
    try {
      console.log('🔄 Starting silent background refresh for last 2 years only');

      // Get stored credentials
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('❌ No stored credentials found for background refresh');
        stopBackgroundRefresh();
        return;
      }

      // Use provided cached academic years or fall back to state
      const academicYearsToUse = cachedAcademicYears || academicYears;
      console.log('📋 Academic years for background refresh:', academicYearsToUse?.length || 0);

      if (academicYearsToUse && academicYearsToUse.length > 0) {
        // Filter to get last 2 years (newest years)
        const last2Years = academicYearsToUse.slice(0, 2);
        console.log('🔄 Background refresh: Creating WebViews for last 2 years:', last2Years.map(y => y.text));

        // Set a timeout to ensure rotation stops even if something goes wrong
        const backgroundRefreshTimeout = setTimeout(() => {
          console.log('⏰ Background refresh timeout reached, stopping rotation');
          // Clear the timeout reference to prevent recursive clearing
          backgroundRefreshTimeoutRef.current = null;
          setIsUpdatingData(false);
          stopRotationAnimation();
        }, 10000); // 10 second timeout

        // Store timeout reference for cleanup
        backgroundRefreshTimeoutRef.current = backgroundRefreshTimeout;

        // Create WebViews for background refresh directly with credentials
        await createTranscriptWebViewsWithCredentials(last2Years, true, storedUsername, storedPassword);
      } else {
        console.log('❌ No cached academic years found for background refresh');
        console.log('📋 Cached academic years:', cachedAcademicYears?.length || 0);
        console.log('📋 State academic years:', academicYears?.length || 0);
        stopBackgroundRefresh();
      }

    } catch (error) {
      console.log('❌ Error starting silent background refresh:', error);
      stopBackgroundRefresh();
    }
  };

  const loadCredentialsAndSetupTranscript = async (isBackgroundRefresh = false) => {
    try {
      // Only show loading screen if no cached data is available
      if (!isBackgroundRefresh && !hasCachedData) {
        setIsLoading(true);
      }
      console.log('📋 Loading credentials for transcript...');

      // Get stored credentials using the same keys as other screens
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (storedUsername && storedPassword) {
        const creds = { username: storedUsername, password: storedPassword };
        setCredentials(creds);

        // Create authenticated URL - properly encode username and password
        const encodedUsername = encodeURIComponent(storedUsername);
        const encodedPassword = encodeURIComponent(storedPassword);
        const authenticatedUrl = `https://${encodedUsername}:${encodedPassword}@apps.guc.edu.eg/student_ext/Grade/Transcript_001.aspx`;
        setTranscriptUrl(authenticatedUrl);

        console.log('✅ Credentials loaded, transcript URL set');
        // Only hide loading if we're not doing background refresh
        if (!isBackgroundRefresh && !hasCachedData) {
          setIsLoading(false);
        }
      } else {
        console.log('❌ No stored credentials found');
        if (!isBackgroundRefresh && !hasCachedData) {
          setIsLoading(false);
        }
        // For background refresh errors, use the helper function
        if (isBackgroundRefresh) {
          stopBackgroundRefresh();
        } else {
          setIsUpdatingData(false);
          stopRotationAnimation(); // Stop rotation on error
        }
      }
    } catch (error) {
      console.log('❌ Error loading credentials:', error);
      if (!isBackgroundRefresh && !hasCachedData) {
        setIsLoading(false);
      }
      // For background refresh errors, use the helper function
      if (isBackgroundRefresh) {
        stopBackgroundRefresh();
      } else {
        setIsUpdatingData(false);
        stopRotationAnimation(); // Stop rotation on error
      }
    }
  };

  const openSidebar = () => {
    setIsSidebarVisible(true);
    Animated.timing(sidebarAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeSidebar = () => {
    Animated.timing(sidebarAnim, {
      toValue: -300,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsSidebarVisible(false);
    });
  };

  // Swipe gesture handler for opening/closing sidebar - Enhanced for Android
  const swipeGestureHandler = PanResponder.create({
    onStartShouldSetPanResponder: () => false, // Don't capture immediately
    onStartShouldSetPanResponderCapture: () => false, // Don't capture on start
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to significant horizontal swipes
      const { dx, dy } = gestureState;

      // Only capture if it's a clear horizontal swipe with significant movement
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
      // Only capture significant horizontal swipes to prevent Android system gestures
      const { dx, dy } = gestureState;
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onPanResponderGrant: () => {
      // Gesture has been granted - prevent other handlers
      return true;
    },
    onPanResponderMove: () => {
      // Optional: Add visual feedback during swipe
    },
    onPanResponderRelease: (evt, gestureState) => {
      const { dx, dy } = gestureState;
      const isHorizontalSwipe = Math.abs(dx) > Math.abs(dy);
      const swipeDistance = Math.abs(dx);

      if (isHorizontalSwipe && swipeDistance > 100) {
        if (dx > 0) {
          // Swipe right - open sidebar
          openSidebar();
        } else {
          // Swipe left - close sidebar if it's open
          if (isSidebarVisible) {
            closeSidebar();
          }
        }
      }
    },
    onPanResponderTerminationRequest: () => false, // Don't allow termination
    onShouldBlockNativeResponder: () => true, // Block native responders
  });

  // Rotation animation functions
  const startRotationAnimation = () => {
    refreshRotation.setValue(0);
    Animated.loop(
      Animated.timing(refreshRotation, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    ).start();
  };

  const stopRotationAnimation = () => {
    Animated.timing(refreshRotation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  // Helper function to stop background refresh and clean up timeout
  const stopBackgroundRefresh = () => {
    console.log('🛑 Stopping background refresh and cleaning up timeout');

    // Clear the background refresh timeout if it exists
    if (backgroundRefreshTimeoutRef.current) {
      clearTimeout(backgroundRefreshTimeoutRef.current);
      backgroundRefreshTimeoutRef.current = null;
      console.log('✅ Background refresh timeout cleared');
    }

    // Stop the rotation animation and update state
    setIsUpdatingData(false);
    stopRotationAnimation();
  };

  const handleRefresh = async () => {
    console.log('🔄 Refresh button pressed - Transcript screen');

    // Prevent multiple simultaneous refreshes
    if (isRefreshing || isUpdatingData) {
      console.log('⚠️ Refresh already in progress, ignoring...');
      return;
    }

    setIsRefreshing(true);
    startRotationAnimation();

    try {
      // Clear cache to force fresh data
      await clearCache();

      // Clear Android-specific cached year data
      if (Platform.OS === 'android') {
        await clearAndroidYearCache();
      }

      // Clear any existing WebViews first to prevent conflicts
      setTranscriptWebViews([]);
      transcriptWebViewRefs.current = [];
      formSubmittedFlags.current = [];

      // Reset WebView creation flags to allow fresh creation
      webViewCreationInProgress.current = false;
      lastWebViewCreationTime.current = 0;
      console.log('🔄 Reset WebView creation flags for fresh start');

      // Reset all data states to trigger ghost loading
      setTranscriptData([]);
      setAcademicYears([]);
      setCumulativeGPA(null);
      setHasEvaluationOptions(false);
      setEvaluationOptions([]);
      setHasCachedData(false);
      setIsUpdatingData(false); // Don't show update indicator during manual refresh
      setFailedYears(new Set()); // Reset failed years
      setRetryAttempts(new Map()); // Reset retry attempts

      // Reset new independent loading states - this will allow failed years to be retried
      setYearLoadingStates(new Map());
      setYearDataMap(new Map());

      console.log(`🔄 Reset all states for fresh loading - failed years can now be retried`);

      // Set loading states to show ghost loading (like first time opening)
      setIsLoading(true); // Show main loading screen
      setIsGPALoading(true); // Show GPA ghost loading
      setIsEvaluationCheckComplete(false); // Reset evaluation check
      setIsLoadingTranscripts(false); // Will be set to true when WebViews are created

      // Reset Android-specific states
      if (Platform.OS === 'android') {
        setSelectedYear(null);
        setSelectedYearData(null);
        setYearDropdownOptions([]);
        setIsDropdownVisible(false);
        setIsLoadingSelectedYear(false);
      }

      // Wait a moment for state to settle before reloading
      await new Promise(resolve => setTimeout(resolve, 500));

      // Reload the WebView
      if (webViewRef.current && transcriptUrl) {
        console.log('🔄 Reloading main WebView...');
        webViewRef.current.reload();
      } else {
        console.log('⚠️ No WebView or URL available for reload');
        // If no WebView, try to reinitialize
        await loadCredentialsAndSetupTranscript(false);
      }

    } catch (error) {
      console.log('❌ Error during refresh:', error);
      // On error, reset loading states
      setIsLoading(false);
      setIsGPALoading(false);
    } finally {
      // Clean up refresh state after a shorter delay since we're now showing proper loading states
      setTimeout(() => {
        setIsRefreshing(false);
        stopRotationAnimation();
        console.log('✅ Refresh completed - Transcript screen');
      }, 1000); // Reduced timeout since we now have proper loading states
    }
  };

  // Clear Android year cache
  const clearAndroidYearCache = async () => {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const yearCacheKeys = keys.filter(key => key.startsWith('transcript_year_'));
      if (yearCacheKeys.length > 0) {
        await AsyncStorage.multiRemove(yearCacheKeys);
        console.log('🗑️ Cleared Android year cache:', yearCacheKeys.length, 'items');
      }
    } catch (error) {
      console.log('❌ Error clearing Android year cache:', error);
    }
  };

  // Handle WebView load completion
  const handleWebViewLoad = () => {
    console.log('📋 Transcript page loaded successfully');
    setIsLoading(false);

    // Only show evaluation checking UI if we don't have cached data
    if (!hasCachedData) {
      setIsCheckingEvaluation(true);
    }

    // Check for evaluation element after page loads
    setTimeout(() => {
      checkForEvaluationElement();
    }, 2000); // Wait 2 seconds for page to fully render
  };

  // Check for evaluation element
  const checkForEvaluationElement = () => {
    if (!webViewRef.current) return;

    console.log('🔍 Checking for evaluation element...');

    const jsCode = `
      (function() {
        try {
          const evaluationElement = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_lstcourseeva');

          if (evaluationElement) {
            console.log('✅ Evaluation element found!');

            // Extract options from the element
            const options = [];
            if (evaluationElement.tagName === 'SELECT') {
              // If it's a select element
              for (let i = 0; i < evaluationElement.options.length; i++) {
                const option = evaluationElement.options[i];
                if (option.value && option.text && option.text.trim() !== '') {
                  options.push({
                    value: option.value,
                    text: option.text.trim()
                  });
                }
              }
            } else {
              // If it's another type of element, try to find options within it
              const optionElements = evaluationElement.querySelectorAll('option');
              for (let i = 0; i < optionElements.length; i++) {
                const option = optionElements[i];
                if (option.value && option.textContent && option.textContent.trim() !== '') {
                  options.push({
                    value: option.value,
                    text: option.textContent.trim()
                  });
                }
              }
            }

            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'evaluation_element_found',
              options: options
            }));
          } else {
            console.log('❌ Evaluation element not found');
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'evaluation_element_not_found'
            }));
          }
        } catch (error) {
          console.log('❌ Error checking evaluation element:', error);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'evaluation_check_error',
            error: error.message
          }));
        }
      })();
      true;
    `;

    webViewRef.current.injectJavaScript(jsCode);
  };

  // Handle WebView messages
  const handleWebViewMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('📨 WebView message received:', data.type);

      switch (data.type) {
        case 'evaluation_element_found':
          console.log('✅ Evaluation element found with', data.options.length, 'options');
          console.log('📋 Options:', data.options);
          setIsCheckingEvaluation(false);
          setHasEvaluationOptions(true);
          setEvaluationOptions(data.options);
          setIsEvaluationCheckComplete(true); // Mark evaluation check as complete
          break;

        case 'evaluation_element_not_found':
          console.log('❌ Evaluation element not found - checking for academic years');
          setIsCheckingEvaluation(false);
          setIsEvaluationCheckComplete(true); // Mark evaluation check as complete
          // Don't clear evaluation options if we have cached data
          if (!hasCachedData) {
            setHasEvaluationOptions(false);
            setEvaluationOptions([]);
          }
          // Check for academic years dropdown
          checkForAcademicYears();
          break;

        case 'evaluation_check_error':
          console.log('❌ Error checking evaluation element:', data.error);
          setIsCheckingEvaluation(false);
          setIsEvaluationCheckComplete(true); // Mark evaluation check as complete even on error
          // Don't clear evaluation options if we have cached data
          if (!hasCachedData) {
            setHasEvaluationOptions(false);
            setEvaluationOptions([]);
          }
          break;

        case 'academic_years_found':
          console.log('✅ Academic years found with', data.options.length, 'options');
          console.log('📋 Academic years:', data.options);
          console.log('📅 Enrollment year:', data.enrollmentYear);

          // Filter years from newest down to enrollment year
          const filteredYears = filterYearsToEnrollment(data.options, data.enrollmentYear);
          setAcademicYears(filteredYears);
          console.log('📋 Filtered years:', filteredYears);

          // Turn off main loading screen since we found the academic years
          setIsLoading(false);

          // If this is a refresh (no cached data), start the data loading process
          if (!hasCachedData) {
            console.log('🔄 Refresh detected - starting fresh data loading with ghost states');
            setIsLoadingTranscripts(true); // Show that we're loading transcript data
            // Keep isGPALoading true to show GPA ghost loading
          }

          // For iOS: Initialize year components immediately with loading states
          // Skip this during background refresh to preserve cached data
          if (Platform.OS === 'ios' && filteredYears.length > 0) {
            const isBackgroundRefresh = isUpdatingData && hasCachedData;

            if (!isBackgroundRefresh) {
              console.log('📱 iOS: Initializing year components immediately');
              const newLoadingStates = new Map();
              const newDataMap = new Map();

              filteredYears.forEach(year => {
                newLoadingStates.set(year.text, 'loading');
                newDataMap.set(year.text, null);
              });

              setYearLoadingStates(newLoadingStates);
              setYearDataMap(newDataMap);

              // Always start GPA loading when initializing year components
              setIsGPALoading(true);
              console.log('✅ iOS: Year components initialized for immediate display');
            } else {
              console.log('🔄 Background refresh: Preserving cached year components and data');
            }
          }

          // Handle platform-specific behavior
          if (Platform.OS === 'android') {
            // For Android: Create dropdown options with "1st year", "2nd year", etc.
            const dropdownOptions = filteredYears.map((year, index) => ({
              ...year,
              displayText: `${getOrdinalNumber(filteredYears.length - index)} year`,
              originalIndex: index
            }));
            setYearDropdownOptions(dropdownOptions);
            console.log('📱 Android dropdown options created:', dropdownOptions);
          } else {
            // For iOS: Start loading transcript data for filtered years
            if (filteredYears.length > 0) {
              // Check if this is a background refresh (not manual refresh)
              const isBackgroundRefresh = isUpdatingData && hasCachedData;

              // Skip WebView creation if this is a background refresh triggered by the main WebView
              // Background refresh WebViews should be created directly by startSilentBackgroundRefresh
              if (isBackgroundRefresh) {
                console.log('🔄 Background refresh detected - skipping main WebView creation');
                console.log('📋 Background refresh will be handled separately for last 2 years only');
                return; // Don't create WebViews here during background refresh
              }

              // For normal loading or manual refresh, create WebViews
              const shouldCreateWebViews = transcriptWebViews.length === 0 || !hasCachedData;

              if (shouldCreateWebViews) {
                setIsLoadingTranscripts(true);

                // For normal loading, process all years
                let yearsToProcess = filteredYears;
                if (isBackgroundRefresh && filteredYears.length > 2) {
                  yearsToProcess = filteredYears.slice(0, 2); // Get the first 2 years (newest)
                  console.log('⚡ iOS Background refresh: Processing only last 2 years');
                  console.log('📋 Years to process (optimized):', yearsToProcess);
                } else {
                  console.log('🔄 iOS Manual refresh: Processing all years');
                }

                console.log('🚀 iOS: Starting WebView creation for transcript data');
                createTranscriptWebViews(yearsToProcess, isBackgroundRefresh);
              } else {
                console.log('⚠️ iOS: WebViews already exist and have cached data, skipping creation');
              }
            }
          }
          break;

        case 'academic_years_not_found':
          console.log('❌ Academic years element not found');
          console.log('📦 Has cached data:', hasCachedData, 'Current academic years:', academicYears.length, 'Current transcript data:', transcriptData.length);

          // If this is a refresh (no cached data and no academic years), keep loading states active
          if (!hasCachedData && academicYears.length === 0) {
            console.log('🔄 Refresh in progress - keeping loading states active');
            // Keep isLoading and isGPALoading true to show ghost loading
            // The WebView will retry or the user can try again
          } else if (!hasCachedData) {
            console.log('🗑️ Clearing academic years (no cached data)');
            setAcademicYears([]);
          } else {
            console.log('📦 Preserving academic years (cached data exists)');
          }
          break;

        case 'academic_years_error':
          console.log('❌ Error checking academic years:', data.error);
          // Don't clear academic years if we have cached data
          if (!hasCachedData) {
            setAcademicYears([]);
          }
          break;



        default:
          console.log('📨 Unknown message type:', data.type);
          break;
      }
    } catch (error) {
      console.log('❌ Error parsing WebView message:', error);
    }
  };

  // Navigate to evaluate screen
  const navigateToEvaluate = () => {
    console.log('🔄 Navigating to Evaluate screen');
    navigation.navigate('Evaluate');
  };

  // Extract enrollment year from student ID
  const getEnrollmentYear = (studentId) => {
    if (!studentId) return null;

    // Extract digits before the dash
    const match = studentId.match(/^(\d+)-/);
    if (!match) return null;

    const digits = match[1];
    const firstTwoDigits = parseInt(digits.substring(0, 2));

    // Map the first two digits to academic years
    if (firstTwoDigits >= 64 && firstTwoDigits <= 66) {
      return '2024-2025';
    } else if (firstTwoDigits >= 61 && firstTwoDigits <= 63) {
      return '2023-2024';
    } else if (firstTwoDigits >= 58 && firstTwoDigits <= 60) {
      return '2022-2023';
    } else {
      return null; // Unknown year
    }
  };

  // Convert number to ordinal (1st, 2nd, 3rd, etc.)
  const getOrdinalNumber = (num) => {
    const suffixes = ['th', 'st', 'nd', 'rd'];
    const value = num % 100;
    return num + (suffixes[(value - 20) % 10] || suffixes[value] || suffixes[0]);
  };

  // Android-specific functions for dropdown handling
  const toggleDropdown = () => {
    setIsDropdownVisible(!isDropdownVisible);
  };

  const handleYearSelect = async (yearOption) => {
    console.log('📱 Android year selected:', yearOption);
    setSelectedYear(yearOption);
    setIsDropdownVisible(false);
    setIsLoadingSelectedYear(true);

    // Reset form submission flags for new year selection
    formSubmittedFlags.current = [false];

    // Check if this is one of the last 2 years (cache-first strategy)
    const isLast2Years = isInLast2Years(yearOption, yearDropdownOptions);

    // Check if we have cached data for this year
    const cachedYearData = await getCachedYearData(yearOption.text);

    if (cachedYearData && isLast2Years) {
      console.log('📦 Using cached data for recent year:', yearOption.text);
      setSelectedYearData(cachedYearData);
      setIsLoadingSelectedYear(false);

      // Start background refresh for last 2 years
      console.log('🔄 Starting background refresh for recent year:', yearOption.text);
      setIsUpdatingData(true);
      startRotationAnimation();
      await loadDataForSpecificYear(yearOption, true); // Pass true for background refresh
    } else if (cachedYearData) {
      console.log('📦 Using cached data for older year:', yearOption.text);
      setSelectedYearData(cachedYearData);
      setIsLoadingSelectedYear(false);
      // No background refresh for older years
    } else {
      // Load data for this specific year (no cache available)
      console.log('🔄 Loading fresh data for year:', yearOption.text);
      await loadDataForSpecificYear(yearOption, false);
    }
  };

  // Check if a year is in the last 2 years
  const isInLast2Years = (selectedYear, allYears) => {
    if (!allYears || allYears.length === 0) return false;

    // The dropdown options are ordered with most recent first
    // So the first 2 options are the last 2 years
    const selectedIndex = allYears.findIndex(year => year.text === selectedYear.text);
    return selectedIndex >= 0 && selectedIndex < 2;
  };

  // Get cached data for a specific year
  const getCachedYearData = async (yearText) => {
    try {
      const cacheKey = `transcript_year_${yearText}`;
      const cachedData = await AsyncStorage.getItem(cacheKey);
      if (cachedData) {
        return JSON.parse(cachedData);
      }
      return null;
    } catch (error) {
      console.log('❌ Error getting cached year data:', error);
      return null;
    }
  };

  // Save data for a specific year
  const saveCachedYearData = async (yearText, data) => {
    try {
      const cacheKey = `transcript_year_${yearText}`;
      await AsyncStorage.setItem(cacheKey, JSON.stringify(data));
      console.log('📦 Cached data for year:', yearText);
    } catch (error) {
      console.log('❌ Error saving cached year data:', error);
    }
  };

  // Load data for a specific year (Android only)
  const loadDataForSpecificYear = async (yearOption, isBackgroundRefresh = false) => {
    try {
      console.log('🔄 Creating WebView for specific year:', yearOption.text, isBackgroundRefresh ? '(background)' : '(foreground)');

      // Get stored credentials
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('❌ No stored credentials found');
        if (!isBackgroundRefresh) {
          setIsLoadingSelectedYear(false);
        } else {
          stopBackgroundRefresh();
        }
        return;
      }

      // Create a single WebView for this year
      const webViewConfig = {
        id: `transcript-android-${Date.now()}`,
        year: yearOption,
        url: `https://${storedUsername}:${storedPassword}@apps.guc.edu.eg/student_ext/Grade/Transcript_001.aspx?t=${Date.now()}`,
        index: 0,
        isBackgroundRefresh: isBackgroundRefresh
      };

      // Set up the WebView for this specific year
      setTranscriptWebViews([webViewConfig]);
      transcriptWebViewRefs.current = [null];
      formSubmittedFlags.current = [false];

      // Set loading state for this year
      setYearLoadingStates(prevStates => {
        const newStates = new Map(prevStates);
        newStates.set(yearOption.text, 'loading');
        return newStates;
      });

    } catch (error) {
      console.log('❌ Error loading data for specific year:', error);
      if (!isBackgroundRefresh) {
        setIsLoadingSelectedYear(false);
      } else {
        stopBackgroundRefresh();
      }
    }
  };

  // Convert GPA to letter grade
  const getLetterGrade = (gpa) => {
    const numericGPA = parseFloat(gpa);
    if (isNaN(numericGPA)) return '';

    if (numericGPA >= 0.7 && numericGPA <= 0.99) return 'A+';
    if (numericGPA >= 1.0 && numericGPA <= 1.29) return 'A';
    if (numericGPA >= 1.3 && numericGPA <= 1.69) return 'A-';
    if (numericGPA >= 1.7 && numericGPA <= 1.99) return 'B+';
    if (numericGPA >= 2.0 && numericGPA <= 2.29) return 'B';
    if (numericGPA >= 2.3 && numericGPA <= 2.69) return 'B-';
    if (numericGPA >= 2.7 && numericGPA <= 2.99) return 'C+';
    if (numericGPA >= 3.0 && numericGPA <= 3.29) return 'C';
    if (numericGPA >= 3.3 && numericGPA <= 3.69) return 'C-';
    if (numericGPA >= 3.7 && numericGPA <= 3.99) return 'D+';
    if (numericGPA >= 4.0 && numericGPA <= 4.99) return 'D';
    if (numericGPA >= 5.0 && numericGPA <= 6.0) return 'F';

    return ''; // Outside known ranges
  };

  // Small Eye Icon Component for blurred grades
  const SmallEyeIcon = ({ size = 12, color = '#FFFFFF' }) => (
    <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
      <Path
        d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
        stroke={color}
        strokeWidth="3"
        fill="none"
      />
      <Circle cx="12" cy="12" r="3" stroke={color} strokeWidth="3" fill="none" />
    </Svg>
  );

  // Toggle individual grade visibility
  const toggleGradeVisibility = (gradeId) => {
    setRevealedGrades(prev => {
      const newSet = new Set(prev);
      if (newSet.has(gradeId)) {
        newSet.delete(gradeId);
      } else {
        newSet.add(gradeId);
      }
      return newSet;
    });
  };

  // Check if a specific grade is revealed
  const isGradeRevealed = (gradeId) => {
    return isGradesVisible || revealedGrades.has(gradeId);
  };

  // Ghost loading component for individual years
  const YearGhostLoading = ({ yearText, displayText }) => (
    <View style={styles.yearContainer}>
      <View style={styles.ghostShimmer}>
        <Text style={styles.yearTitle}>{displayText || yearText}</Text>
        <Text style={styles.yearSummary}>Loading courses...</Text>
      </View>

      {/* Ghost semester containers */}
      {[1, 2, 3].map((semesterIndex) => (
        <View key={semesterIndex} style={[styles.semesterContainer, styles.ghostShimmer]}>
          <View style={[styles.ghostLine, { width: '60%', height: 16, marginBottom: 10 }]} />

          {/* Ghost course containers */}
          {[1, 2, 3, 4].map((courseIndex) => (
            <View key={courseIndex} style={[styles.courseContainer, styles.ghostShimmer]}>
              <View style={styles.courseHeader}>
                <View style={[styles.ghostLine, { width: '70%', height: 16 }]} />
                <View style={[styles.ghostLine, { width: '15%', height: 20 }]} />
              </View>
              <View style={styles.courseDetails}>
                <View style={[styles.ghostLine, { width: '30%', height: 12 }]} />
                <View style={[styles.ghostLine, { width: '20%', height: 12 }]} />
                <View style={[styles.ghostLine, { width: '25%', height: 12 }]} />
              </View>
            </View>
          ))}

          {/* Ghost GPA container */}
          <View style={[styles.gpaContainer, styles.ghostShimmer]}>
            <View style={[styles.ghostLine, { width: '40%', height: 14 }]} />
            <View style={[styles.ghostLine, { width: '30%', height: 14 }]} />
          </View>
        </View>
      ))}
    </View>
  );



  // Android-specific ghost loading for selected year (matches iOS style)
  const AndroidYearGhostLoading = ({ yearText }) => (
    <View>
      {/* Note: Cumulative GPA ghost loading is handled by the main section above, not here */}

      {/* Year Container with Course Data */}
      <View style={styles.yearContainer}>
        <View style={styles.ghostShimmer}>
          <Text style={styles.yearTitle}>{selectedYear?.displayText || 'Loading...'}</Text>
          <Text style={styles.yearSummary}>Loading courses...</Text>
        </View>

        {/* Ghost semester containers - same as iOS */}
        {[1, 2, 3].map((semesterIndex) => (
          <View key={semesterIndex} style={[styles.semesterContainer, styles.ghostShimmer]}>
            <View style={[styles.ghostLine, { width: '60%', height: 16, marginBottom: 10 }]} />

            {/* Ghost course containers */}
            {[1, 2, 3, 4].map((courseIndex) => (
              <View key={courseIndex} style={[styles.courseContainer, styles.ghostShimmer]}>
                <View style={styles.courseHeader}>
                  <View style={[styles.ghostLine, { width: '70%', height: 16 }]} />
                  <View style={[styles.ghostLine, { width: '15%', height: 20 }]} />
                </View>
                <View style={styles.courseDetails}>
                  <View style={[styles.ghostLine, { width: '30%', height: 12 }]} />
                  <View style={[styles.ghostLine, { width: '20%', height: 12 }]} />
                  <View style={[styles.ghostLine, { width: '25%', height: 12 }]} />
                </View>
              </View>
            ))}

            {/* Ghost GPA container */}
            <View style={[styles.gpaContainer, styles.ghostShimmer]}>
              <View style={[styles.ghostLine, { width: '40%', height: 14 }]} />
              <View style={[styles.ghostLine, { width: '30%', height: 14 }]} />
            </View>
          </View>
        ))}
      </View>
    </View>
  );



  // Cache management functions
  const CACHE_KEY = 'transcript_cache';
  const CACHE_TIMESTAMP_KEY = 'transcript_cache_timestamp';
  const CUMULATIVE_GPA_CACHE_KEY = 'cumulative_gpa_permanent_cache';

  // Save cumulative GPA to permanent cache
  const saveCumulativeGPAToCache = async (gpa) => {
    try {
      // Convert to string since AsyncStorage only accepts strings
      const gpaString = typeof gpa === 'number' ? gpa.toString() : String(gpa);
      await AsyncStorage.setItem(CUMULATIVE_GPA_CACHE_KEY, gpaString);
      console.log('📦 Cumulative GPA cached permanently:', gpaString);
    } catch (error) {
      console.error('❌ Error saving cumulative GPA to cache:', error);
    }
  };

  // Load cumulative GPA from permanent cache
  const loadCumulativeGPAFromCache = async () => {
    try {
      const cachedGPA = await AsyncStorage.getItem(CUMULATIVE_GPA_CACHE_KEY);
      if (cachedGPA) {
        console.log('📦 Loaded cached cumulative GPA:', cachedGPA);
        return cachedGPA;
      }
    } catch (error) {
      console.error('❌ Error loading cumulative GPA from cache:', error);
    }
    return null;
  };

  const saveToCache = async (data) => {
    try {
      const cacheData = {
        transcriptData: data.transcriptData,
        academicYears: data.academicYears,
        cumulativeGPA: data.cumulativeGPA,
        hasEvaluationOptions: data.hasEvaluationOptions,
        evaluationOptions: data.evaluationOptions,
        timestamp: Date.now()
      };
      await AsyncStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
      await AsyncStorage.setItem(CACHE_TIMESTAMP_KEY, Date.now().toString());
      console.log('📦 Transcript data cached successfully');
    } catch (error) {
      console.log('❌ Error saving to cache:', error);
    }
  };

  // New function to save cache from independent loading system
  const saveCacheFromIndependentSystem = async () => {
    try {
      if (Platform.OS === 'ios') {
        // Convert yearDataMap back to transcriptData array for caching
        const transcriptDataArray = Array.from(yearDataMap.values()).filter(Boolean);

        const cacheData = {
          transcriptData: transcriptDataArray,
          academicYears: academicYears,
          cumulativeGPA: cumulativeGPA,
          hasEvaluationOptions: hasEvaluationOptions,
          evaluationOptions: evaluationOptions,
          timestamp: Date.now()
        };

        await AsyncStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
        await AsyncStorage.setItem(CACHE_TIMESTAMP_KEY, Date.now().toString());
        console.log('📦 iOS: Independent system data cached successfully');
      }
    } catch (error) {
      console.log('❌ Error saving independent system cache:', error);
    }
  };

  const loadFromCache = async () => {
    try {
      const cachedData = await AsyncStorage.getItem(CACHE_KEY);
      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        console.log('📦 Loading cached transcript data');

        // Set cached data to state (legacy)
        setTranscriptData(parsedData.transcriptData || []);
        setAcademicYears(parsedData.academicYears || []);
        setCumulativeGPA(parsedData.cumulativeGPA);
        setHasEvaluationOptions(parsedData.hasEvaluationOptions || false);
        setEvaluationOptions(parsedData.evaluationOptions || []);
        setHasCachedData(true);
        setIsLoading(false);

        // Set GPA loading state based on whether we have cached GPA
        if (parsedData.cumulativeGPA !== null && parsedData.cumulativeGPA !== undefined) {
          setIsGPALoading(false); // We have cached GPA, no need to show loading
        } else {
          setIsGPALoading(true); // No cached GPA, show loading
        }

        // Mark evaluation check as complete since we have cached data
        setIsEvaluationCheckComplete(true);

        // Mark evaluation check as complete since we have cached data
        setIsEvaluationCheckComplete(true);

        // Initialize new independent loading system with cached data
        if (Platform.OS === 'ios' && parsedData.transcriptData && parsedData.academicYears) {
          const newDataMap = new Map();
          const newLoadingStates = new Map();

          // Populate data map with cached data
          parsedData.transcriptData.forEach(yearData => {
            if (yearData.year) {
              newDataMap.set(yearData.year, yearData);
              newLoadingStates.set(yearData.year, 'completed');
            }
          });

          // Set any remaining years as loading (in case cache is incomplete)
          parsedData.academicYears.forEach(year => {
            if (!newDataMap.has(year.text)) {
              newDataMap.set(year.text, null);
              newLoadingStates.set(year.text, 'loading');
            }
          });

          setYearDataMap(newDataMap);
          setYearLoadingStates(newLoadingStates);

          console.log('📦 iOS: Independent loading system initialized with cached data');
        }

        return true; // Cache loaded successfully
      }
      return false; // No cache found
    } catch (error) {
      console.log('❌ Error loading from cache:', error);
      return false;
    }
  };

  const clearCache = async () => {
    try {
      await AsyncStorage.removeItem(CACHE_KEY);
      await AsyncStorage.removeItem(CACHE_TIMESTAMP_KEY);
      console.log('🗑️ Transcript cache cleared');
    } catch (error) {
      console.log('❌ Error clearing cache:', error);
    }
  };

  // Check for academic years dropdown
  const checkForAcademicYears = async () => {
    if (!webViewRef.current) return;

    console.log('🔍 Checking for academic years dropdown...');

    // Get student ID from storage to determine enrollment year
    const storedStudentId = await AsyncStorage.getItem('student_id');
    const enrollmentYear = getEnrollmentYear(storedStudentId);

    console.log('📅 Student ID:', storedStudentId);
    console.log('📅 Enrollment Year:', enrollmentYear);

    const jsCode = `
      (function() {
        try {
          const academicYearsElement = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_stdYrLst');

          if (academicYearsElement) {
            console.log('✅ Academic years element found!');

            // Extract options from the element
            const options = [];
            const optionElements = academicYearsElement.querySelectorAll('option');

            for (let i = 0; i < optionElements.length; i++) {
              const option = optionElements[i];
              if (option.value && option.textContent && option.textContent.trim() !== '') {
                options.push({
                  value: option.value,
                  text: option.textContent.trim()
                });
              }
            }

            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'academic_years_found',
              options: options,
              enrollmentYear: '${enrollmentYear}'
            }));
          } else {
            console.log('❌ Academic years element not found');
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'academic_years_not_found'
            }));
          }
        } catch (error) {
          console.log('❌ Error checking academic years:', error);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'academic_years_error',
            error: error.message
          }));
        }
      })();
      true;
    `;

    webViewRef.current.injectJavaScript(jsCode);
  };

  // Filter academic years from newest down to enrollment year
  const filterYearsToEnrollment = (allYears, enrollmentYear) => {
    if (!enrollmentYear || allYears.length === 0) return allYears;

    // Remove duplicates first by creating a Map with year text as key
    const uniqueYearsMap = new Map();
    allYears.forEach(year => {
      if (year.text && year.text.trim() !== '') {
        uniqueYearsMap.set(year.text, year);
      }
    });

    // Convert back to array
    const uniqueYears = Array.from(uniqueYearsMap.values());
    console.log(`🔍 Removed duplicates: ${allYears.length} → ${uniqueYears.length} years`);

    // Sort years in descending order (newest first)
    const sortedYears = uniqueYears.sort((a, b) => {
      // Extract year from text like "2024-2025" or "2023-2024"
      const yearA = parseInt(a.text.split('-')[0]);
      const yearB = parseInt(b.text.split('-')[0]);
      return yearB - yearA; // Descending order
    });

    // Find the enrollment year index
    const enrollmentIndex = sortedYears.findIndex(year => year.text === enrollmentYear);

    if (enrollmentIndex === -1) {
      // If enrollment year not found, return all years
      console.log('⚠️ Enrollment year not found in available years, showing all');
      return sortedYears;
    }

    // Return years from newest down to enrollment year (inclusive)
    const filteredYears = sortedYears.slice(0, enrollmentIndex + 1);
    console.log(`📅 Filtered ${filteredYears.length} years from ${filteredYears[0]?.text} to ${enrollmentYear}`);
    return filteredYears;
  };

  // Create multiple WebViews for transcript data extraction (iOS: Independent loading, Android: Keep new logic)
  const createTranscriptWebViews = async (years, isBackgroundRefresh = false) => {
    // Prevent multiple simultaneous WebView creations (but allow during refresh)
    const now = Date.now();
    if (webViewCreationInProgress.current && !isRefreshing) {
      console.log('⚠️ WebView creation already in progress, skipping...');
      return;
    }

    // Prevent rapid successive calls (debounce) - but allow during refresh
    if (!isRefreshing && now - lastWebViewCreationTime.current < 2000) {
      console.log('⚠️ WebView creation called too recently, debouncing...');
      return;
    }

    // During refresh, always allow WebView creation
    if (isRefreshing) {
      console.log('🔄 Refresh in progress, allowing WebView creation');
      webViewCreationInProgress.current = false; // Reset flag during refresh
      lastWebViewCreationTime.current = 0; // Reset debounce during refresh
    }

    webViewCreationInProgress.current = true;
    lastWebViewCreationTime.current = now;

    // Set a timeout to automatically reset the flag if WebView creation gets stuck
    const creationTimeout = setTimeout(() => {
      console.log('⏰ WebView creation timeout reached, resetting flags');
      webViewCreationInProgress.current = false;
      lastWebViewCreationTime.current = 0;
    }, 15000); // 15 second timeout

    if (Platform.OS === 'ios') {
      // iOS: New independent loading approach
      console.log(`🚀 iOS: Creating ${years.length} WebViews for independent transcript data extraction`);
      console.log('📋 Years to process:', years);
      console.log('🔄 Is background refresh:', isBackgroundRefresh);

      // Check if WebViews already exist for these years to prevent duplicates
      // Skip this check during refresh since WebViews should have been cleared
      const existingWebViews = transcriptWebViews.filter(wv =>
        years.some(year => year.text === wv.year.text)
      );

      if (existingWebViews.length > 0 && !isBackgroundRefresh && !isRefreshing) {
        console.log('⚠️ iOS: WebViews already exist for some years, skipping creation');
        console.log('📋 Existing WebViews:', existingWebViews.map(wv => wv.year.text));
        return;
      }

      if (isRefreshing) {
        console.log('🔄 Refresh in progress - forcing WebView creation even if some exist');
      }

      // For background refresh, don't change loading states or clear data - keep existing UI
      if (!isBackgroundRefresh) {
        // Update loading states for years being processed (only for non-background refresh)
        setYearLoadingStates(prevStates => {
          const newStates = new Map(prevStates);
          years.forEach(year => {
            // Only set to loading if not already set or if it's a retry
            if (!newStates.has(year.text) || newStates.get(year.text) === 'failed') {
              newStates.set(year.text, 'loading');
            }
          });
          return newStates;
        });

        setYearDataMap(prevMap => {
          const newMap = new Map(prevMap);
          years.forEach(year => {
            // Only clear data for non-background refresh
            newMap.set(year.text, null);
          });
          return newMap;
        });
      } else {
        console.log('🔄 Background refresh: Keeping existing UI states and data visible');
      }

      // Clear any existing WebViews first only if not background refresh
      if (!isBackgroundRefresh) {
        setTranscriptWebViews([]);
      }

      // Get stored credentials
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('❌ No stored credentials found');
        // Mark all years as failed
        const failedStates = new Map();
        years.forEach(year => failedStates.set(year.text, 'failed'));
        setYearLoadingStates(failedStates);

        // Reset WebView creation flag on error
        webViewCreationInProgress.current = false;
        clearTimeout(creationTimeout);
        console.log('❌ WebView creation failed (no credentials), flags reset');
        return;
      }

      // Create WebView configurations for each year with staggered creation
      const webViewConfigs = years.map((year, index) => ({
        id: `transcript-${index}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`, // More unique ID
        year: year,
        url: `https://${storedUsername}:${storedPassword}@apps.guc.edu.eg/student_ext/Grade/Transcript_001.aspx?t=${Date.now()}&idx=${index}&bg=${isBackgroundRefresh ? 1 : 0}`, // Cache busting with background flag
        index: index,
        isBackgroundRefresh: isBackgroundRefresh
      }));

      // Create WebViews with staggered timing to ensure complete isolation
      setTimeout(() => {
        // Double-check we're not refreshing before creating WebViews
        if (!isRefreshing) {
          if (isBackgroundRefresh) {
            // For background refresh, append to existing WebViews
            setTranscriptWebViews(prevWebViews => [...prevWebViews, ...webViewConfigs]);
            // Extend refs array
            transcriptWebViewRefs.current = [
              ...transcriptWebViewRefs.current,
              ...new Array(years.length).fill(null)
            ];
            console.log('✅ Background WebViews appended to existing ones');
          } else {
            // For normal loading, replace all WebViews
            setTranscriptWebViews(webViewConfigs);
            // Initialize refs array
            transcriptWebViewRefs.current = new Array(years.length).fill(null);
            console.log('✅ WebViews created for normal loading');
          }

          // Reset WebView creation flag since creation is complete
          webViewCreationInProgress.current = false;
          clearTimeout(creationTimeout);
          console.log('✅ WebView creation completed, flags reset');
        }
      }, 500);
      formSubmittedFlags.current = new Array(years.length).fill(false);

      // Add individual timeouts for each year (8 seconds per trial - balanced for reliability)
      years.forEach((year, index) => {
        const timeoutDuration = 8000 + (index * 1000); // Staggered timeouts: 8s, 9s, 10s, etc.
        const timeoutId = setTimeout(() => {
          console.log(`⏰ Initial timeout reached for year: ${year.text} after ${timeoutDuration}ms`);
          // Check if this year is still processing
          setYearLoadingStates(currentStates => {
            const currentState = currentStates.get(year.text);
            if (currentState === 'loading') {
              console.log(`⏰ Year ${year.text} timed out, triggering retry logic`);
              // Trigger the same error handling as extraction errors
              handleTranscriptWebViewMessage({
                nativeEvent: {
                  data: JSON.stringify({
                    type: 'transcript_extraction_error',
                    error: 'Initial timeout reached',
                    year: year.text
                  })
                }
              }, year.text);
            }
            return currentStates; // Return unchanged states
          });
        }, timeoutDuration);

        // Store timeout ID for potential cleanup
        year.timeoutId = timeoutId;
      });

    } else {
      // Android: Keep new complex logic
      console.log(`🚀 Android: Creating WebViews for transcript data extraction`);
      console.log('📋 All available years:', years);
      console.log('🔄 Is background refresh:', isBackgroundRefresh);

      // Check if WebViews already exist to prevent duplicates
      if (transcriptWebViews.length > 0 && !isBackgroundRefresh) {
        console.log('⚠️ Android: WebViews already exist, skipping creation');
        return;
      }

      // Optimize for background refresh: only fetch last 2 years if student has more than 2 years
      let yearsToProcess = years;
      if (isBackgroundRefresh && years.length > 2) {
        yearsToProcess = years.slice(0, 2); // Get the first 2 years (newest)
        console.log('⚡ Background refresh optimization: Processing only last 2 years');
        console.log('📋 Years to process (optimized):', yearsToProcess);
      } else {
        console.log('📋 Processing all years:', yearsToProcess);
      }

      // Clear any existing WebViews first only if not background refresh
      if (!isBackgroundRefresh) {
        setTranscriptWebViews([]);
      }

      // Get stored credentials
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('❌ No stored credentials found');
        // Reset WebView creation flag on error
        webViewCreationInProgress.current = false;
        clearTimeout(creationTimeout);
        console.log('❌ Android WebView creation failed (no credentials), flags reset');
        return;
      }

      // Create WebView configurations for each year with enhanced session isolation
      const encodedUsername = encodeURIComponent(storedUsername);
      const encodedPassword = encodeURIComponent(storedPassword);
      const webViewConfigs = yearsToProcess.map((year, index) => {
        const timestamp = Date.now();
        const sessionId = `${timestamp}-${index}-${year.value}-${Math.random().toString(36).substring(2, 9)}`;
        return {
          id: `transcript-${sessionId}`, // Unique ID with timestamp and year
          year: year,
          url: `https://${encodedUsername}:${encodedPassword}@apps.guc.edu.eg/student_ext/Grade/Transcript_001.aspx?t=${timestamp}&idx=${index}&year=${year.value}&session=${sessionId}&bg=${isBackgroundRefresh ? 1 : 0}`, // Enhanced cache busting with session isolation
          index: index,
          sessionId: sessionId, // Store session ID for debugging
          isBackgroundRefresh: isBackgroundRefresh
        };
      });

      // Create WebViews with staggered timing to ensure complete isolation
      setTranscriptWebViews([]);
      setTimeout(() => {
        // Double-check we're not refreshing before creating WebViews
        if (!isRefreshing) {
          setTranscriptWebViews(webViewConfigs);
          // Initialize refs array
          transcriptWebViewRefs.current = new Array(yearsToProcess.length).fill(null);
          formSubmittedFlags.current = new Array(yearsToProcess.length).fill(false);

          // Reset WebView creation flag since creation is complete
          webViewCreationInProgress.current = false;
          clearTimeout(creationTimeout);
          console.log('✅ Android WebView creation completed, flags reset');
        }
      }, 500);

      // Add individual timeouts for each year (10 seconds per trial)
      yearsToProcess.forEach((year) => {
        setTimeout(() => {
          console.log(`⏰ Timeout reached for year: ${year.text}`);
          // Check if this year is still processing
          const isStillProcessing = !transcriptData.some(data => data.year === year.text);
          if (isStillProcessing) {
            console.log(`⏰ Year ${year.text} timed out, triggering retry logic`);
            // Trigger the same error handling as extraction errors
            handleTranscriptWebViewMessage({
              nativeEvent: {
                data: JSON.stringify({
                  type: 'transcript_extraction_error',
                  error: 'Timeout reached',
                  year: year.text
                })
              }
            }, year.text);
          }
        }, 10000); // 10 seconds per trial
      });
    }

    // Always reset the creation flag after a delay
    setTimeout(() => {
      webViewCreationInProgress.current = false;
      console.log('✅ WebView creation flag reset');
    }, 1000);
  };

  // Create transcript WebViews with provided credentials (for background refresh)
  const createTranscriptWebViewsWithCredentials = async (years, isBackgroundRefresh = false, username, password) => {
    console.log(`🚀 Creating ${years.length} WebViews with provided credentials for background refresh`);

    // Prevent multiple simultaneous WebView creations
    const now = Date.now();
    if (webViewCreationInProgress.current && !isRefreshing) {
      console.log('⚠️ WebView creation already in progress, skipping...');
      return;
    }

    // During refresh, always allow WebView creation
    if (isRefreshing) {
      console.log('🔄 Refresh in progress, allowing WebView creation');
      webViewCreationInProgress.current = false;
      lastWebViewCreationTime.current = 0;
    }

    webViewCreationInProgress.current = true;
    lastWebViewCreationTime.current = now;

    // Set a timeout to automatically reset the flag if WebView creation gets stuck
    const creationTimeout = setTimeout(() => {
      console.log('⏰ WebView creation timeout reached, resetting flags');
      webViewCreationInProgress.current = false;
      lastWebViewCreationTime.current = 0;
    }, 15000); // 15 second timeout

    // Create WebView configurations for each year with provided credentials
    const webViewConfigs = years.map((year, index) => ({
      id: `transcript-bg-${index}-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      year: year,
      url: `https://${encodeURIComponent(username)}:${encodeURIComponent(password)}@apps.guc.edu.eg/student_ext/Grade/Transcript_001.aspx?t=${Date.now()}&idx=${index}&bg=1`,
      index: index,
      isBackgroundRefresh: isBackgroundRefresh
    }));

    // For background refresh, append to existing WebViews
    setTimeout(() => {
      if (!isRefreshing) {
        if (isBackgroundRefresh) {
          // For background refresh, append to existing WebViews
          setTranscriptWebViews(prevWebViews => [...prevWebViews, ...webViewConfigs]);
          // Extend refs array
          transcriptWebViewRefs.current = [
            ...transcriptWebViewRefs.current,
            ...new Array(years.length).fill(null)
          ];
          console.log('✅ Background WebViews appended to existing ones');
        } else {
          // For normal loading, replace all WebViews
          setTranscriptWebViews(webViewConfigs);
          transcriptWebViewRefs.current = new Array(years.length).fill(null);
          console.log('✅ WebViews created for normal loading');
        }

        // Reset WebView creation flag since creation is complete
        webViewCreationInProgress.current = false;
        clearTimeout(creationTimeout);
        console.log('✅ WebView creation with credentials completed, flags reset');
      }
    }, 500);

    formSubmittedFlags.current = [...(formSubmittedFlags.current || []), ...new Array(years.length).fill(false)];
  };

  // Handle WebView load completion and submit form for year selection
  const handleTranscriptWebViewLoad = (year, index) => {
    console.log(`📋 WebView loaded for year: ${year.text} (value: ${year.value}, index: ${index})`);

    if (Platform.OS === 'ios') {
      // iOS: Use old simple approach
      const webViewRef = transcriptWebViewRefs.current[index];
      if (!webViewRef) {
        console.log('❌ WebView ref not available for index:', index);
        return;
      }

      // Wait longer for the page to fully load and render, then submit form to select year
      setTimeout(() => {
        console.log(`📝 iOS: Submitting form for ${year.text} after extended wait`);
        submitFormForYear(year, index, webViewRef);
      }, 4000); // Increased from 2s to 4s for better reliability

    } else {
      // Android: Keep complex retry logic
      console.log(`🔒 Session isolation check - WebView ${index} for ${year.text}`);
      console.log("Test" + transcriptWebViewRefs.current[index]);

      // Android production build workaround - retry with longer delays
      const checkRefAndSubmit = (attempt = 1) => {
        const webViewRef = transcriptWebViewRefs.current?.[index];

        if (!webViewRef) {
          const maxAttempts = 15; // More attempts for Android
          const delay = 1500; // Longer delay for Android

          if (attempt <= maxAttempts) {
            console.log(`⏳ WebView ref not available for index ${index}, attempt ${attempt}/${maxAttempts}, retrying in ${delay}ms...`);
            setTimeout(() => checkRefAndSubmit(attempt + 1), delay);
            return;
          } else {
            console.log('❌ WebView ref not available for index:', index, `after ${maxAttempts} attempts`);
            // Android-specific fallback: Try to submit without ref using postMessage
            console.log('🤖 Android: Attempting ref-less form submission...');
            attemptReflessSubmission(year, index);
            return;
          }
        }

        console.log(`✅ WebView ref found for index ${index}, submitting form...`);
        // Wait a bit for the page to fully load, then submit form to select year
        setTimeout(() => {
          submitFormForYear(year, index, webViewRef);
        }, 2000);
      };

      // Start checking with initial delay
      setTimeout(() => checkRefAndSubmit(), 500);
    }
  };

  // Android fallback: Attempt form submission without ref
  const attemptReflessSubmission = (year, index) => {
    console.log('🤖 Android: Implementing ref-less submission fallback...');
    // This would require a different approach, possibly using a different WebView library
    // or implementing a custom solution for Android production builds
    console.log('🤖 Android: Ref-less submission not yet implemented - this is a known Android production issue');
  };

  // Submit form to select the year (separate from data extraction)
  const submitFormForYear = (year, index, webViewRef) => {
    // Check if form was already submitted for this WebView
    if (formSubmittedFlags.current[index]) {
      console.log(`⚠️ Form already submitted for ${year.text}, skipping...`);
      return;
    }

    // Check if webViewRef is valid before proceeding
    if (!webViewRef || !webViewRef.injectJavaScript) {
      console.log(`❌ WebView ref is invalid for form submission for year ${year.text}`);
      // Trigger error handling
      handleTranscriptWebViewMessage({
        nativeEvent: {
          data: JSON.stringify({
            type: 'transcript_extraction_error',
            error: 'WebView ref is invalid for form submission',
            year: year.text
          })
        }
      }, year.text);
      return;
    }

    console.log(`📝 Submitting form for year: ${year.text} (value: ${year.value}, index: ${index})`);

    // Mark as submitted to prevent multiple submissions
    formSubmittedFlags.current[index] = true;

    if (Platform.OS === 'ios') {
      // iOS: Use improved approach with basic waiting
      const jsCode = `
        (function() {
          try {
            console.log('🔄 iOS: Setting up form submission for year: ${year.text} with value: ${year.value}');

            // Simple waiting function for iOS
            function waitForDropdownIOS(attempt = 1) {
              console.log('🔍 iOS: Waiting for dropdown, attempt: ' + attempt + '/15');

              const yearDropdown = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_stdYrLst');

              if (yearDropdown && yearDropdown.options && yearDropdown.options.length > 1) {
                console.log('✅ iOS: Year dropdown found with ' + yearDropdown.options.length + ' options');

                // Set the dropdown value
                console.log('🎯 iOS: Setting dropdown value to: ${year.value}');
                yearDropdown.value = '${year.value}';

                // Trigger change event
                const changeEvent = new Event('change', { bubbles: true });
                yearDropdown.dispatchEvent(changeEvent);

                // Also trigger onchange if it exists
                if (yearDropdown.onchange) {
                  yearDropdown.onchange(changeEvent);
                }

                // Submit form after a longer delay to ensure processing
                setTimeout(function() {
                  console.log('📝 iOS: Current dropdown value: ' + yearDropdown.value);

                  const form = yearDropdown.closest('form') || document.forms[0];
                  if (form) {
                    console.log('✅ iOS: Form found, submitting for year: ${year.text}');

                    // Try multiple submission methods
                    try {
                      // Method 1: Direct form submission
                      form.submit();
                    } catch (e) {
                      console.log('⚠️ iOS: Direct submit failed, trying alternative method');
                      // Method 2: Find and click submit button
                      const submitBtn = form.querySelector('input[type="submit"]') ||
                                       form.querySelector('button[type="submit"]') ||
                                       form.querySelector('input[value*="Submit"]');
                      if (submitBtn) {
                        submitBtn.click();
                      } else {
                        // Method 3: Trigger form submit event
                        const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
                        form.dispatchEvent(submitEvent);
                      }
                    }
                  } else {
                    console.log('❌ iOS: Form not found');
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                      type: 'transcript_extraction_error',
                      error: 'Form not found',
                      year: '${year.text}'
                    }));
                  }
                }, 1000); // Increased delay from 300ms to 1000ms

              } else {
                // Try again if less than 15 attempts
                if (attempt < 15) {
                  setTimeout(function() {
                    waitForDropdownIOS(attempt + 1);
                  }, 1000);
                } else {
                  console.log('❌ iOS: Dropdown not found after 15 attempts');
                  window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'transcript_extraction_error',
                    error: 'Year dropdown not found after waiting',
                    year: '${year.text}'
                  }));
                }
              }
            }

            // Start waiting
            waitForDropdownIOS();

          } catch (error) {
            console.log('❌ iOS: Error setting up form submission:', error);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'transcript_extraction_error',
              error: error.message,
              year: '${year.text}'
            }));
          }
        })();
        true;
      `;

      webViewRef.injectJavaScript(jsCode);

    } else {
      // Android: Keep complex waiting logic
      const jsCode = `
        (function() {
          try {
            console.log('🔄 Android: Setting up form submission for year: ${year.text} with value: ${year.value}');

            // Function to wait for dropdown to appear
            function waitForDropdown(attempt = 1, maxAttempts = 30) {
              console.log('🔍 Waiting for dropdown, attempt: ' + attempt + '/' + maxAttempts);

              const yearDropdown = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_stdYrLst');

              if (yearDropdown && yearDropdown.options && yearDropdown.options.length > 0) {
                console.log('✅ Year dropdown found with ' + yearDropdown.options.length + ' options');

                // Verify the target year value exists in dropdown
                let targetOptionExists = false;
                for (let i = 0; i < yearDropdown.options.length; i++) {
                  if (yearDropdown.options[i].value === '${year.value}') {
                    targetOptionExists = true;
                    console.log('✅ Target year ${year.value} found in dropdown at index: ' + i);
                    break;
                  }
                }

                if (!targetOptionExists) {
                  console.log('❌ Target year ${year.value} not found in dropdown options');
                  window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'transcript_extraction_error',
                    error: 'Target year ${year.value} not found in dropdown',
                    year: '${year.text}'
                  }));
                  return;
                }

                // Set the dropdown value
                console.log('🎯 Setting dropdown value to: ${year.value}');
                yearDropdown.value = '${year.value}';

                // Trigger multiple events to ensure the selection is registered
                const changeEvent = new Event('change', { bubbles: true });
                yearDropdown.dispatchEvent(changeEvent);

                // Also trigger onchange if it exists
                if (yearDropdown.onchange) {
                  yearDropdown.onchange(changeEvent);
                }

                // Wait a moment for the change to register, then submit
                setTimeout(function() {
                  console.log('📝 Current dropdown value after setting: ' + yearDropdown.value);

                  // Find and submit the form
                  const form = yearDropdown.closest('form') || document.forms[0];
                  if (form) {
                    console.log('✅ Form found, submitting for year: ${year.text}');

                    // Try multiple submission methods for better compatibility
                    try {
                      form.submit();
                    } catch (submitError) {
                      console.log('⚠️ Standard submit failed, trying alternative method');
                      // Try to find and click submit button as fallback
                      const submitButton = form.querySelector('input[type="submit"]') ||
                                         form.querySelector('button[type="submit"]') ||
                                         form.querySelector('input[value*="Submit"]');
                      if (submitButton) {
                        console.log('✅ Found submit button, clicking it');
                        submitButton.click();
                      } else {
                        console.log('❌ No submit button found either');
                        window.ReactNativeWebView.postMessage(JSON.stringify({
                          type: 'transcript_extraction_error',
                          error: 'Form submission failed: ' + submitError.message,
                          year: '${year.text}'
                        }));
                      }
                    }
                  } else {
                    console.log('❌ Form not found');
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                      type: 'transcript_extraction_error',
                      error: 'Form not found',
                      year: '${year.text}'
                    }));
                  }
                }, 1000); // Increased wait time to 1 second for better reliability

              } else {
                // Dropdown not ready yet, try again
                if (attempt < maxAttempts) {
                  setTimeout(function() {
                    waitForDropdown(attempt + 1, maxAttempts);
                  }, 500); // Wait 500ms before next attempt
                } else {
                  console.log('❌ Dropdown not found after ' + maxAttempts + ' attempts');
                  window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'transcript_extraction_error',
                    error: 'Year dropdown not found after waiting',
                    year: '${year.text}'
                  }));
                }
              }
            }

            // Start waiting for dropdown
            waitForDropdown();

          } catch (error) {
            console.log('❌ Error setting up form submission:', error);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'transcript_extraction_error',
              error: error.message,
              year: '${year.text}'
            }));
          }
        })();
        true;
      `;

      webViewRef.injectJavaScript(jsCode);
    }
  };

  // Handle messages from transcript WebViews
  const handleTranscriptWebViewMessage = (event, yearText) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log(`📨 Transcript WebView message for ${yearText}:`, data.type);

      switch (data.type) {
        case 'transcript_data_extracted':
          console.log('✅ Transcript data extracted for year:', data.year);
          console.log('📊 Transcript data:', data.transcriptInfo);

          // Set cumulative GPA if available (only set once from the first response)
          if (data.cumulativeGPA !== null && cumulativeGPA === null) {
            console.log('📊 Setting cumulative GPA:', data.cumulativeGPA);
            setCumulativeGPA(data.cumulativeGPA);
            setIsGPALoading(false); // Stop GPA loading

            // Save to permanent cache if this is from the most recent 2 years
            if (Platform.OS === 'android' && selectedYear) {
              const isLast2Years = isInLast2Years(selectedYear, yearDropdownOptions);
              if (isLast2Years) {
                console.log('💾 Saving cumulative GPA to permanent cache (from last 2 years)');
                saveCumulativeGPAToCache(data.cumulativeGPA); // Don't await, run in background
              }
            } else if (Platform.OS === 'ios') {
              // For iOS, save cumulative GPA from any year (as it loads all years)
              console.log('💾 Saving cumulative GPA to permanent cache (iOS)');
              saveCumulativeGPAToCache(data.cumulativeGPA); // Don't await, run in background
            }
          }

          // Handle platform-specific behavior
          if (Platform.OS === 'ios') {
            // iOS: Update independent year data and loading state
            console.log('📱 iOS: Setting data for year:', data.year);

            // Update the year data map
            setYearDataMap(prevMap => {
              const newMap = new Map(prevMap);
              newMap.set(data.year, data.transcriptInfo);
              return newMap;
            });

            // Update loading state for this year
            setYearLoadingStates(prevStates => {
              const newStates = new Map(prevStates);
              newStates.set(data.year, 'completed');
              return newStates;
            });

            // Also update the legacy transcriptData for compatibility
            setTranscriptData(prevData => {
              // Check if this year already exists in the data
              const existingIndex = prevData.findIndex(item => item.year === data.year);
              if (existingIndex >= 0) {
                // Update existing year data
                const newData = [...prevData];
                newData[existingIndex] = data.transcriptInfo;
                return newData;
              } else {
                // Add new year data
                return [...prevData, data.transcriptInfo];
              }
            });

            console.log('✅ iOS: Year data updated independently:', data.year);

            // Save to cache after each year completes (background operation)
            setTimeout(() => {
              saveCacheFromIndependentSystem();
            }, 100);

            // Check if this was the last year to complete and stop any global loading indicators
            setYearLoadingStates(currentStates => {
              // Check if we're in background refresh mode
              const isCurrentlyUpdating = isUpdatingData;

              if (isCurrentlyUpdating) {
                // For background refresh, only check if the refreshing years are complete
                const refreshingYears = transcriptWebViews
                  .filter(wv => wv.isBackgroundRefresh)
                  .map(wv => wv.year.text);

                console.log('🔍 Background refresh check:', {
                  refreshingYears,
                  totalWebViews: transcriptWebViews.length,
                  backgroundWebViews: transcriptWebViews.filter(wv => wv.isBackgroundRefresh).length,
                  currentYear: data.year,
                  currentState: currentStates.get(data.year)
                });

                // If no background refresh WebViews found, check if this is a background refresh year
                // (This handles the case where WebViews are created but not properly marked)
                let yearsToCheck = refreshingYears;
                if (yearsToCheck.length === 0) {
                  // Fallback: Check if we're updating the last 2 years (2025-2026, 2024-2025)
                  const possibleBackgroundYears = ['2025-2026', '2024-2025'];
                  yearsToCheck = possibleBackgroundYears.filter(year => currentStates.has(year));
                  console.log('🔄 Fallback: Checking possible background years:', yearsToCheck);
                }

                const refreshingYearsCompleted = yearsToCheck.every(year => {
                  const state = currentStates.get(year);
                  return state === 'completed' || state === 'failed';
                });

                if (refreshingYearsCompleted && yearsToCheck.length > 0) {
                  console.log('✅ Background refresh completed for years:', yearsToCheck);
                  stopBackgroundRefresh();
                }
              } else {
                // For normal loading, check if all years are complete
                const allYearsCompleted = Array.from(currentStates.values()).every(state =>
                  state === 'completed' || state === 'failed'
                );

                if (allYearsCompleted) {
                  console.log('✅ All years have completed loading');
                  setIsLoadingTranscripts(false);
                  // Check if this was a background refresh
                  if (isUpdatingData) {
                    stopBackgroundRefresh();
                  } else {
                    setIsUpdatingData(false);
                    stopRotationAnimation();
                  }
                } else {
                  const stillLoading = Array.from(currentStates.entries())
                    .filter(([_, state]) => state === 'loading' || state === 'retrying')
                    .map(([year, _]) => year);
                  console.log(`📊 Still loading years: ${stillLoading.join(', ')}`);
                }
              }

              return currentStates; // Return unchanged states
            });
          }

          // Handle platform-specific behavior
          if (Platform.OS === 'android' && selectedYear) {
            // For Android: Set data for selected year only and cache it
            console.log('📱 Android: Setting data for selected year:', data.year);

            // Check if this is a background refresh
            const currentWebView = transcriptWebViews.find(wv => wv.year.text === data.year);
            const isBackgroundRefresh = currentWebView?.isBackgroundRefresh || false;

            if (isBackgroundRefresh) {
              console.log('📱 Android: Background refresh completed for year:', data.year);
              // Always update the displayed data for background refresh
              // (the cache comparison would require async, so we'll just update)
              setSelectedYearData(data.transcriptInfo);
              stopBackgroundRefresh();
            } else {
              console.log('📱 Android: Foreground load completed for year:', data.year);
              setSelectedYearData(data.transcriptInfo);
              setIsLoadingSelectedYear(false);
            }

            // Cache this year's data
            saveCachedYearData(data.year, data.transcriptInfo);

            // Remove only the WebView for this specific year (don't clear all)
            // Add safety check to prevent infinite loops
            setTranscriptWebViews(prevWebViews => {
              const filteredWebViews = prevWebViews.filter(wv => wv.year.text !== data.year);
              console.log(`📱 Android: Removed WebView for ${data.year}, remaining: ${filteredWebViews.length}`);
              return filteredWebViews;
            });
          } else {
            // For iOS: Add this year's data to the transcript data array (existing behavior)
            setTranscriptData(prevData => {
              const newData = [...prevData, data.transcriptInfo];
              console.log('📊 Total transcript data collected:', newData.length);

              // Determine expected count based on whether this is a background refresh
              const isBackgroundRefresh = isUpdatingData && hasCachedData;

              // For background refresh, we only process the last 2 years
              // For manual refresh, we process all years
              let expectedCount;
              if (isBackgroundRefresh && academicYears.length > 2) {
                expectedCount = Math.min(2, academicYears.length); // Only expect 2 years for background refresh
              } else {
                expectedCount = academicYears.length; // Expect all years for manual refresh
              }

              console.log('📊 Expected data count:', expectedCount, '(background refresh:', isBackgroundRefresh, ', total years:', academicYears.length, ')');

              // Check if we have data for expected years OR if we're doing background refresh and have at least the expected count
              const shouldStop = isBackgroundRefresh
                ? newData.length >= expectedCount // For background refresh, stop when we have enough data
                : newData.length === expectedCount; // For manual refresh, wait for exact count

              if (shouldStop) {
                console.log('✅ Expected transcript data collected! (Background refresh:', isBackgroundRefresh, ', Data count:', newData.length, ', Expected:', expectedCount, ')');
                setIsLoadingTranscripts(false);
                if (isBackgroundRefresh) {
                  stopBackgroundRefresh(); // Stop background refresh properly
                } else {
                  setIsUpdatingData(false);
                  stopRotationAnimation(); // Stop rotation when update is complete
                }

                // Save to cache when expected data is collected
                const cacheData = {
                  transcriptData: newData,
                  academicYears: academicYears,
                  cumulativeGPA: cumulativeGPA,
                  hasEvaluationOptions: hasEvaluationOptions,
                  evaluationOptions: evaluationOptions
                };
                saveToCache(cacheData);
              }

              return newData;
            });
          }
          break;

        case 'transcript_extraction_error':
          console.log('❌ Error extracting transcript data:', data.error, 'for year:', data.year);

          // Handle platform-specific error behavior
          if (Platform.OS === 'android' && selectedYear) {
            // For Android: Stop loading and clear WebView
            console.log('📱 Android: Error loading selected year data');

            // Check if this was a background refresh
            const currentWebView = transcriptWebViews.find(wv => wv.year.text === data.year);
            const isBackgroundRefresh = currentWebView?.isBackgroundRefresh || false;

            if (isBackgroundRefresh) {
              console.log('📱 Android: Background refresh error for year:', data.year);
              stopBackgroundRefresh();
            } else {
              console.log('📱 Android: Foreground load error for year:', data.year);
              setIsLoadingSelectedYear(false);
            }

            // Remove only the WebView for this specific year (don't clear all)
            setTranscriptWebViews(prevWebViews =>
              prevWebViews.filter(wv => wv.year.text !== data.year)
            );
          } else {
            // For iOS: Implement enhanced retry logic for failed years with longer timeouts
            const yearText = data.year;
            const currentAttempts = retryAttempts.get(yearText) || 0;
            const maxRetries = 3; // Changed to 3 retries for better success rate

            if (currentAttempts < maxRetries) {
              console.log(`🔄 iOS: Retrying failed year: ${yearText} (attempt ${currentAttempts + 1}/${maxRetries})`);

              // Update retry attempts
              setRetryAttempts(prev => new Map(prev.set(yearText, currentAttempts + 1)));

              // Find the failed year object
              const failedYear = academicYears.find(year => year.text === yearText);
              if (failedYear) {
                // Use custom delay: 2s for first retry, 5s for second and third retry (reduced for faster recovery)
                const retryDelay = currentAttempts === 0 ? 2000 : 5000;

                console.log(`🔄 Starting retry ${currentAttempts + 1} for year: ${yearText} in ${retryDelay}ms`);

                // Retry only this specific year after progressive delay
                setTimeout(async () => {
                  console.log(`🔄 Executing retry ${currentAttempts + 1} for year: ${yearText}`);

                  // Set loading state back to loading IMMEDIATELY when retry starts
                  setYearLoadingStates(prevStates => {
                    const newStates = new Map(prevStates);
                    newStates.set(yearText, 'loading');
                    return newStates;
                  });

                  // Get credentials for retry
                  const storedUsername = await AsyncStorage.getItem('guc_username');
                  const storedPassword = await AsyncStorage.getItem('guc_password');

                  if (storedUsername && storedPassword) {
                    // Create a single WebView for retry with improved configuration
                    const retryWebViewConfig = {
                      id: `transcript-retry-${currentAttempts + 1}-${Date.now()}-${Math.random().toString(36).substring(2, 6)}`,
                      year: failedYear,
                      url: `https://${encodeURIComponent(storedUsername)}:${encodeURIComponent(storedPassword)}@apps.guc.edu.eg/student_ext/Grade/Transcript_001.aspx?retry=${currentAttempts + 1}&t=${Date.now()}&attempt=${currentAttempts + 1}`,
                      index: 0, // Always use index 0 for retries
                      isBackgroundRefresh: false,
                      isRetry: true,
                      retryAttempt: currentAttempts + 1,
                      yearText: yearText // Add explicit year text for easier tracking
                    };

                    console.log(`🔧 Creating retry WebView for ${yearText}:`, {
                      id: retryWebViewConfig.id,
                      attempt: currentAttempts + 1,
                      url: retryWebViewConfig.url.substring(0, 100) + '...'
                    });

                    // Remove any existing WebViews for this year and add the retry WebView
                    setTranscriptWebViews(prevWebViews => {
                      // Remove ALL WebViews for this year (including original and any previous retries)
                      const filteredWebViews = prevWebViews.filter(wv => wv.year.text !== yearText);
                      console.log(`🔄 Removed existing WebViews for ${yearText}, adding retry WebView`);
                      return [...filteredWebViews, retryWebViewConfig];
                    });

                    // Extend refs arrays if needed
                    if (!transcriptWebViewRefs.current) transcriptWebViewRefs.current = [];
                    if (!formSubmittedFlags.current) formSubmittedFlags.current = [];

                    // Add timeout for this retry with custom duration: 12s for first retry, 15s for second and third retry (longer timeout for retries)
                    const retryTimeout = currentAttempts === 0 ? 12000 : 15000; // Longer timeouts for retries
                    setTimeout(() => {
                      console.log(`⏰ Retry timeout reached for year: ${yearText} (attempt ${currentAttempts + 1}) after ${retryTimeout}ms`);
                      setYearLoadingStates(currentStates => {
                        const currentState = currentStates.get(yearText);
                        if (currentState === 'loading') {
                          console.log(`⏰ Retry ${currentAttempts + 1} for year ${yearText} timed out, triggering next retry`);
                          // Trigger another retry or mark as failed
                          handleTranscriptWebViewMessage({
                            nativeEvent: {
                              data: JSON.stringify({
                                type: 'transcript_extraction_error',
                                error: `Retry ${currentAttempts + 1} timeout reached`,
                                year: yearText
                              })
                            }
                          }, yearText);
                        } else {
                          console.log(`⏰ Year ${yearText} state changed to ${currentState}, not triggering retry timeout`);
                        }
                        return currentStates;
                      });
                    }, retryTimeout);

                  } else {
                    console.log('❌ No credentials available for retry');
                    // Mark as failed if no credentials
                    setYearLoadingStates(prevStates => {
                      const newStates = new Map(prevStates);
                      newStates.set(yearText, 'failed');
                      return newStates;
                    });
                  }
                }, retryDelay);
              }
            } else {
              console.log(`❌ iOS: Max retries reached for year: ${yearText}, marking as failed`);

              // Mark as failed in both old and new state systems
              setFailedYears(prev => new Set(prev.add(yearText)));

              // Update loading state to failed
              setYearLoadingStates(prevStates => {
                const newStates = new Map(prevStates);
                newStates.set(yearText, 'failed');
                return newStates;
              });

              console.log('✅ iOS: Year marked as failed independently:', yearText);
              console.log('🔄 User can manually refresh to retry failed years');

              // Check if all years are now complete (including failed ones)
              setYearLoadingStates(currentStates => {
                const allYearsCompleted = Array.from(currentStates.values()).every(state =>
                  state === 'completed' || state === 'failed'
                );

                if (allYearsCompleted) {
                  console.log('✅ All years have completed loading (some failed)');
                  setIsLoadingTranscripts(false);
                  // Check if this was a background refresh
                  if (isUpdatingData) {
                    stopBackgroundRefresh();
                  } else {
                    setIsUpdatingData(false);
                    stopRotationAnimation();
                  }
                }

                return currentStates; // Return unchanged states
              });
            }
          }
          break;

        default:
          console.log('📨 Unknown transcript message type:', data.type);
          break;
      }
    } catch (error) {
      console.log('❌ Error parsing transcript WebView message:', error);
    }
  };

  // Extract transcript data for a specific year using individual WebView
  const extractTranscriptDataForYear = (year, index, webViewRef) => {
    console.log(`📊 Starting extraction for year: ${year.text} (value: ${year.value}, index: ${index})`);

    // Check if webViewRef is valid before proceeding
    if (!webViewRef || !webViewRef.injectJavaScript) {
      console.log(`❌ WebView ref is invalid for year ${year.text}, skipping extraction`);
      // Trigger error handling
      handleTranscriptWebViewMessage({
        nativeEvent: {
          data: JSON.stringify({
            type: 'transcript_extraction_error',
            error: 'WebView ref is invalid',
            year: year.text
          })
        }
      }, year.text);
      return;
    }

    // JavaScript code to select the year and extract data
    const jsCode = `
      (function() {
        try {
          console.log('� Extracting transcript data for year: ${year.text} with value: ${year.value}');

          // Extract data from all tables with class "table"
          extractAllTranscriptData('${year.text}', '${year.value}');

        } catch (error) {
          console.log('❌ Error extracting transcript data:', error);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'transcript_extraction_error',
            error: error.message,
            year: '${year.text}'
          }));
        }
      })();

      function extractAllTranscriptData(yearText, yearValue) {
        try {
          console.log('� Starting transcript data extraction for year:', yearText);

          // Function to wait for tables to appear
          function waitForTables(attempt = 1, maxAttempts = 20) {
            console.log('🔍 Waiting for tables, attempt: ' + attempt + '/' + maxAttempts);

            const tables = document.querySelectorAll('.table');
            console.log('📋 Found', tables.length, 'tables with class "table"');

            if (tables.length > 0) {
              console.log('✅ Tables found, proceeding with extraction');
              performDataExtraction(tables, yearText, yearValue);
            } else {
              // Tables not ready yet, try again
              if (attempt < maxAttempts) {
                setTimeout(function() {
                  waitForTables(attempt + 1, maxAttempts);
                }, 1000); // Wait 1 second before next attempt
              } else {
                console.log('❌ No tables found after ' + maxAttempts + ' attempts');
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'transcript_extraction_error',
                  error: 'No tables found after waiting',
                  year: yearText
                }));
              }
            }
          }

          // Start waiting for tables
          waitForTables();

        } catch (error) {
          console.log('❌ Error in extractAllTranscriptData:', error);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'transcript_extraction_error',
            error: error.message,
            year: yearText
          }));
        }
      }

      function performDataExtraction(tables, yearText, yearValue) {
        try {
          console.log('📊 Performing data extraction for', tables.length, 'tables');
          const allSemesters = [];

          // Process each table
          tables.forEach((table, tableIndex) => {
            console.log('📊 Processing table', tableIndex + 1, 'of', tables.length);

            try {
              // Extract the semester title from the first row with bgcolor="whitesmoke"
              const titleRow = table.querySelector('tr:first-child td[bgcolor="whitesmoke"]');
              const semesterTitle = titleRow ? titleRow.textContent.trim() : 'Table ' + (tableIndex + 1);
              console.log('📋 Semester title:', semesterTitle);

              // Extract course data from the table
              const courses = [];
              const allRows = table.querySelectorAll('tr');

              // Skip first row (title), second row (headers), and last row (GPA)
              // Look for course rows (they have 5 cells with course data)
              for (let i = 2; i < allRows.length - 1; i++) {
                const row = allRows[i];
                const cells = row.querySelectorAll('td');

                if (cells.length >= 5) {
                  // Extract text content from each cell
                  const semester = cells[0].textContent.trim();
                  const courseName = cells[1].textContent.trim();
                  const numeric = cells[2].textContent.trim();
                  const grade = cells[3].textContent.trim();
                  const hours = cells[4].textContent.trim();

                  // Only add if we have valid course data (not empty)
                  if (semester && courseName && numeric && grade && hours) {
                    courses.push({
                      semester: semester,
                      courseName: courseName,
                      numeric: parseFloat(numeric) || numeric,
                      grade: grade,
                      hours: parseInt(hours) || hours
                    });
                  }
                }
              }

              // Extract GPA information from the last row
              const gpaRow = table.querySelector('tr:last-child');
              let semesterGPA = null;
              let totalHours = null;

              if (gpaRow) {
                const gpaCells = gpaRow.querySelectorAll('td');
                if (gpaCells.length >= 5) {
                  // GPA is in the 3rd cell (index 2)
                  const gpaText = gpaCells[2].textContent.trim();
                  // Total hours is in the 5th cell (index 4)
                  const hoursText = gpaCells[4].textContent.trim();

                  semesterGPA = parseFloat(gpaText) || gpaText;
                  totalHours = parseInt(hoursText) || hoursText;
                }
              }

              const semesterData = {
                tableIndex: tableIndex,
                semesterTitle: semesterTitle,
                courses: courses,
                semesterGPA: semesterGPA,
                totalHours: totalHours,
                coursesCount: courses.length
              };

              allSemesters.push(semesterData);
              console.log('✅ Table', tableIndex + 1, 'processed:', courses.length, 'courses found');

            } catch (tableError) {
              console.log('❌ Error processing table', tableIndex + 1, ':', tableError);

              // Still add an entry for this table even if there's an error
              allSemesters.push({
                tableIndex: tableIndex,
                semesterTitle: 'Table ' + (tableIndex + 1) + ' (Error)',
                courses: [],
                semesterGPA: null,
                totalHours: null,
                coursesCount: 0,
                error: tableError.message
              });
            }
          });

          const transcriptData = {
            year: yearText,
            yearValue: yearValue,
            totalTables: tables.length,
            totalSemesters: allSemesters.length,
            semesters: allSemesters,
            extractedAt: new Date().toISOString(),
            summary: {
              totalCourses: allSemesters.reduce((sum, sem) => sum + sem.coursesCount, 0),
              semestersWithCourses: allSemesters.filter(sem => sem.coursesCount > 0).length,
              semestersWithGPA: allSemesters.filter(sem => sem.semesterGPA !== null && sem.semesterGPA !== '').length
            }
          };

          // Extract cumulative GPA
          let cumulativeGPA = null;
          const cumulativeGPAElement = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_cmGpaLbl');
          if (cumulativeGPAElement) {
            const gpaText = cumulativeGPAElement.textContent.trim();
            cumulativeGPA = parseFloat(gpaText) || gpaText;
            console.log('📊 Cumulative GPA found:', cumulativeGPA);
          } else {
            console.log('⚠️ Cumulative GPA element not found');
          }

          console.log('✅ All transcript data extracted successfully');
          console.log('📊 Summary:', transcriptData.summary.totalCourses, 'total courses across', transcriptData.summary.semestersWithCourses, 'semesters');

          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'transcript_data_extracted',
            year: yearText,
            transcriptInfo: transcriptData,
            cumulativeGPA: cumulativeGPA
          }));

        } catch (error) {
          console.log('❌ Error extracting transcript data:', error);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'transcript_extraction_error',
            error: error.message,
            year: yearText
          }));
        }
      }

      true;
    `;

    if (Platform.OS === 'ios') {
      // iOS: Use improved extraction with basic waiting
      const iosExtractionCode = `
        (function() {
          try {
            console.log('📊 iOS: Starting extraction for ${year.text}');

            // Simple waiting function for tables
            function waitForTablesIOS(attempt = 1) {
              console.log('🔍 iOS: Waiting for tables, attempt: ' + attempt + '/15');

              const tables = document.querySelectorAll('.table');
              console.log('📋 Found', tables.length, 'tables with class "table"');

              if (tables.length > 0) {
                console.log('✅ iOS: Tables found, proceeding with extraction');
                extractTablesIOS(tables);
              } else {
                if (attempt < 15) {
                  setTimeout(function() {
                    waitForTablesIOS(attempt + 1);
                  }, 1000);
                } else {
                  console.log('❌ iOS: No tables found after 15 attempts');
                  window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'transcript_extraction_error',
                    error: 'No tables found after waiting',
                    year: '${year.text}'
                  }));
                }
              }
            }

            function extractTablesIOS(tables) {

              const allSemesters = [];

              // Process each table
              tables.forEach((table, tableIndex) => {
              console.log('📊 Processing table', tableIndex + 1, 'of', tables.length);

              try {
                // Extract the semester title from the first row with bgcolor="whitesmoke"
                const titleRow = table.querySelector('tr:first-child td[bgcolor="whitesmoke"]');
                const semesterTitle = titleRow ? titleRow.textContent.trim() : 'Table ' + (tableIndex + 1);
                console.log('📋 Semester title:', semesterTitle);

                // Extract course data from the table
                const courses = [];
                const allRows = table.querySelectorAll('tr');

                // Skip first row (title), second row (headers), and last row (GPA)
                for (let i = 2; i < allRows.length - 1; i++) {
                  const row = allRows[i];
                  const cells = row.querySelectorAll('td');

                  if (cells.length >= 5) {
                    const semester = cells[0].textContent.trim();
                    const courseName = cells[1].textContent.trim();
                    const numeric = cells[2].textContent.trim();
                    const grade = cells[3].textContent.trim();
                    const hours = cells[4].textContent.trim();

                    if (courseName && courseName !== '') {
                      courses.push({
                        semester: semester,
                        courseName: courseName,
                        numeric: numeric,
                        grade: grade,
                        hours: hours
                      });
                    }
                  }
                }

                // Extract semester GPA and total hours from the last row
                const lastRow = allRows[allRows.length - 1];
                const lastCells = lastRow.querySelectorAll('td');
                let semesterGPA = null;
                let totalHours = null;

                if (lastCells.length >= 3) {
                  semesterGPA = lastCells[2].textContent.trim();
                  totalHours = lastCells[4] ? lastCells[4].textContent.trim() : null;
                }

                const semesterData = {
                  tableIndex: tableIndex,
                  semesterTitle: semesterTitle,
                  courses: courses,
                  semesterGPA: semesterGPA,
                  totalHours: totalHours,
                  coursesCount: courses.length
                };

                allSemesters.push(semesterData);
                console.log('✅ Table', tableIndex + 1, 'processed:', courses.length, 'courses found');

              } catch (tableError) {
                console.log('❌ Error processing table', tableIndex + 1, ':', tableError);
                allSemesters.push({
                  tableIndex: tableIndex,
                  semesterTitle: 'Table ' + (tableIndex + 1) + ' (Error)',
                  courses: [],
                  semesterGPA: null,
                  totalHours: null,
                  coursesCount: 0,
                  error: tableError.message
                });
              }
            });

            // Extract cumulative GPA using the same approach as Android
            let cumulativeGPA = null;
            const cumulativeGPAElement = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_cmGpaLbl');
            if (cumulativeGPAElement) {
              const gpaText = cumulativeGPAElement.textContent.trim();
              cumulativeGPA = parseFloat(gpaText) || gpaText;
              console.log('📊 iOS: Cumulative GPA found:', cumulativeGPA);
            } else {
              console.log('⚠️ iOS: Cumulative GPA element not found');
            }

            const transcriptData = {
              year: '${year.text}',
              semesters: allSemesters,
              summary: {
                totalCourses: allSemesters.reduce((sum, semester) => sum + semester.coursesCount, 0),
                semestersWithCourses: allSemesters.filter(semester => semester.coursesCount > 0).length
              }
            };

            console.log('✅ iOS: All transcript data extracted successfully');
            console.log('📊 Summary:', transcriptData.summary.totalCourses, 'total courses across', transcriptData.summary.semestersWithCourses, 'semesters');

              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'transcript_data_extracted',
                year: '${year.text}',
                transcriptInfo: transcriptData,
                cumulativeGPA: cumulativeGPA
              }));
            }

            // Start waiting for tables
            waitForTablesIOS();

          } catch (error) {
            console.log('❌ iOS: Error extracting transcript data:', error);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'transcript_extraction_error',
              error: error.message,
              year: '${year.text}'
            }));
          }
        })();
        true;
      `;

      webViewRef.injectJavaScript(iosExtractionCode);

    } else {
      // Android: Use complex extraction with waiting mechanisms
      webViewRef.injectJavaScript(jsCode);
    }
  };

  return (
    <View style={{ flex: 1 }} {...swipeGestureHandler.panHandlers}>
      <View style={styles.container}>
      {/* Sidebar Button */}
      <View style={styles.sidebarButtonContainer}>
        <TouchableOpacity
          style={styles.sidebarButton}
          onPress={openSidebar}
        >
          <HamburgerIcon size={20} color={theme.colors.primary} strokeWidth={3} />
        </TouchableOpacity>
      </View>

      {/* Page Title */}
      <View style={styles.titleContainer}>
        <Text style={styles.title}>Transcript</Text>
      </View>

      {/* Refresh Button */}
      <View style={styles.refreshButtonContainer}>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={handleRefresh}
          disabled={isRefreshing || isUpdatingData}
        >
          <Animated.View
            style={{
              transform: [{
                rotate: refreshRotation.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg']
                })
              }]
            }}
          >
            <RefreshIcon
              size={24}
              color={(isRefreshing || isUpdatingData) ? theme.colors.textSecondary : safeCurrentThemeName === 'navy' ? '#DC2626' : '#f1c40f'}
            />
          </Animated.View>
        </TouchableOpacity>
      </View>

      {/* Eye Button for Grade Visibility */}
      <View style={styles.eyeButtonContainer}>
        <TouchableOpacity
          style={styles.eyeButton}
          onPress={() => setIsGradesVisible(!isGradesVisible)}
        >
          {isGradesVisible ? (
            <Svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#9CA3AF" strokeWidth="2">
              <Path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
              <Line x1="1" y1="1" x2="23" y2="23"/>
            </Svg>
          ) : (
            <Svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke={safeCurrentThemeName === 'navy' ? '#DC2626' : '#EAB308'} strokeWidth="2">
              <Path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
              <Circle cx="12" cy="12" r="3"/>
            </Svg>
          )}
        </TouchableOpacity>
      </View>



      {/* Main Content Container - Split into two halves */}
      <View style={styles.contentContainer}>
        {(isLoading && !hasCachedData) ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#EAB308" />
            <Text style={styles.loadingText}>Loading transcript...</Text>
          </View>
        ) : transcriptUrl ? (
          <View style={styles.webViewContainer}>
            {/* Main Content - Full Height (Debug component temporarily hidden) */}
            <View style={styles.fullHeightContainer}>
            {/* Hidden WebView for transcript - Always show to prevent reload during background refresh */}
            <View style={styles.hiddenWebView}>
              <WebView
                ref={webViewRef}
                source={{ uri: transcriptUrl }}
                onLoad={handleWebViewLoad}
                onMessage={handleWebViewMessage}
                javaScriptEnabled={true}
                domStorageEnabled={true}
                mixedContentMode="compatibility"
                cacheEnabled={false}
                incognito={true}
              />
            </View>

            {/* Show loading only when no cached data and still checking */}
            {(!hasCachedData && (isCheckingEvaluation || (academicYears.length === 0 && !hasEvaluationOptions && !isLoadingTranscripts && transcriptData.length === 0))) ? (
              <View style={styles.checkingContainer}>
                <ActivityIndicator size="large" color="#EAB308" />
                <Text style={styles.checkingText}>
                  {isCheckingEvaluation ? 'Checking for course evaluations...' : 'Loading transcript...'}
                </Text>
              </View>
            ) : (
              <>
                {/* Evaluation Options - Only show if evaluation options found */}
                {hasEvaluationOptions && (
                  <View style={styles.evaluationButtonContainer}>
                    <Text style={styles.evaluationFoundText}>
                      You need to evaluate these courses first
                    </Text>

                    {/* Course List */}
                    <View style={styles.courseListContainer}>
                      {evaluationOptions.map((course, index) => (
                        <View key={index} style={styles.courseItem}>
                          <Text style={styles.courseText}>{course.text}</Text>
                        </View>
                      ))}
                    </View>

                    <TouchableOpacity
                      style={styles.evaluationButton}
                      onPress={navigateToEvaluate}
                    >
                      <Text style={styles.evaluationButtonText}>Go to Evaluate</Text>
                    </TouchableOpacity>
                  </View>
                )}

                {/* Transcript Data Display - Show when no evaluations needed and we have academic years */}
                {!hasEvaluationOptions && academicYears.length > 0 && (
                  <View style={styles.transcriptContainer}>

                    {/* Android Year Dropdown */}
                    {Platform.OS === 'android' && yearDropdownOptions.length > 0 && (
                      <View style={styles.androidDropdownContainer}>
                        <Text style={styles.dropdownLabel}>Select Academic Year:</Text>
                        <View style={styles.dropdownWrapper}>
                          <TouchableOpacity
                            style={styles.dropdown}
                            onPress={toggleDropdown}
                            disabled={isLoadingSelectedYear}
                          >
                            <View style={styles.dropdownContent}>
                              {selectedYear ? (
                                <Text style={styles.dropdownText}>{selectedYear.displayText}</Text>
                              ) : (
                                <Text style={styles.dropdownPlaceholder}>Select Year</Text>
                              )}
                            </View>
                            <Text style={[
                              styles.dropdownArrow,
                              { transform: [{ rotate: isDropdownVisible ? '180deg' : '0deg' }] }
                            ]}>
                              ▼
                            </Text>
                          </TouchableOpacity>

                          {/* Dropdown Options List */}
                          {isDropdownVisible && yearDropdownOptions.length > 0 && (
                            <View style={styles.dropdownList}>
                              <ScrollView
                                style={styles.optionsList}
                                showsVerticalScrollIndicator={true}
                                nestedScrollEnabled={true}
                                keyboardShouldPersistTaps="handled"
                              >
                                {yearDropdownOptions.map((option, index) => (
                                  <TouchableOpacity
                                    key={index}
                                    style={styles.optionItem}
                                    onPress={() => handleYearSelect(option)}
                                    activeOpacity={0.7}
                                  >
                                    <Text style={styles.optionText}>{option.displayText}</Text>
                                  </TouchableOpacity>
                                ))}
                              </ScrollView>
                            </View>
                          )}
                        </View>
                      </View>
                    )}

                    {/* Cumulative GPA Display - Platform-specific logic */}
                    {cumulativeGPA !== null && cumulativeGPA !== undefined ? (
                      <View style={styles.cumulativeGPAContainer}>
                        <TouchableOpacity
                          style={styles.gpaRow}
                          onPress={() => toggleGradeVisibility('cumulative-gpa')}
                          activeOpacity={0.7}
                        >
                          {!isGradeRevealed('cumulative-gpa') ? (
                            <View style={styles.blurredGPAContainer}>
                              <Text style={styles.blurredText}>
                                {cumulativeGPA} {getLetterGrade(cumulativeGPA) && `(${getLetterGrade(cumulativeGPA)})`}
                              </Text>
                            </View>
                          ) : (
                            <>
                              <Text style={styles.cumulativeGPAValue}>
                                {cumulativeGPA}
                              </Text>
                              {getLetterGrade(cumulativeGPA) && (
                                <Text style={styles.cumulativeGPALetter}>
                                  ({getLetterGrade(cumulativeGPA)})
                                </Text>
                              )}
                            </>
                          )}
                        </TouchableOpacity>
                        <Text style={styles.cumulativeGPALabel}>Cumulative GPA</Text>
                      </View>
                    ) : (
                      // Platform-specific ghost loading logic
                      Platform.OS === 'ios' && isGPALoading ? (
                        // iOS: Show ghost loading while GPA is loading
                        <View style={styles.cumulativeGPAContainer}>
                          <View style={styles.ghostShimmer}>
                            <View style={styles.gpaRow}>
                              <View style={[styles.ghostLine, { width: 80, height: 36, borderRadius: 8 }]} />
                              <View style={[styles.ghostLine, { width: 60, height: 24, borderRadius: 6, marginLeft: 10 }]} />
                            </View>
                            <View style={[styles.ghostLine, { width: 120, height: 14, borderRadius: 4, marginTop: 5, alignSelf: 'center' }]} />
                          </View>
                        </View>
                      ) : Platform.OS === 'android' && isGPALoading && selectedYear ? (
                        // Android: Show ghost loading only if GPA is loading AND user has selected a year
                        <View style={styles.cumulativeGPAContainer}>
                          <View style={styles.ghostShimmer}>
                            <View style={styles.gpaRow}>
                              <View style={[styles.ghostLine, { width: 80, height: 36, borderRadius: 8 }]} />
                              <View style={[styles.ghostLine, { width: 60, height: 24, borderRadius: 6, marginLeft: 10 }]} />
                            </View>
                            <View style={[styles.ghostLine, { width: 120, height: 14, borderRadius: 4, marginTop: 5, alignSelf: 'center' }]} />
                          </View>
                        </View>
                      ) : null
                    )}





                    {/* Loading States */}
                    {Platform.OS === 'android' && isLoadingSelectedYear ? (
                      <AndroidYearGhostLoading yearText={selectedYear?.displayText} />
                    ) : (Platform.OS === 'ios' && (academicYears.length > 0 || transcriptData.length > 0 || hasCachedData)) ||
                         (Platform.OS === 'android' && selectedYearData) ? (
                      <ScrollView
                        style={styles.transcriptScrollView}
                        showsVerticalScrollIndicator={false}
                        nestedScrollEnabled={true}
                        keyboardShouldPersistTaps="handled"
                      >
                        {Platform.OS === 'android' && selectedYearData ? (
                          // Android: Show selected year data only
                          (() => {
                            const yearData = selectedYearData;

                            return (
                              <View key="selected-year" style={styles.yearContainer}>
                                <Text style={styles.yearTitle}>{selectedYear?.displayText} ({yearData.year})</Text>
                                <Text style={styles.yearSummary}>
                                  {yearData.summary?.totalCourses || 0} courses across {yearData.summary?.semestersWithCourses || 0} semesters
                                </Text>

                                {yearData.semesters && yearData.semesters.length > 0 ? (
                                  yearData.semesters
                                    .filter(semester => semester.courses && semester.courses.length > 0)
                                    .sort((a, b) => {
                                      // Sort semesters in reverse chronological order (newest first)
                                      const getSemesterPriority = (semesterTitle) => {
                                        const title = semesterTitle.toLowerCase();
                                        if (title.includes('summer')) return 5;
                                        if (title.includes('spring') && title.includes('makeup')) return 3.5;
                                        if (title.includes('spring')) return 4;
                                        if (title.includes('winter')) return 3;
                                        if (title.includes('fall')) return 2;
                                        return 1;
                                      };

                                      const priorityA = getSemesterPriority(a.semesterTitle);
                                      const priorityB = getSemesterPriority(b.semesterTitle);
                                      return priorityB - priorityA;
                                    })
                                    .map((semester, semesterIndex) => (
                                      <View key={semesterIndex} style={styles.semesterContainer}>
                                        <Text style={styles.semesterTitle}>{semester.semesterTitle}</Text>

                                        {semester.courses.map((course, courseIndex) => {
                                          const gradeId = `${yearData.year}-${semester.tableIndex}-${courseIndex}`;
                                          return (
                                            <View key={courseIndex} style={styles.courseContainer}>
                                              <View style={styles.courseHeader}>
                                                <Text style={styles.courseName}>{course.courseName}</Text>
                                                <TouchableOpacity
                                                  onPress={() => toggleGradeVisibility(gradeId)}
                                                  activeOpacity={0.7}
                                                  style={styles.gradeContainer}
                                                >
                                                  <View style={!isGradeRevealed(gradeId) ? styles.blurredGradeContainer : null}>
                                                    <Text style={[styles.courseGrade, !isGradeRevealed(gradeId) && styles.blurredText]}>
                                                      {course.grade}
                                                    </Text>
                                                  </View>
                                                  {!isGradeRevealed(gradeId) && (
                                                    <View style={styles.eyeIconOverlay}>
                                                      <SmallEyeIcon size={16} color="#FFFFFF" />
                                                    </View>
                                                  )}
                                                </TouchableOpacity>
                                              </View>
                                              <View style={styles.courseDetails}>
                                                <Text style={styles.courseInfoSemester}>Semester: {course.semester}</Text>
                                                <Text style={styles.courseInfoHours}>Hours: {course.hours}</Text>
                                                <TouchableOpacity
                                                  onPress={() => toggleGradeVisibility(gradeId)} // Same as grade toggle
                                                  activeOpacity={0.7}
                                                  style={styles.numericTouchable}
                                                >
                                                  {!isGradeRevealed(gradeId) ? ( // Same condition as grade
                                                    <View style={styles.blurredNumericContainer}>
                                                      <Text style={styles.blurredText}>
                                                        Numeric: {course.numeric}
                                                      </Text>
                                                    </View>
                                                  ) : (
                                                    <Text style={styles.courseInfoNumeric}>
                                                      Numeric: {course.numeric}
                                                    </Text>
                                                  )}
                                                </TouchableOpacity>
                                              </View>
                                            </View>
                                          );
                                        })}

                                        {semester.semesterGPA && (
                                          <View style={styles.gpaContainer}>
                                            <TouchableOpacity
                                              onPress={() => toggleGradeVisibility(`${yearData.year}-${semester.tableIndex}-semester-gpa`)}
                                              activeOpacity={0.7}
                                            >
                                              <View style={!isGradeRevealed(`${yearData.year}-${semester.tableIndex}-semester-gpa`) ? styles.blurredSemesterGPAContainer : null}>
                                                <Text style={[styles.gpaText, !isGradeRevealed(`${yearData.year}-${semester.tableIndex}-semester-gpa`) && styles.blurredText]}>
                                                  Semester GPA: {semester.semesterGPA}
                                                </Text>
                                              </View>
                                            </TouchableOpacity>
                                            <Text style={styles.gpaText}>
                                              Total Hours: {semester.totalHours}
                                            </Text>
                                          </View>
                                        )}
                                      </View>
                                    ))
                                ) : (
                                  <Text style={styles.noCoursesText}>No semesters found for this year</Text>
                                )}
                              </View>
                            );
                          })()
                        ) : Platform.OS === 'ios' ? (
                          // iOS: Show years with independent loading states
                          (() => {
                            // Get all years that should be displayed (from academicYears)
                            const yearsToDisplay = academicYears
                              .sort((a, b) => {
                                // Sort by year in descending order (newest first)
                                const yearA = parseInt(a.text?.split('-')[0]) || 0;
                                const yearB = parseInt(b.text?.split('-')[0]) || 0;
                                return yearB - yearA;
                              });

                            // If we have academic years, show them immediately (but only after evaluation check is complete)
                            if (yearsToDisplay.length === 0) {
                              // No years available yet, show loading message
                              return (
                                <View style={styles.loadingContainer}>
                                  <ActivityIndicator size="large" color="#EAB308" />
                                  <Text style={styles.loadingText}>
                                    Loading academic years...
                                  </Text>
                                </View>
                              );
                            }

                            // Don't show ghost loading until evaluation check is complete
                            if (!isEvaluationCheckComplete) {
                              return null; // Show nothing while evaluation check is in progress
                            }

                            return yearsToDisplay.map((year) => {
                              const yearText = year.text;
                              const loadingState = yearLoadingStates.get(yearText) || 'loading';
                              const yearData = yearDataMap.get(yearText);

                              // Show ghost loading if still loading or retrying
                              if (loadingState === 'loading' || loadingState === 'retrying') {
                                return (
                                  <YearGhostLoading
                                    key={`ghost-${yearText}`}
                                    yearText={yearText}
                                    displayText={yearText} // Don't show retry status to user
                                  />
                                );
                              }

                              // Show error state if failed
                              if (loadingState === 'failed') {
                                return (
                                  <View key={`failed-${yearText}`} style={styles.yearContainer}>
                                    <Text style={styles.yearTitle}>{yearText}</Text>
                                    <Text style={[styles.yearSummary, { color: '#ef4444' }]}>
                                      Failed to load data for this year
                                    </Text>
                                  </View>
                                );
                              }

                              // Show actual data if completed and data exists
                              if (loadingState === 'completed' && yearData) {
                                // Only show if has courses
                                const totalCourses = yearData.summary?.totalCourses || 0;
                                const hasMatchingCourses = yearData.semesters?.some(semester => semester.courses?.length > 0);

                                if (totalCourses === 0 || !hasMatchingCourses) {
                                  return null; // Don't show this year
                                }

                                return (
                                  <View key={`completed-${yearText}`} style={styles.yearContainer}>
                                    <Text style={styles.yearTitle}>{yearText}</Text>
                                    <Text style={styles.yearSummary}>
                                      {yearData.summary?.totalCourses || 0} courses across {yearData.summary?.semestersWithCourses || 0} semesters
                                    </Text>

                                    {yearData.semesters && yearData.semesters.length > 0 ? (
                                      yearData.semesters
                                        .filter(semester => semester.courses && semester.courses.length > 0)
                                        .sort((a, b) => {
                                          // Sort semesters in reverse chronological order (newest first)
                                          const getSemesterPriority = (semesterTitle) => {
                                            const title = semesterTitle.toLowerCase();
                                            if (title.includes('summer')) return 5;
                                            if (title.includes('spring') && title.includes('makeup')) return 3.5;
                                            if (title.includes('spring')) return 4;
                                            if (title.includes('winter')) return 3;
                                            if (title.includes('fall')) return 2;
                                            return 1;
                                          };

                                          const priorityA = getSemesterPriority(a.semesterTitle);
                                          const priorityB = getSemesterPriority(b.semesterTitle);
                                          return priorityB - priorityA;
                                        })
                                        .map((semester, semesterIndex) => (
                                          <View key={semesterIndex} style={styles.semesterContainer}>
                                            <Text style={styles.semesterTitle}>{semester.semesterTitle}</Text>

                                            {semester.courses.map((course, courseIndex) => {
                                              const gradeId = `${yearText}-${semester.tableIndex}-${courseIndex}`;
                                              return (
                                                <View key={courseIndex} style={styles.courseContainer}>
                                                  <View style={styles.courseHeader}>
                                                    <Text style={styles.courseName}>{course.courseName}</Text>
                                                    <TouchableOpacity
                                                      onPress={() => toggleGradeVisibility(gradeId)}
                                                      activeOpacity={0.7}
                                                      style={styles.gradeContainer}
                                                    >
                                                      <View style={!isGradeRevealed(gradeId) ? styles.blurredGradeContainer : null}>
                                                        <Text style={[styles.courseGrade, !isGradeRevealed(gradeId) && styles.blurredText]}>
                                                          {course.grade}
                                                        </Text>
                                                      </View>
                                                      {!isGradeRevealed(gradeId) && (
                                                        <View style={styles.eyeIconOverlay}>
                                                          <SmallEyeIcon size={16} color="#FFFFFF" />
                                                        </View>
                                                      )}
                                                    </TouchableOpacity>
                                                  </View>
                                                  <View style={styles.courseDetails}>
                                                    <Text style={styles.courseInfoSemester}>Semester: {course.semester}</Text>
                                                    <Text style={styles.courseInfoHours}>Hours: {course.hours}</Text>
                                                    <TouchableOpacity
                                                      onPress={() => toggleGradeVisibility(gradeId)}
                                                      activeOpacity={0.7}
                                                      style={styles.numericTouchable}
                                                    >
                                                      {!isGradeRevealed(gradeId) ? (
                                                        <View style={styles.blurredNumericContainer}>
                                                          <Text style={styles.blurredText}>
                                                            Numeric: {course.numeric}
                                                          </Text>
                                                        </View>
                                                      ) : (
                                                        <Text style={styles.courseInfoNumeric}>
                                                          Numeric: {course.numeric}
                                                        </Text>
                                                      )}
                                                    </TouchableOpacity>
                                                  </View>
                                                </View>
                                              );
                                            })}

                                            {semester.semesterGPA && (
                                              <View style={styles.gpaContainer}>
                                                <TouchableOpacity
                                                  onPress={() => toggleGradeVisibility(`${yearText}-${semester.tableIndex}-semester-gpa`)}
                                                  activeOpacity={0.7}
                                                >
                                                  <View style={!isGradeRevealed(`${yearText}-${semester.tableIndex}-semester-gpa`) ? styles.blurredSemesterGPAContainer : null}>
                                                    <Text style={[styles.gpaText, !isGradeRevealed(`${yearText}-${semester.tableIndex}-semester-gpa`) && styles.blurredText]}>
                                                      Semester GPA: {semester.semesterGPA}
                                                    </Text>
                                                  </View>
                                                </TouchableOpacity>
                                                <Text style={styles.gpaText}>
                                                  Total Hours: {semester.totalHours}
                                                </Text>
                                              </View>
                                            )}
                                          </View>
                                        ))
                                    ) : (
                                      <Text style={styles.noCoursesText}>No semesters found for this year</Text>
                                    )}
                                  </View>
                                );
                              }

                              // Return null if no data to show
                              return null;
                            }).filter(Boolean); // Remove null entries
                          })()
                        ) : null}
                      </ScrollView>
                    ) : Platform.OS === 'ios' && academicYears.length === 0 && !hasCachedData ? (
                      <View style={styles.loadingContainer}>
                        <ActivityIndicator size="large" color="#EAB308" />
                        <Text style={styles.loadingText}>Loading academic years...</Text>
                      </View>
                    ) : Platform.OS === 'ios' && academicYears.length === 0 ? (
                      <Text style={styles.noDataText}>No transcript data available</Text>
                    ) : null}
                  </View>
                )}
              </>
            )}
            </View>

            {/* Bottom Half - WebView Debug Component - TEMPORARILY HIDDEN */}
            {/*
            <View style={styles.bottomHalfContainer}>
              <Text style={styles.debugTitle}>Background WebViews</Text>
              <View style={styles.webViewGrid}>
                {[0, 1, 2, 3].map((slotIndex) => {
                  const webViewConfig = transcriptWebViews[slotIndex];
                  return (
                    <View key={slotIndex} style={styles.webViewSlot}>
                      {webViewConfig ? (
                        <View style={styles.webViewDebugContainer}>
                          <Text style={styles.webViewTitle} numberOfLines={1}>
                            {webViewConfig.year?.text || `WebView ${slotIndex + 1}`}
                          </Text>
                          <View style={styles.webViewPreview}>
                            <WebView
                              ref={(ref) => {
                                if (Platform.OS === 'android') {
                                  setTimeout(() => {
                                    if (ref && transcriptWebViewRefs.current) {
                                      transcriptWebViewRefs.current[slotIndex] = ref;
                                      console.log(`🤖 Android: Deferred ref set for index ${slotIndex}:`, !!ref);
                                    }
                                  }, 100);
                                } else {
                                  if (transcriptWebViewRefs.current && ref) {
                                    transcriptWebViewRefs.current[slotIndex] = ref;
                                  }
                                }
                              }}
                              source={{ uri: webViewConfig.url }}
                              style={styles.debugWebView}
                              javaScriptEnabled={true}
                              domStorageEnabled={false}
                              thirdPartyCookiesEnabled={false}
                              sharedCookiesEnabled={false}
                              mixedContentMode="compatibility"
                              cacheEnabled={false}
                              incognito={true}
                              clearCache={true}
                              clearCookies={true}
                              userAgent={`MyGUCApp-${slotIndex}-${Date.now()}-${webViewConfig.year.value}`}
                              applicationNameForUserAgent={`MyGUCTranscript-${slotIndex}-${webViewConfig.year.value}`}
                              allowsInlineMediaPlayback={false}
                              mediaPlaybackRequiresUserAction={true}
                              allowsBackForwardNavigationGestures={false}
                              bounces={true}
                              scrollEnabled={true}
                              onLoad={() => handleTranscriptWebViewLoad(webViewConfig.year, webViewConfig.index)}
                              onMessage={(event) => handleTranscriptWebViewMessage(event, webViewConfig.year.text)}
                              onNavigationStateChange={(navState) => {
                                console.log(`📍 Debug WebView ${slotIndex} (${webViewConfig.year.text}) navigated to:`, navState.url);
                                if (navState.url.includes('Transcript_001.aspx') &&
                                    navState.loading === false &&
                                    formSubmittedFlags.current[slotIndex]) {
                                  console.log(`🔄 Page reloaded for ${webViewConfig.year.text}, waiting before extraction...`);
                                  const currentState = yearLoadingStates.get(webViewConfig.year.text);
                                  if (currentState === 'loading') {
                                    setTimeout(() => {
                                      console.log(`📊 Starting data extraction for ${webViewConfig.year.text}`);
                                      extractTranscriptDataForYear(webViewConfig.year, webViewConfig.index, transcriptWebViewRefs.current[slotIndex]);
                                    }, 3000);
                                  } else {
                                    console.log(`⚠️ Skipping extraction for ${webViewConfig.year.text} - state is ${currentState}`);
                                  }
                                }
                              }}
                            />
                          </View>
                          <View style={styles.webViewStatus}>
                            <Text style={styles.statusText} numberOfLines={1}>
                              {webViewConfig.isBackgroundRefresh ? 'Background' : 'Loading'}
                            </Text>
                          </View>
                        </View>
                      ) : (
                        <View style={styles.emptyWebViewSlot}>
                          <Text style={styles.emptySlotText}>Empty</Text>
                        </View>
                      )}
                    </View>
                  );
                })}
              </View>
            </View>
            */}
          </View>
        ) : (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>No credentials found. Please log in first.</Text>
          </View>
        )}
      </View>



      {/* Hidden WebViews for data extraction */}
      <View style={styles.hiddenWebViewContainer}>
        {transcriptWebViews.map((webViewConfig, index) => (
          <View key={webViewConfig.id} style={styles.hiddenWebViewWrapper}>
            <WebView
              ref={(ref) => {
                // Android production build workaround
                if (Platform.OS === 'android') {
                  // Use setTimeout to defer ref assignment on Android
                  setTimeout(() => {
                    if (ref && transcriptWebViewRefs.current) {
                      transcriptWebViewRefs.current[index] = ref;
                      console.log(`🤖 Android: Deferred ref set for index ${index}:`, !!ref);
                    }
                  }, 100);
                } else {
                  // iOS: Direct assignment
                  if (transcriptWebViewRefs.current && ref) {
                    transcriptWebViewRefs.current[index] = ref;
                  }
                }
              }}
              source={{ uri: webViewConfig.url }}
              style={{ flex: 1 }}
              javaScriptEnabled={true}
              domStorageEnabled={false}
              thirdPartyCookiesEnabled={false}
              sharedCookiesEnabled={false}
              mixedContentMode="compatibility"
              cacheEnabled={false}
              incognito={true}
              clearCache={true}
              clearCookies={true}
              userAgent={`MyGUCApp-${index}-${Date.now()}-${webViewConfig.year.value}`}
              // iOS-specific session isolation
              applicationNameForUserAgent={`MyGUCTranscript-${index}-${webViewConfig.year.value}`}

              allowsInlineMediaPlayback={false}
              mediaPlaybackRequiresUserAction={true}
              // Additional isolation properties
              allowsBackForwardNavigationGestures={false}
              bounces={Platform.OS === 'ios' ? true : false} // Enable bouncing on iOS for debugging
              scrollEnabled={Platform.OS === 'ios' ? true : false} // Enable scrolling on iOS for debugging
              onLoad={() => handleTranscriptWebViewLoad(webViewConfig.year, webViewConfig.index)}
              onMessage={(event) => handleTranscriptWebViewMessage(event, webViewConfig.year.text)}
              onNavigationStateChange={(navState) => {
                console.log(`📍 WebView ${index} (${webViewConfig.year.text}) navigated to:`, navState.url);
                // Check if this is a page reload after form submission AND form was already submitted
                if (navState.url.includes('Transcript_001.aspx') &&
                    navState.loading === false &&
                    formSubmittedFlags.current[index]) {
                  console.log(`🔄 Page reloaded for ${webViewConfig.year.text}, waiting before extraction...`);

                  // Check if this year is still in loading state before proceeding
                  const currentState = yearLoadingStates.get(webViewConfig.year.text);
                  if (currentState === 'loading') {
                    setTimeout(() => {
                      console.log(`📊 Starting data extraction for ${webViewConfig.year.text}`);
                      extractTranscriptDataForYear(webViewConfig.year, webViewConfig.index, transcriptWebViewRefs.current[index]);
                    }, 3000); // Increased wait time from 2s to 3s for better reliability
                  } else {
                    console.log(`⚠️ Skipping extraction for ${webViewConfig.year.text} - state is ${currentState}`);
                    // If state is 'retrying', it might transition to 'loading' soon, so check again after a short delay
                    if (currentState === 'retrying') {
                      console.log(`🔄 Year ${webViewConfig.year.text} is retrying, checking again in 2 seconds...`);
                      setTimeout(() => {
                        const newState = yearLoadingStates.get(webViewConfig.year.text);
                        if (newState === 'loading') {
                          console.log(`✅ State changed to loading for ${webViewConfig.year.text}, starting extraction`);
                          extractTranscriptDataForYear(webViewConfig.year, webViewConfig.index, transcriptWebViewRefs.current[index]);
                        } else {
                          console.log(`❌ State still ${newState} for ${webViewConfig.year.text}, giving up on this attempt`);
                        }
                      }, 2000);
                    }
                  }
                }
              }}
            />
          </View>
        ))}
      </View>

      {/* Sidebar Component */}
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={closeSidebar}
        sidebarAnim={sidebarAnim}
        navigation={navigation}
        currentScreen="Transcript"
      />
    </View>
    </View>
  );
};

// Create styles function that uses theme
const createStyles = (theme, safeCurrentThemeName) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  titleContainer: {
    position: 'absolute',
    top: 58, // Moved higher as requested
    left: 80, // Move title to the right to give space from sidebar button
    right: 80, // Reduced right margin to center the title better
    alignItems: 'center',
    justifyContent: 'center', // Added to ensure proper centering
    zIndex: 999,
  },
  title: {
    fontSize: Dimensions.get('window').height < 700 ? 22 : Dimensions.get('window').height < 800 ? 24 : 26, // Responsive font size - smaller for smaller screens
    fontWeight: 'bold',
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    textAlign: 'center',
  },
  sidebarButtonContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 1000,
  },
  sidebarButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: safeCurrentThemeName === 'colorful' ? 2 : safeCurrentThemeName === 'navy' ? 2 : 0, // Yellow border in pink mode, red border in navy mode
    borderColor: safeCurrentThemeName === 'colorful' ? '#EAB308' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  refreshButtonContainer: {
    position: 'absolute',
    top: 50,
    right: 15, // Moved slightly to the right
    zIndex: 1000,
  },
  refreshButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: safeCurrentThemeName === 'colorful' ? 2 : safeCurrentThemeName === 'navy' ? 2 : 0, // Yellow border in pink mode, red border in navy mode
    borderColor: safeCurrentThemeName === 'colorful' ? '#EAB308' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  contentContainer: {
    flex: 1,
    paddingTop: 100, // Reduced from 120 to raise content higher
    paddingHorizontal: 5, // Reduced padding for wider content
    paddingBottom: 0, // Removed bottom padding to stretch to bottom
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: theme.colors.primary,
    fontSize: 16,
    marginTop: 10,
  },
  checkingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkingText: {
    color: theme.colors.primary,
    fontSize: 16,
    marginTop: 15,
    textAlign: 'center',
  },
  webViewContainer: {
    flex: 1,
  },
  hiddenWebView: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
    opacity: 0,
  },
  evaluationButtonContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  evaluationFoundText: {
    color: theme.colors.primary,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  courseListContainer: {
    width: '100%',
    marginBottom: 20,
  },
  courseItem: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 15,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: theme.colors.primary,
  },
  courseText: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    fontSize: 14,
    fontWeight: '500',
  },
  evaluationButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 10,
    paddingVertical: 15,
    paddingHorizontal: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  evaluationButtonText: {
    color: theme.colors.primaryText,
    fontSize: 16,
    fontWeight: 'bold',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    color: theme.colors.error,
    fontSize: 16,
    textAlign: 'center',
  },
  transcriptContainer: {
    flex: 1,
    paddingHorizontal: 5, // Reduced padding for wider content
    paddingTop: 30, // Increased from 10 to move content down
  },
  transcriptTitle: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
  },
  // Android dropdown styles
  androidDropdownContainer: {
    marginBottom: 20,
  },
  dropdownLabel: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  dropdownWrapper: {
    position: 'relative',
    zIndex: 10000,
    elevation: 10000,
  },
  dropdown: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 10,
    paddingVertical: 18,
    paddingHorizontal: 20,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    minHeight: 55,
  },
  dropdownContent: {
    flex: 1,
    justifyContent: 'center',
    minHeight: 20,
  },
  dropdownText: {
    fontSize: 16,
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    flex: 1,
    textAlignVertical: 'center',
  },
  dropdownPlaceholder: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    flex: 1,
    textAlignVertical: 'center',
  },
  dropdownArrow: {
    fontSize: 16,
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  dropdownList: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 10,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    borderTopWidth: 0,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    maxHeight: 200,
    zIndex: 10001,
    elevation: 10001,
  },
  optionsList: {
    maxHeight: 200,
  },
  optionItem: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  optionText: {
    fontSize: 16,
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
  },
  transcriptScrollView: {
    flex: 1,
  },
  yearContainer: {
    backgroundColor: theme.colors.surface,
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#475569' : theme.colors.border,
  },
  yearTitle: {
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#FFFFFF' : theme.colors.primary,
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  yearSummary: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 15,
    fontStyle: 'italic',
  },
  semesterContainer: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    borderWidth: safeCurrentThemeName === 'colorful' ? 2 : safeCurrentThemeName === 'navy' ? 1 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#00CED1' : safeCurrentThemeName === 'navy' ? '#475569' : theme.colors.borderLight,
  },
  semesterTitle: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    textAlign: 'center',
  },
  courseContainer: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    borderLeftWidth: 3,
    borderLeftColor: safeCurrentThemeName === 'colorful' ? '#EAB308' : safeCurrentThemeName === 'navy' ? '#475569' : theme.colors.primary, // Yellow border in pink mode, slate in navy mode
  },
  courseHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  courseName: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
    marginRight: 10,
  },
  courseGrade: {
    color: safeCurrentThemeName === 'colorful' ? '#EAB308' : safeCurrentThemeName === 'navy' ? '#FFFFFF' : theme.colors.primary, // Yellow in pink mode, white in navy mode
    fontSize: 20, // Increased from 16 to make grades bigger
    fontWeight: 'bold',
    backgroundColor: safeCurrentThemeName === 'colorful' ? 'transparent' : safeCurrentThemeName === 'navy' ? theme.colors.surfaceSecondary : (safeCurrentThemeName === 'light' ? theme.colors.surface : theme.colors.border), // Navy uses surface secondary, not red
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  courseDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 5,
  },
  courseInfo: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
    fontSize: 12,
    flex: 1,
  },
  // New specific styles for course details layout
  courseInfoSemester: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
    fontSize: 12,
    flex: 1.5, // Reduced from 2 to give less space to semester section
    textAlign: 'left', // Leftmost position
    flexWrap: 'nowrap', // Prevent text wrapping
  },
  courseInfoHours: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
    fontSize: 12,
    flex: 0.8, // Reduced from 1 to give less space to hours
    textAlign: 'center', // Middle position
  },
  courseInfoNumeric: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
    fontSize: 12,
    flex: 1,
    textAlign: 'right', // Rightmost position
  },
  gpaContainer: {
    backgroundColor: safeCurrentThemeName === 'colorful' ? 'transparent' : safeCurrentThemeName === 'navy' ? theme.colors.surfaceSecondary : (safeCurrentThemeName === 'light' ? theme.colors.surface : theme.colors.border), // Navy uses surface secondary, not red
    borderRadius: 8,
    padding: 12,
    marginTop: 10,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderWidth: safeCurrentThemeName === 'colorful' ? 2 : safeCurrentThemeName === 'navy' ? 1 : 0, // Subtle border for navy
    borderColor: safeCurrentThemeName === 'colorful' ? '#40E0D0' : safeCurrentThemeName === 'navy' ? '#475569' : 'transparent', // Slate border for navy
  },
  gpaText: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : safeCurrentThemeName === 'navy' ? '#E2E8F0' : theme.colors.primary, // White font in pink mode, light gray in navy mode
    fontSize: 14,
    fontWeight: 'bold',
    backgroundColor: 'transparent', // Transparent background
    borderWidth: 0, // No border
    borderColor: 'transparent', // No border color
    paddingHorizontal: 0, // No padding
    paddingVertical: 0, // No padding
    borderRadius: 0, // No border radius
  },
  noCoursesText: {
    color: theme.colors.textSecondary,
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  noDataText: {
    color: theme.colors.textSecondary,
    fontSize: 16,
    textAlign: 'center',
    marginTop: 50,
  },
  // Cumulative GPA styles
  cumulativeGPAContainer: {
    alignItems: 'center',
    marginBottom: 25,
    paddingVertical: 20,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 2,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#475569' : theme.colors.primary, // Subtle slate border for navy theme
  },
  gpaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  cumulativeGPAValue: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : safeCurrentThemeName === 'navy' ? '#FFFFFF' : theme.colors.primary, // White number in pink and navy modes
    fontSize: 36, // Reduced from 48 to make it smaller
    fontWeight: 'bold',
    textAlign: 'center',
  },
  cumulativeGPALetter: {
    color: safeCurrentThemeName === 'colorful' ? '#40E0D0' : safeCurrentThemeName === 'navy' ? '#CBD5E0' : theme.colors.textSecondary, // Turquoise grade in pink mode, elegant gray in navy
    fontSize: 24,
    fontWeight: '600',
    marginLeft: 10,
    alignSelf: 'flex-end',
    marginBottom: 8,
  },
  cumulativeGPALabel: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : safeCurrentThemeName === 'navy' ? '#E2E8F0' : theme.colors.textSecondary,
    fontSize: 14,
    textAlign: 'center',
    marginTop: 5,
    fontStyle: 'italic',
  },
  // Search styles
  searchContainer: {
    marginBottom: 20,
  },
  searchInput: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    fontSize: 16,
    borderWidth: safeCurrentThemeName === 'colorful' ? 2 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#EAB308' : theme.colors.borderLight, // Yellow border in pink mode
  },
  // Hidden WebView styles
  hiddenWebViewContainer: {
    position: 'absolute',
    top: -2000,
    left: -2000,
    width: 1,
    height: 1,
    opacity: 0,
  },
  hiddenWebViewWrapper: {
    width: 1,
    height: 1,
    opacity: 0,
  },
  // Eye button styles - Fixed positioning with better spacing
  eyeButtonContainer: {
    position: 'absolute',
    top: 50, // Same level as refresh button
    right: 70, // Moved slightly to the right
    zIndex: 1000,
  },
  eyeButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: safeCurrentThemeName === 'colorful' ? 2 : safeCurrentThemeName === 'navy' ? 2 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#EAB308' : safeCurrentThemeName === 'navy' ? '#DC2626' : theme.colors.border, // Yellow border in pink mode, red border in navy mode
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },

  // Blur effect - old style (kept for compatibility)
  blurredContent: {
    textShadowColor: 'transparent',
    color: 'transparent',
    backgroundColor: theme.colors.textSecondary,
    borderRadius: 4,
  },
  // New improved blur styles
  blurredText: {
    color: 'transparent',
    backgroundColor: 'transparent', // Ensure background is also transparent in all themes
  },
  blurredGPAContainer: {
    backgroundColor: safeCurrentThemeName === 'colorful' ? '#40E0D0' : safeCurrentThemeName === 'navy' ? '#475569' : theme.colors.textSecondary, // Turquoise in pink mode, elegant slate in navy mode
    borderRadius: 8,
    paddingHorizontal: 25, // Increased from 15 to make it bigger
    paddingVertical: 15, // Increased from 8 to make it bigger
    alignItems: 'center',
    justifyContent: 'center',
    minWidth: 120, // Ensure minimum width
    minHeight: 60, // Ensure minimum height
  },
  gradeContainer: {
    position: 'relative',
  },
  blurredGradeContainer: {
    backgroundColor: safeCurrentThemeName === 'colorful' ? '#40E0D0' : safeCurrentThemeName === 'navy' ? '#475569' : theme.colors.textSecondary, // Turquoise in pink mode, elegant slate in navy mode
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  eyeIconOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -8 }, { translateY: -8 }], // Center the icon
    alignItems: 'center',
    justifyContent: 'center',
  },
  blurredNumericContainer: {
    backgroundColor: safeCurrentThemeName === 'colorful' ? '#40E0D0' : theme.colors.textSecondary, // Turquoise in pink mode
    borderRadius: 3, // Reduced from 4 for smaller appearance
    paddingHorizontal: 5, // Further reduced from 6
    paddingVertical: 1, // Further reduced from 2 to make it much thinner vertically
    alignSelf: 'flex-end', // Align blurred container to the right
  },
  numericTouchable: {
    flex: 1,
    alignItems: 'flex-end', // Align numeric value to the right
  },
  blurredSemesterGPAContainer: {
    backgroundColor: safeCurrentThemeName === 'colorful' ? '#40E0D0' : safeCurrentThemeName === 'navy' ? '#475569' : theme.colors.textSecondary, // Turquoise in pink mode, elegant slate in navy mode
    borderRadius: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
    alignSelf: 'flex-start',
  },
  // Update indicator styles
  updateIndicator: {
    position: 'absolute',
    top: 110,
    left: 20,
    right: 20,
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 999,
    borderWidth: 1,
    borderColor: theme.colors.border,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  updateText: {
    color: theme.colors.primary,
    fontSize: 14,
    marginLeft: 8,
    fontWeight: '500',
  },
  // Ghost loading styles
  ghostShimmer: {
    backgroundColor: 'transparent',
  },
  ghostLine: {
    backgroundColor: theme.colors.border,
    borderRadius: 4,
    opacity: 0.6,
  },

  // Split container styles for top/bottom halves
  splitContainer: {
    flex: 1,
    flexDirection: 'column',
  },
  topHalfContainer: {
    flex: 0.6, // 60% for top content
    minHeight: '40%',
  },
  // Full height container when debug component is hidden
  fullHeightContainer: {
    flex: 1,
  },
  bottomHalfContainer: {
    flex: 2,
    backgroundColor: theme.colors.surfaceSecondary,
    borderTopWidth: 2,
    borderTopColor: theme.colors.primary,
    padding: 8,
  },
  debugTitle: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  webViewGrid: {
    flex: 1,
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    alignItems: 'stretch',
  },
  webViewSlot: {
    width: '49%',
    height: '47%',
    marginBottom: '3%',
    borderRadius: 10,
    borderWidth: 2,
    borderColor: safeCurrentThemeName === 'colorful' ? '#EAB308' : theme.colors.primary,
    overflow: 'hidden',
    backgroundColor: theme.colors.surface,
  },
  webViewDebugContainer: {
    flex: 1,
    backgroundColor: theme.colors.surface,
  },
  webViewTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    textAlign: 'center',
    padding: 6,
    backgroundColor: theme.colors.primary,
    color: theme.colors.primaryText,
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  webViewPreview: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  debugWebView: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  webViewStatus: {
    padding: 6,
    backgroundColor: theme.colors.surfaceSecondary,
    alignItems: 'center',
    borderBottomLeftRadius: 8,
    borderBottomRightRadius: 8,
  },
  statusText: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
    fontSize: 12,
    fontWeight: '600',
  },
  emptyWebViewSlot: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    opacity: 0.7,
    borderStyle: 'dashed',
    borderWidth: 2,
    borderColor: theme.colors.border,
  },
  emptySlotText: {
    color: theme.colors.textSecondary,
    fontSize: 14,
    fontWeight: '500',
    fontStyle: 'italic',
  },
  webViewInfoText: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
    fontSize: 10,
    marginBottom: 4,
    textAlign: 'center',
  },
  debugWebViewWrapper: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 8,
  },

  });

export default TranscriptScreen;
