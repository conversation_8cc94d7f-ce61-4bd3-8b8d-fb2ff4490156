import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Animated,
  TextInput,
  PanResponder,
  Dimensions,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import { useFocusEffect } from '@react-navigation/native';
import Sidebar from '../../components/Sidebar';
import HamburgerIcon from '../../components/HamburgerIcon';
import RefreshIcon from '../../components/RefreshIcon';
import { clearWebViewSession, disposeWebView } from '../../utils/WebViewUtils';
import { useTheme } from '../../contexts/ThemeContext';
import ThemeTransitionWrapper from '../../components/ThemeTransitionWrapper';
import Svg, { Circle, Path, Line } from 'react-native-svg';

// Profile Icon Component
const ProfileIcon = ({ size = 24, color = '#EAB308' }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Circle cx="12" cy="7" r="4" stroke={color} strokeWidth="2" fill="none" />
    <Path d="M4 20c0-4.418 3.582-8 8-8s8 3.582 8 8" stroke={color} strokeWidth="2" fill="none" />
  </Svg>
);

// Book Icon Component
const BookIcon = ({ size = 24, color = '#EAB308' }) => (
  <Svg width={size} height={size} viewBox="0 0 64 64" fill="none">
    {/* Left page */}
    <Path d="M32 12 L12 16 C10.9 16 10 16.9 10 18 V48 C10 49.1 10.9 50 12 50 L32 44" stroke={color} strokeWidth="2" />

    {/* Right page */}
    <Path d="M32 12 L52 16 C53.1 16 54 16.9 54 18 V48 C54 49.1 53.1 50 52 50 L32 44" stroke={color} strokeWidth="2" />

    {/* Spine */}
    <Line x1="32" y1="12" x2="32" y2="44" stroke={color} strokeWidth="1.5" />

    {/* Left page lines: slanted like right edge */}
    <Line x1="14" y1="24" x2="30" y2="20" stroke={color} strokeWidth="1" />
    <Line x1="14" y1="30" x2="30" y2="26" stroke={color} strokeWidth="1" />
    <Line x1="14" y1="36" x2="30" y2="32" stroke={color} strokeWidth="1" />
    <Line x1="14" y1="42" x2="30" y2="38" stroke={color} strokeWidth="1" />

    {/* Right page lines: slanted like left edge */}
    <Line x1="34" y1="20" x2="50" y2="24" stroke={color} strokeWidth="1" />
    <Line x1="34" y1="26" x2="50" y2="30" stroke={color} strokeWidth="1" />
    <Line x1="34" y1="32" x2="50" y2="36" stroke={color} strokeWidth="1" />
    <Line x1="34" y1="38" x2="50" y2="42" stroke={color} strokeWidth="1" />
  </Svg>
);

const EvaluateScreen = ({ navigation }) => {
  // Theme context
  const { theme, currentThemeName } = useTheme();
  const safeCurrentThemeName = currentThemeName || 'dark';

  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const [sidebarAnim] = useState(new Animated.Value(-300)); // Start off-screen
  const [refreshRotation] = useState(new Animated.Value(0)); // For rotating refresh arrow
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [credentials, setCredentials] = useState(null);
  const [evaluateUrl, setEvaluateUrl] = useState('');
  const [dropdownOptions, setDropdownOptions] = useState([]);
  const [selectedCourse, setSelectedCourse] = useState({ main: 'Select Course', code: '' });
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [isDropdownLoading, setIsDropdownLoading] = useState(true);
  const [evaluationMode, setEvaluationMode] = useState('course'); // 'course' or 'staff'
  const [selectedStaff, setSelectedStaff] = useState({ main: 'Select Staff', code: '' });
  const [staffDropdownOptions, setStaffDropdownOptions] = useState([]);
  const [isStaffDropdownVisible, setIsStaffDropdownVisible] = useState(false);
  const [isStaffDropdownLoading, setIsStaffDropdownLoading] = useState(true);
  const [evaluationStatus, setEvaluationStatus] = useState(''); // For showing evaluation status
  const [isCheckingEvaluation, setIsCheckingEvaluation] = useState(false);
  const [evaluationResponses, setEvaluationResponses] = useState({});
  const [additionalResponses, setAdditionalResponses] = useState({});
  const [critiqueText, setCritiqueText] = useState('');
  const [bulkRating, setBulkRating] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionStatus, setSubmissionStatus] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState('');
  const [currentCourseCode, setCurrentCourseCode] = useState('');
  const [isAdvanceCourse, setIsAdvanceCourse] = useState(false);
  const [showEditButton, setShowEditButton] = useState(false);
  const [submissionVerified, setSubmissionVerified] = useState(false);
  const [isBulkDropdownVisible, setIsBulkDropdownVisible] = useState(false);
  const [gradesUrl, setGradesUrl] = useState('');
  const [gradesCourses, setGradesCourses] = useState([]);
  const [isGradesLoading, setIsGradesLoading] = useState(true);
  const [advanceCourses, setAdvanceCourses] = useState([]);
  const webViewRef = useRef(null);
  const gradesWebViewRef = useRef(null);
  const scrollViewRef = useRef(null);
  const errorMessageRef = useRef(null);

  // Refs for tracking background operations and component mount state
  const isMountedRef = useRef(true);
  const activeTimeoutsRef = useRef(new Set());

  // Comprehensive function to kill all background operations
  const killAllBackgroundOperations = async () => {
    console.log('🛑 EvaluateScreen: Killing all background operations...');

    // Mark component as unmounted to prevent state updates
    isMountedRef.current = false;

    // Clear all active timeouts
    activeTimeoutsRef.current.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    activeTimeoutsRef.current.clear();

    // Stop all animations
    refreshRotation.stopAnimation();
    sidebarAnim.stopAnimation();

    // Reset all loading states to prevent cache corruption
    setIsLoading(false);
    setIsLoadingCourses(false);
    setIsGradesLoading(false);

    // Dispose of WebViews
    await disposeWebView(webViewRef, 'evaluate-webview');
    await disposeWebView(gradesWebViewRef, 'grades-webview');

    console.log('✅ EvaluateScreen: All background operations killed');
  };

  // Safe state setter that checks if component is still mounted
  const safeSetState = (setter, value, stateName) => {
    if (isMountedRef.current) {
      setter(value);
    } else {
      console.log(`⚠️ EvaluateScreen: Prevented ${stateName} state update after unmount`);
    }
  };

  // Safe timeout wrapper that tracks timeouts for cleanup
  const safeSetTimeout = (callback, delay) => {
    const timeoutId = setTimeout(() => {
      activeTimeoutsRef.current.delete(timeoutId);
      if (isMountedRef.current) {
        callback();
      }
    }, delay);
    activeTimeoutsRef.current.add(timeoutId);
    return timeoutId;
  };

  // Generate styles based on current theme
  const styles = createStyles(theme, safeCurrentThemeName);

  // Evaluation questions with 6-point scale
  const evaluationQuestions = [
    "The timetable works efficiently as far as my activities are concerned",
    "Any changes in the course or teaching have been communicated effectively",
    "The course is well organized and is running smoothly",
    "The required amount of work for this course is adequate",
    "I have received sufficient advice and support with my studies",
    "I have been able to contact staff when I needed to",
    "Good advice was available when I needed to make study choices",
    "The library resources and services are good enough for my needs",
    "I have been able to access general IT resources when I needed to",
    "I have been able to access general IT resources when I needed to",
    "The course has helped me present myself with confidence",
    "My communication skills have improved",
    "As a result of the course, I feel confident in tackling unfamiliar problems",
    "The criteria used in marking have been clear in advance",
    "Assessment arrangements and marking have been fair",
    "Feedback on my work has been prompt",
    "I have received detailed comments on my work",
    "Feedback on my work has helped me clarify things I did not understand",
    "Overall, I am satisfied with the quality of the course"
  ];

  // Additional questions with different rating scales
  const additionalQuestions = [
    {
      question: "I attended the Lecture/Course (from 1 being always to 6 being almost never)",
      options: ["1", "2", "3", "4", "5", "6"],
      type: "attendance"
    },
    {
      question: "My weekly time expenditure for preparation/wrap-up of the Lecture/Course was (in hours)",
      options: ["1", "2", "3", "4", "5", "6"],
      type: "time"
    },
    {
      question: "How great is the amount of work for this course to you (from 1 being very great to 6 being very low)",
      options: ["1", "2", "3", "4", "5", "6"],
      type: "workload"
    }
  ];

  // Rating options - switched positions of Strongly Disagree and Slightly Disagree for display
  const ratingOptions = [
    "Strongly Agree",
    "Agree",
    "Slightly Agree",
    "Strongly Disagree", // Switched position for display
    "Disagree",
    "Slightly Disagree"  // Switched position for display
  ];

  // Bulk rating options with numbers - switched positions to match display order
  const bulkRatingOptions = [
    { label: "1 / Strongly Agree", value: "Strongly Agree" },
    { label: "2 / Agree", value: "Agree" },
    { label: "3 / Slightly Agree", value: "Slightly Agree" },
    { label: "4 / Strongly Disagree", value: "Strongly Disagree" }, // Switched position for display
    { label: "5 / Disagree", value: "Disagree" },
    { label: "6 / Slightly Disagree", value: "Slightly Disagree" }  // Switched position for display
  ];

  // Handle rating selection
  const handleRatingSelect = (questionIndex, rating) => {
    setEvaluationResponses(prev => ({
      ...prev,
      [questionIndex]: rating
    }));
    console.log(`Question ${questionIndex + 1}: ${rating}`);
  };

  // Handle additional question rating selection
  const handleAdditionalRatingSelect = (questionIndex, rating) => {
    setAdditionalResponses(prev => ({
      ...prev,
      [questionIndex]: rating
    }));
    console.log(`Additional Question ${questionIndex + 1}: ${rating}`);
  };

  // Handle bulk rating selection
  const handleBulkRatingSelect = (option) => {
    setBulkRating(option.value);
    setIsBulkDropdownVisible(false);
    console.log('📋 Bulk rating selected:', option.label);
  };

  // Apply bulk rating to all main questions
  const applyBulkRating = () => {
    if (!bulkRating) {
      console.log('⚠️ No bulk rating selected');
      return;
    }

    console.log('🔄 Applying bulk rating to all questions:', bulkRating);

    // Apply to all 19 main evaluation questions
    const newResponses = {};
    for (let i = 0; i < evaluationQuestions.length; i++) {
      newResponses[i] = bulkRating;
    }
    setEvaluationResponses(newResponses);

    // Map rating to numeric value for additional questions
    // Note: Values remain the same for correct submission regardless of display order
    const ratingToNumber = {
      'Strongly Agree': '1',
      'Agree': '2',
      'Slightly Agree': '3',
      'Slightly Disagree': '4',  // Correct value for submission
      'Disagree': '5',
      'Strongly Disagree': '6'   // Correct value for submission
    };

    const numericValue = ratingToNumber[bulkRating];
    if (numericValue) {
      // Apply to all 3 additional questions (20-22)
      const newAdditionalResponses = {};
      for (let i = 0; i < additionalQuestions.length; i++) {
        newAdditionalResponses[i] = numericValue;
      }
      setAdditionalResponses(newAdditionalResponses);
      console.log('✅ Applied numeric value', numericValue, 'to additional questions');
    }

    console.log('✅ Applied', bulkRating, 'to all', evaluationQuestions.length, 'main questions');
    console.log('✅ Applied', numericValue, 'to all', additionalQuestions.length, 'additional questions');
  };

  // Function to transfer all responses to WebView
  const transferResponsesToWebView = () => {
    if (!webViewRef.current) {
      console.log('❌ WebView not available');
      return;
    }

    console.log('🔄 Transferring responses to WebView...');
    console.log('📋 Evaluation responses:', evaluationResponses);
    console.log('📋 Additional responses:', additionalResponses);
    console.log('📋 Critique text:', critiqueText);

    // Create mapping for 6-point scale responses to radio button values
    // Note: Values remain the same for correct submission regardless of display order
    const ratingToValue = {
      'Strongly Agree': '1',
      'Agree': '2',
      'Slightly Agree': '3',
      'Slightly Disagree': '4',  // Correct value for submission
      'Disagree': '5',
      'Strongly Disagree': '6'   // Correct value for submission
    };

    const transferJS = `
      (function() {
        try {
          console.log('🔄 Starting response transfer to evaluation form...');

          // Transfer main evaluation questions (19 questions)
          ${Object.entries(evaluationResponses).map(([questionIndex, rating]) => {
            const value = ratingToValue[rating];
            if (!value) return '';

            const questionNumber = parseInt(questionIndex) + 1;
            // Format control number: 01-09 for questions 1-9, 10+ for questions 10+
            const controlNumber = questionNumber < 10 ? `0${questionNumber}` : questionNumber.toString();

            return `
              // Question ${questionNumber}: ${rating} (value: ${value})
              const radio_${questionIndex} = document.querySelector('input[name="ctl00$ctl00$ContentPlaceHolderright$ContentPlaceHoldercontent$objRptr$ctl${controlNumber}$grade"][value="${value}"]');
              if (radio_${questionIndex}) {
                radio_${questionIndex}.checked = true;
                console.log('✅ Set question ${questionNumber} to ${rating}');
              } else {
                console.log('❌ Radio button not found for question ${questionNumber}');
              }
            `;
          }).join('')}

          // Transfer additional questions
          ${Object.entries(additionalResponses).map(([questionIndex, rating]) => {
            const radioListNumber = parseInt(questionIndex) + 1;
            return `
              // Additional Question ${parseInt(questionIndex) + 1}: ${rating}
              const additionalRadio_${questionIndex} = document.querySelector('input[name="ctl00$ctl00$ContentPlaceHolderright$ContentPlaceHoldercontent$RadioButtonList${radioListNumber}"][value="${rating}"]');
              if (additionalRadio_${questionIndex}) {
                additionalRadio_${questionIndex}.checked = true;
                console.log('✅ Set additional question ${parseInt(questionIndex) + 1} to ${rating}');
              } else {
                console.log('❌ Additional radio button not found for question ${parseInt(questionIndex) + 1}');
              }
            `;
          }).join('')}

          // Transfer critique text
          const critiqueTextArea = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_rmrk');
          if (critiqueTextArea) {
            critiqueTextArea.value = \`${critiqueText.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`;
            console.log('✅ Set critique text');
          } else {
            console.log('❌ Critique text area not found');
          }

          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'responses_transferred',
            message: 'All responses transferred successfully'
          }));

        } catch (error) {
          console.log('❌ Error transferring responses:', error);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'transfer_error',
            error: error.message
          }));
        }
      })();
      true;
    `;

    webViewRef.current.injectJavaScript(transferJS);
  };

  // Function to submit evaluation (transfer responses + submit form)
  const submitEvaluation = () => {
    if (!webViewRef.current) {
      console.log('❌ WebView not available');
      return;
    }

    // Validate that all questions are answered
    if (!validateAllQuestionsAnswered()) {
      console.log('❌ Not all questions are answered');
      setSubmissionStatus('Please answer all evaluation questions before submitting.');
      scrollToErrorMessage();
      setTimeout(() => setSubmissionStatus(''), 5000);
      return;
    }

    console.log('🚀 Starting evaluation submission...');
    setIsSubmitting(true);
    setSubmissionStatus('');
    setSubmissionVerified(false);

    // Create mapping for 6-point scale responses to radio button values
    // Note: Values remain the same for correct submission regardless of display order
    const ratingToValue = {
      'Strongly Agree': '1',
      'Agree': '2',
      'Slightly Agree': '3',
      'Slightly Disagree': '4',  // Correct value for submission
      'Disagree': '5',
      'Strongly Disagree': '6'   // Correct value for submission
    };

    const submitJS = `
      (function() {
        try {
          console.log('🔄 Starting response transfer and submission...');

          // Transfer main evaluation questions (19 questions)
          ${Object.entries(evaluationResponses).map(([questionIndex, rating]) => {
            const value = ratingToValue[rating];
            if (!value) return '';

            const questionNumber = parseInt(questionIndex) + 1;
            const controlNumber = questionNumber < 10 ? `0${questionNumber}` : questionNumber.toString();

            return `
              // Question ${questionNumber}: ${rating} (value: ${value})
              const radio_${questionIndex} = document.querySelector('input[name="ctl00$ctl00$ContentPlaceHolderright$ContentPlaceHoldercontent$objRptr$ctl${controlNumber}$grade"][value="${value}"]');
              if (radio_${questionIndex}) {
                radio_${questionIndex}.checked = true;
                console.log('✅ Set question ${questionNumber} to ${rating}');
              } else {
                console.log('❌ Radio button not found for question ${questionNumber}');
              }
            `;
          }).join('')}

          // Transfer additional questions
          ${Object.entries(additionalResponses).map(([questionIndex, rating]) => {
            const radioListNumber = parseInt(questionIndex) + 1;
            return `
              // Additional Question ${parseInt(questionIndex) + 1}: ${rating}
              const additionalRadio_${questionIndex} = document.querySelector('input[name="ctl00$ctl00$ContentPlaceHolderright$ContentPlaceHoldercontent$RadioButtonList${radioListNumber}"][value="${rating}"]');
              if (additionalRadio_${questionIndex}) {
                additionalRadio_${questionIndex}.checked = true;
                console.log('✅ Set additional question ${parseInt(questionIndex) + 1} to ${rating}');
              } else {
                console.log('❌ Additional radio button not found for question ${parseInt(questionIndex) + 1}');
              }
            `;
          }).join('')}

          // Transfer critique text
          const critiqueTextArea = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_rmrk');
          if (critiqueTextArea) {
            critiqueTextArea.value = \`${critiqueText.replace(/`/g, '\\`').replace(/\$/g, '\\$')}\`;
            console.log('✅ Set critique text');
          } else {
            console.log('❌ Critique text area not found');
          }

          // Submit the form
          console.log('🚀 Submitting evaluation form...');
          const submitButton = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_pstEvalBtn');
          if (submitButton) {
            submitButton.click();
            console.log('✅ Submit button clicked - page will reload');

            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'evaluation_form_submitted',
              message: 'Form submitted successfully'
            }));
          } else {
            console.log('❌ Submit button not found');
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'submission_error',
              error: 'Submit button not found'
            }));
          }

        } catch (error) {
          console.log('❌ Error during submission:', error);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'submission_error',
            error: error.message
          }));
        }
      })();
      true;
    `;

    webViewRef.current.injectJavaScript(submitJS);
  };

  // Cache management functions
  const getCacheKey = (courseCode) => `evaluation_cache_${courseCode}`;

  // Save evaluation answers to cache
  const saveEvaluationToCache = async (courseCode, courseName) => {
    try {
      // Validate that all questions are answered
      if (!validateAllQuestionsAnswered()) {
        console.log('❌ Not all questions are answered');
        setSaveStatus('Please answer all evaluation questions before saving.');
        scrollToErrorMessage();
        setTimeout(() => setSaveStatus(''), 5000);
        return;
      }

      setIsSaving(true);
      setSaveStatus('');

      const cacheData = {
        courseCode,
        courseName,
        evaluationResponses,
        additionalResponses,
        critiqueText,
        savedAt: new Date().toISOString()
      };

      const cacheKey = getCacheKey(courseCode);
      await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheData));

      console.log('💾 Saved evaluation to cache for course:', courseCode);
      setIsSaving(false);
      setSaveStatus(`Evaluation saved for ${courseName}!`);

      // Hide questionnaire and show saved status
      setEvaluationStatus('Course pre-evaluation has been saved');
      setShowEditButton(true);

      // Clear status after 3 seconds
      setTimeout(() => setSaveStatus(''), 3000);
    } catch (error) {
      console.log('❌ Error saving to cache:', error);
      setIsSaving(false);
      setSaveStatus('Error saving evaluation. Please try again.');
      scrollToErrorMessage();
      setTimeout(() => setSaveStatus(''), 3000);
    }
  };

  // Load evaluation answers from cache
  const loadEvaluationFromCache = async (courseCode) => {
    try {
      const cacheKey = getCacheKey(courseCode);
      const cachedData = await AsyncStorage.getItem(cacheKey);

      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        console.log('📂 Loaded cached evaluation for course:', courseCode);

        // Load the cached responses
        setEvaluationResponses(parsedData.evaluationResponses || {});
        setAdditionalResponses(parsedData.additionalResponses || {});
        setCritiqueText(parsedData.critiqueText || '');

        return true; // Cache found and loaded
      }

      return false; // No cache found
    } catch (error) {
      console.log('❌ Error loading from cache:', error);
      return false;
    }
  };

  // Check if course has cached evaluation
  const hasCachedEvaluation = async (courseCode) => {
    try {
      const cacheKey = getCacheKey(courseCode);
      const cachedData = await AsyncStorage.getItem(cacheKey);
      return cachedData !== null;
    } catch (error) {
      console.log('❌ Error checking cache:', error);
      return false;
    }
  };

  // Handle edit answers button
  const handleEditAnswers = () => {
    console.log('✏️ Editing cached answers for course:', currentCourseCode);
    setEvaluationStatus('Course evaluation available');
    setShowEditButton(false);
  };

  // Validate that all required questions are answered
  const validateAllQuestionsAnswered = () => {
    // Check main evaluation questions (19 questions)
    const mainQuestionsCount = evaluationQuestions.length;
    const answeredMainQuestions = Object.keys(evaluationResponses).length;

    // Check additional questions (3 questions)
    const additionalQuestionsCount = additionalQuestions.length;
    const answeredAdditionalQuestions = Object.keys(additionalResponses).length;

    console.log('📊 Validation check:');
    console.log('Main questions:', answeredMainQuestions, '/', mainQuestionsCount);
    console.log('Additional questions:', answeredAdditionalQuestions, '/', additionalQuestionsCount);

    const allMainAnswered = answeredMainQuestions === mainQuestionsCount;
    const allAdditionalAnswered = answeredAdditionalQuestions === additionalQuestionsCount;

    return allMainAnswered && allAdditionalAnswered;
  };

  // Function to scroll to error message
  const scrollToErrorMessage = () => {
    if (errorMessageRef.current && scrollViewRef.current) {
      setTimeout(() => {
        errorMessageRef.current.measureLayout(
          scrollViewRef.current,
          (_, y) => {
            scrollViewRef.current.scrollTo({
              y: y - 100, // Offset to show some content above the error
              animated: true
            });
          },
          () => {
            // Fallback: scroll to bottom if measurement fails
            scrollViewRef.current.scrollToEnd({ animated: true });
          }
        );
      }, 100); // Small delay to ensure the error message is rendered
    }
  };

  // Auto-scroll to error messages when they appear
  useEffect(() => {
    if ((submissionStatus && !submissionStatus.includes('successfully')) ||
        (saveStatus && !saveStatus.includes('saved'))) {
      scrollToErrorMessage();
    }
  }, [submissionStatus, saveStatus]);



  useEffect(() => {
    loadCredentialsAndSetupEvaluate();

    // Cleanup function
    return () => {
      console.log('🧹 EvaluateScreen: Component unmounting - cleaning up...');
      killAllBackgroundOperations();
    };
  }, []);

  // Handle navigation focus/blur - kill background operations when losing focus
  useFocusEffect(
    useCallback(() => {
      console.log('🔄 EvaluateScreen: Screen focused');

      // Mark component as mounted when focused
      isMountedRef.current = true;

      // Return cleanup function that runs when screen loses focus
      return () => {
        console.log('🔄 EvaluateScreen: Screen losing focus - killing background operations...');
        killAllBackgroundOperations();
      };
    }, [])
  );

  // Kill background operations when navigating away completely
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      console.log('🧹 EvaluateScreen: Screen unmounting - Killing all background operations...');
      await killAllBackgroundOperations();
    });

    return unsubscribe;
  }, [navigation]);

  const loadCredentialsAndSetupEvaluate = async () => {
    try {
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (storedUsername && storedPassword) {
        const creds = { username: storedUsername, password: storedPassword };
        setCredentials(creds);

        // Create URL with embedded credentials - properly encode username and password
        const encodedUsername = encodeURIComponent(storedUsername);
        const encodedPassword = encodeURIComponent(storedPassword);
        const url = `https://${encodedUsername}:${encodedPassword}@apps.guc.edu.eg/student_ext/Evaluation/EvaluateCourse.aspx`;
        setEvaluateUrl(url);

        // Create grades URL for fetching additional courses
        const gradesUrlWithCreds = `https://${encodedUsername}:${encodedPassword}@apps.guc.edu.eg/student_ext/Grade/CheckGrade_01.aspx`;
        setGradesUrl(gradesUrlWithCreds);

        console.log('✅ Evaluate credentials loaded and URLs set');
        setIsLoading(false);
      } else {
        console.log('❌ No stored credentials found');
        setIsLoading(false);
      }
    } catch (error) {
      console.log('❌ Error loading credentials:', error);
      setIsLoading(false);
    }
  };

  // Format course name: extract last 2 strings as code, rest as main name
  const formatCourseName = (courseName) => {
    if (!courseName) return { main: '', code: '' };

    // Split by spaces and get all words
    const words = courseName.trim().split(/\s+/);

    if (words.length >= 2) {
      // Get last 2 words as course code (without space)
      const courseCode = words.slice(-2).join('');
      // Get everything except last 2 words as course name
      const mainName = words.slice(0, -2).join(' ');

      return {
        main: mainName || courseName, // Fallback to full name if no main name
        code: courseCode
      };
    }

    return {
      main: courseName,
      code: ''
    };
  };

  // Format staff name: show full name on left, no code on right
  const formatStaffName = (staffName) => {
    if (!staffName) return { main: '', code: '' };

    // Return the full staff name as main, with empty code
    return {
      main: staffName.trim(),
      code: '' // No code for staff names
    };
  };

  // Handle dropdown option selection
  const handleCourseSelect = async (option) => {
    const formatted = formatCourseName(option.text);
    setSelectedCourse(formatted);
    setIsDropdownVisible(false);
    console.log('⭐ Course selected for evaluation:', formatted.main, formatted.code);

    // Clear previous evaluation status when switching courses
    setEvaluationStatus('');
    setIsCheckingEvaluation(false);
    setSaveStatus('');
    setSubmissionStatus('');
    setShowEditButton(false);

    // Set course info for caching
    const courseCode = option.isAdvance ? option.courseCode : formatted.code;
    setCurrentCourseCode(courseCode);
    setIsAdvanceCourse(option.isAdvance || false);

    // Clear previous responses
    setEvaluationResponses({});
    setAdditionalResponses({});
    setCritiqueText('');

    // Always check server-side evaluation status first, regardless of cache
    if (!option.isAdvance) {
      // For regular courses, always check WebView first to get server-side status
      selectCourseInWebView(option.value, formatted.main);
    } else {
      // For advance courses, check cache but still allow evaluation
      if (courseCode) {
        const hasCache = await hasCachedEvaluation(courseCode);
        if (hasCache) {
          console.log('📂 Found cached evaluation for advance course:', courseCode);
          await loadEvaluationFromCache(courseCode);
          setEvaluationStatus('Course pre-evaluation has been saved');
          setShowEditButton(true);
        } else {
          console.log('🆕 Advance course selected - showing questionnaire');
          setEvaluationStatus('Course evaluation available');
        }
      } else {
        console.log('🆕 Advance course selected - showing questionnaire');
        setEvaluationStatus('Course evaluation available');
      }
    }
  };

  // Toggle dropdown visibility
  const toggleDropdown = () => {
    setIsDropdownVisible(!isDropdownVisible);
  };

  // Toggle staff dropdown visibility
  const toggleStaffDropdown = () => {
    setIsStaffDropdownVisible(!isStaffDropdownVisible);
  };

  // Handle staff option selection
  const handleStaffSelect = (option) => {
    const formatted = formatStaffName(option.text); // Use staff-specific formatting logic
    setSelectedStaff(formatted);
    setIsStaffDropdownVisible(false);
    console.log('⭐ Staff selected for evaluation:', formatted.main, formatted.code);

    // Clear previous evaluation status when switching staff
    setEvaluationStatus('');
    setIsCheckingEvaluation(false);

    // Select staff in WebView and check evaluation status
    selectStaffInWebView(option.value, formatted.main);
  };

  // Switch to staff evaluation mode
  const handleEvaluateStaffPress = () => {
    console.log('👨‍🏫 Switching to Staff Evaluation mode');
    setEvaluationMode('staff');

    // Reset staff dropdown state
    setSelectedStaff({ main: 'Select Staff', code: '' });
    setIsStaffDropdownLoading(true);
    setStaffDropdownOptions([]);
    setEvaluationStatus(''); // Reset evaluation status
    setIsCheckingEvaluation(false);

    // Update URL for staff evaluation - CORRECT URL
    if (credentials) {
      const staffUrl = `https://${credentials.username}:${credentials.password}@apps.guc.edu.eg/student_ext/Evaluation/EvaluateStaff.aspx`;
      setEvaluateUrl(staffUrl);

      // Reload WebView to get staff dropdown
      if (webViewRef.current) {
        webViewRef.current.reload();
      }
    }
  };

  // Switch back to course evaluation mode
  const handleEvaluateCoursePress = () => {
    console.log('📚 Switching to Course Evaluation mode');
    setEvaluationMode('course');

    // Reset course dropdown state
    setSelectedCourse({ main: 'Select Course', code: '' });
    setIsDropdownLoading(true);
    setDropdownOptions([]);
    setEvaluationStatus(''); // Reset evaluation status
    setIsCheckingEvaluation(false);

    // Update URL for course evaluation
    if (credentials) {
      const courseUrl = `https://${credentials.username}:${credentials.password}@apps.guc.edu.eg/student_ext/Evaluation/EvaluateCourse.aspx`;
      setEvaluateUrl(courseUrl);

      // Reload WebView to get course dropdown
      if (webViewRef.current) {
        webViewRef.current.reload();
      }
    }
  };

  // Function to select staff in WebView and check evaluation status
  const selectStaffInWebView = (staffValue, staffName) => {
    if (webViewRef.current) {
      console.log('🔄 Selecting staff in WebView:', staffValue, staffName);
      setIsCheckingEvaluation(true);
      setEvaluationStatus('');

      // Phase 1: Select dropdown and dispatch events
      const phase1JS = `
        (function() {
          try {
            // Set the value of the staff dropdown
            const dropdown = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_stfIdLst');

            if (dropdown) {
              dropdown.value = '${staffValue}';

              // Trigger multiple events to ensure it works
              dropdown.dispatchEvent(new Event('change', { bubbles: true }));
              dropdown.dispatchEvent(new Event('input', { bubbles: true }));

              // Notify React Native that phase 1 is complete
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'staff_selection_phase1_complete',
                message: 'Staff dropdown selection completed, waiting for page response...',
                staffName: '${staffName}'
              }));
            } else {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'staff_selection_error',
                error: 'Staff dropdown not found',
                staffName: '${staffName}'
              }));
            }
          } catch (error) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'staff_selection_error',
              error: error.message,
              staffName: '${staffName}'
            }));
          }
        })();
        true;
      `;

      webViewRef.current.injectJavaScript(phase1JS);

      // Phase 2: Check for evaluation status message after a delay (optimized)
      safeSetTimeout(() => {
        const phase2JS = `
          (function() {
            try {
              console.log('🔍 Checking for evaluation status message...');

              // Look for the message label
              const msgLabel = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_msgLbl');

              if (msgLabel) {
                const messageText = msgLabel.textContent || msgLabel.innerText || '';
                console.log('📋 Message found:', messageText);

                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'evaluation_status_check',
                  message: messageText.trim(),
                  staffName: '${staffName}'
                }));
              } else {
                console.log('❌ Message label not found');
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'evaluation_status_check',
                  message: '',
                  staffName: '${staffName}'
                }));
              }
            } catch (error) {
              console.log('❌ Error checking evaluation status:', error);
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'evaluation_status_error',
                error: error.message,
                staffName: '${staffName}'
              }));
            }
          })();
          true;
        `;

        if (webViewRef.current) {
          webViewRef.current.injectJavaScript(phase2JS);
        }
      }, 2000); // Wait 2 seconds for page to update
    }
  };

  // Function to select course in WebView and check evaluation status
  const selectCourseInWebView = (courseValue, courseName) => {
    if (webViewRef.current) {
      console.log('🔄 Selecting course in WebView:', courseValue, courseName);
      setIsCheckingEvaluation(true);
      setEvaluationStatus('');

      // Phase 1: Select dropdown and dispatch events
      const phase1JS = `
        (function() {
          try {
            // Set the value of the course dropdown
            const dropdown = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_crsIdLst');

            if (dropdown) {
              dropdown.value = '${courseValue}';

              // Trigger multiple events to ensure it works
              dropdown.dispatchEvent(new Event('change', { bubbles: true }));
              dropdown.dispatchEvent(new Event('input', { bubbles: true }));

              // Notify React Native that phase 1 is complete
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'course_selection_phase1_complete',
                message: 'Course dropdown selection completed, waiting for page response...',
                courseName: '${courseName}'
              }));
            } else {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'course_selection_error',
                error: 'Course dropdown not found',
                courseName: '${courseName}'
              }));
            }
          } catch (error) {
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'course_selection_error',
              error: error.message,
              courseName: '${courseName}'
            }));
          }
        })();
        true;
      `;

      webViewRef.current.injectJavaScript(phase1JS);

      // Phase 2: Check for evaluation status message after a delay
      setTimeout(() => {
        const phase2JS = `
          (function() {
            try {
              console.log('🔍 Checking for course evaluation status message...');

              // Look for the message label
              const msgLabel = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_msgLbl');

              if (msgLabel) {
                const messageText = msgLabel.textContent || msgLabel.innerText || '';
                console.log('📋 Message found:', messageText);

                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'course_evaluation_status_check',
                  message: messageText.trim(),
                  courseName: '${courseName}'
                }));
              } else {
                console.log('❌ Message label not found');
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'course_evaluation_status_check',
                  message: '',
                  courseName: '${courseName}'
                }));
              }
            } catch (error) {
              console.log('❌ Error checking course evaluation status:', error);
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'course_evaluation_status_error',
                error: error.message,
                courseName: '${courseName}'
              }));
            }
          })();
          true;
        `;

        if (webViewRef.current) {
          webViewRef.current.injectJavaScript(phase2JS);
        }
      }, 2000); // Wait 2 seconds for page to update
    }
  };

  // Format grades course name like GradesScreen: cut until '-', split on space, rearrange
  const formatGradesCourse = (courseName) => {
    if (!courseName) return { main: '', code: '' };

    const dashIndex = courseName.indexOf('- ');
    if (dashIndex !== -1) {
      const afterDash = courseName.substring(dashIndex + 2); // +2 to skip "- "
      const parts = afterDash.split(' ');

      if (parts.length >= 2) {
        const code = parts[0]; // Course code (e.g., "CSEN602")
        const name = parts.slice(1).join(' '); // Course name (e.g., "Operating Systems")
        return { main: name, code: code };
      }
      return { main: afterDash, code: '' };
    }
    return { main: courseName, code: '' };
  };

  // Function to compare courses and find advance evaluation options
  const compareCourses = (evaluationCourses, gradesCoursesList) => {
    console.log('🔍 Comparing courses for advance evaluation...');
    console.log('📋 Evaluation courses:', evaluationCourses.length);
    console.log('📋 Grades courses:', gradesCoursesList.length);

    const advanceOptions = [];

    // Extract last 2 strings from evaluation courses for comparison
    const evaluationCourseKeys = new Set();

    evaluationCourses.forEach(evalCourse => {
      const courseText = evalCourse.text;
      console.log('🔍 Processing evaluation course:', courseText);

      // Split by spaces and get last 2 strings
      const words = courseText.trim().split(/\s+/);
      if (words.length >= 2) {
        const lastTwoWords = words.slice(-2).join('');
        evaluationCourseKeys.add(lastTwoWords);
        console.log('📝 Evaluation key extracted:', lastTwoWords);
      }
    });

    console.log('🔑 Total evaluation keys:', evaluationCourseKeys.size);

    // Process and find courses in grades that are not in evaluation
    gradesCoursesList.forEach(gradesCourse => {
      const originalText = gradesCourse.text;
      console.log('🔍 Checking grades course:', originalText);

      // Format the grades course using GradesScreen logic
      const formatted = formatGradesCourse(originalText);
      const gradesCourseCode = formatted.code; // This will be like "CSEN602"

      console.log('📝 Formatted grades course - Code:', gradesCourseCode, 'Name:', formatted.main);

      let isInEvaluation = false;

      // Check if the grades course code matches any evaluation key
      for (const evalKey of evaluationCourseKeys) {
        if (gradesCourseCode && gradesCourseCode.toLowerCase() === evalKey.toLowerCase()) {
          console.log('✅ Match found:', gradesCourseCode, 'matches', evalKey);
          isInEvaluation = true;
          break;
        }
      }

      if (!isInEvaluation && formatted.main && formatted.code) {
        console.log('🆕 Found advance evaluation course:', formatted.main, formatted.code);
        // Create a properly formatted course option without dash before code
        const formattedText = `${formatted.main} - ${formatted.code}`;
        advanceOptions.push({
          value: gradesCourse.value,
          text: formattedText,
          courseCode: formatted.code, // Store course code for caching
          isAdvance: true
        });
      } else {
        console.log('❌ Course already in evaluation or invalid format:', originalText);
      }
    });

    console.log('✅ Found', advanceOptions.length, 'advance evaluation courses');
    setAdvanceCourses(advanceOptions);

    return advanceOptions;
  };

  // Handle grades WebView load
  const handleGradesWebViewLoad = () => {
    console.log('📱 Grades WebView loaded, extracting courses...');
    extractGradesCourses();
  };

  // Extract courses from grades page
  const extractGradesCourses = (attemptNumber = 0, maxAttempts = 3) => {
    const delay = attemptNumber * 1000;

    setTimeout(() => {
      const jsCode = `
        (function() {
          try {
            console.log('🔍 Looking for grades dropdown on attempt ${attemptNumber + 1}...');

            const dropdown = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_smCrsLst');
            if (dropdown) {
              console.log('✅ Grades dropdown found on attempt ${attemptNumber + 1}');
              const options = [];
              for (let i = 0; i < dropdown.options.length; i++) {
                const option = dropdown.options[i];
                // Skip placeholder options
                if (option.value && option.value.trim() !== '' &&
                    !option.text.toLowerCase().includes('choose') &&
                    !option.text.toLowerCase().includes('select')) {
                  options.push({
                    value: option.value,
                    text: option.text
                  });
                }
              }
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'grades_courses_extracted',
                options: options,
                attempt: ${attemptNumber + 1}
              }));
            } else {
              console.log('❌ Grades dropdown not found on attempt ${attemptNumber + 1}');
              if (${attemptNumber + 1} < ${maxAttempts}) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'grades_retry_needed',
                  attempt: ${attemptNumber + 1},
                  maxAttempts: ${maxAttempts}
                }));
              } else {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'grades_extraction_error',
                  error: 'Grades dropdown not found after ' + ${maxAttempts} + ' attempts'
                }));
              }
            }
          } catch (error) {
            console.log('❌ Error extracting grades courses on attempt ${attemptNumber + 1}:', error);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'grades_retry_needed',
              attempt: ${attemptNumber + 1},
              maxAttempts: ${maxAttempts},
              error: error.message
            }));
          }
        })();
      `;

      if (gradesWebViewRef.current) {
        gradesWebViewRef.current.injectJavaScript(jsCode);
      }
    }, delay);
  };

  // Handle grades WebView messages
  const handleGradesWebViewMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('📨 Grades WebView message received:', data.type);

      switch (data.type) {
        case 'grades_courses_extracted':
          console.log(`📋 Grades courses extracted on attempt ${data.attempt || 1}:`, data.options.length, 'courses');
          setGradesCourses(data.options);
          setIsGradesLoading(false);

          // Compare with evaluation courses if they're available
          if (dropdownOptions.length > 0) {
            compareCourses(dropdownOptions, data.options);
          }
          break;

        case 'grades_retry_needed':
          console.log(`🔄 Retrying grades extraction (attempt ${data.attempt}/${data.maxAttempts})...`);
          if (data.attempt < data.maxAttempts) {
            extractGradesCourses(data.attempt, data.maxAttempts);
          } else {
            console.log('❌ Max attempts reached for grades extraction');
            setIsGradesLoading(false);
          }
          break;

        case 'grades_extraction_error':
          console.log('❌ Grades extraction failed:', data.error);
          setIsGradesLoading(false);
          break;

        default:
          console.log('📨 Unknown grades message type:', data.type);
          break;
      }
    } catch (error) {
      console.log('❌ Error parsing grades WebView message:', error);
    }
  };

  const handleGradesWebViewError = (syntheticEvent) => {
    const { nativeEvent } = syntheticEvent;
    console.log('❌ Grades WebView error:', nativeEvent);
    setIsGradesLoading(false);
  };

  const openSidebar = () => {
    setIsSidebarVisible(true);
    Animated.timing(sidebarAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeSidebar = () => {
    Animated.timing(sidebarAnim, {
      toValue: -300,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsSidebarVisible(false);
    });
  };

  // Swipe gesture handler for opening/closing sidebar - Enhanced for Android
  const swipeGestureHandler = PanResponder.create({
    onStartShouldSetPanResponder: () => false, // Don't capture immediately
    onStartShouldSetPanResponderCapture: () => false, // Don't capture on start
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to significant horizontal swipes
      const { dx, dy } = gestureState;

      // Only capture if it's a clear horizontal swipe with significant movement
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
      // Only capture significant horizontal swipes to prevent Android system gestures
      const { dx, dy } = gestureState;
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onPanResponderGrant: () => {
      // Gesture has been granted - prevent other handlers
      return true;
    },
    onPanResponderMove: () => {
      // Optional: Add visual feedback during swipe
    },
    onPanResponderRelease: (evt, gestureState) => {
      const { dx, dy } = gestureState;
      const isHorizontalSwipe = Math.abs(dx) > Math.abs(dy);
      const swipeDistance = Math.abs(dx);

      if (isHorizontalSwipe && swipeDistance > 100) {
        if (dx > 0) {
          // Swipe right - open sidebar
          openSidebar();
        } else {
          // Swipe left - close sidebar if it's open
          if (isSidebarVisible) {
            closeSidebar();
          }
        }
      }
    },
    onPanResponderTerminationRequest: () => false, // Don't allow termination
    onShouldBlockNativeResponder: () => true, // Block native responders
  });

  // Rotation animation functions
  const startRotationAnimation = () => {
    refreshRotation.setValue(0);
    Animated.loop(
      Animated.timing(refreshRotation, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    ).start();
  };

  const stopRotationAnimation = () => {
    Animated.timing(refreshRotation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const handleRefresh = () => {
    console.log('🔄 Refresh button pressed - Evaluate screen');
    setIsRefreshing(true);
    startRotationAnimation();

    // Only refresh the output/results, not the dropdown options
    if (evaluationMode === 'course') {
      // For course mode: re-trigger course selection if a course is selected
      if (selectedCourse && selectedCourse.main !== 'Select Course') {
        console.log('🔄 Refreshing course evaluation output for:', selectedCourse.main);
        // Find the selected course option and re-trigger selection
        const selectedOption = dropdownOptions.find(option => {
          const formatted = formatCourseName(option.text);
          return formatted.main === selectedCourse.main && formatted.code === selectedCourse.code;
        });
        if (selectedOption) {
          handleCourseSelect(selectedOption);
        }
      }
    } else {
      // For staff mode: re-trigger staff selection if a staff is selected
      if (selectedStaff && selectedStaff.main !== 'Select Staff') {
        console.log('🔄 Refreshing staff evaluation output for:', selectedStaff.main);
        // Find the selected staff option and re-trigger selection
        const selectedOption = staffDropdownOptions.find(option => {
          const formatted = formatStaffName(option.text);
          return formatted.main === selectedStaff.main && formatted.code === selectedStaff.code;
        });
        if (selectedOption) {
          selectStaffInWebView(selectedOption.value, selectedStaff.main);
        }
      }
    }

    setTimeout(() => {
      setIsRefreshing(false);
      stopRotationAnimation();
      console.log('✅ Refresh completed - Evaluate screen');
    }, 1000);
  };

  // Extract dropdown options with retry logic
  const extractDropdownOptions = (attemptNumber = 0, maxAttempts = 3) => {
    const delay = attemptNumber * 600; // Optimized: 600ms increments instead of 1000ms

    safeSetTimeout(() => {
      const jsCode = `
        (function() {
          try {
            console.log('🔍 Looking for evaluate dropdown on attempt ${attemptNumber + 1}...');
            console.log('🔍 Current URL:', window.location.href);
            console.log('🔍 Page title:', document.title);

            // Try to find course dropdown first
            const courseDropdown = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_crsIdLst');
            // Try to find staff dropdown
            const staffDropdown = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_stfIdLst');

            console.log('🔍 Course dropdown found:', !!courseDropdown);
            console.log('🔍 Staff dropdown found:', !!staffDropdown);

            if (courseDropdown) {
              console.log('✅ Course dropdown found on attempt ${attemptNumber + 1}');
              const options = [];
              for (let i = 0; i < courseDropdown.options.length; i++) {
                const option = courseDropdown.options[i];
                // Skip placeholder options like "Choose a course" or empty values
                if (option.value && option.value.trim() !== '' &&
                    !option.text.toLowerCase().includes('choose') &&
                    !option.text.toLowerCase().includes('select')) {
                  options.push({
                    value: option.value,
                    text: option.text
                  });
                }
              }
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'course_dropdown_options',
                options: options,
                attempt: ${attemptNumber + 1}
              }));
            }

            if (staffDropdown) {
              console.log('✅ Staff dropdown found on attempt ${attemptNumber + 1}');
              const options = [];
              for (let i = 0; i < staffDropdown.options.length; i++) {
                const option = staffDropdown.options[i];
                // Skip placeholder options like "Choose staff" or empty values
                if (option.value && option.value.trim() !== '' &&
                    !option.text.toLowerCase().includes('choose') &&
                    !option.text.toLowerCase().includes('select')) {
                  options.push({
                    value: option.value,
                    text: option.text
                  });
                }
              }
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'staff_dropdown_options',
                options: options,
                attempt: ${attemptNumber + 1}
              }));
            }

            if (!courseDropdown && !staffDropdown) {
              console.log('❌ No dropdowns found on attempt ${attemptNumber + 1}');
              if (${attemptNumber + 1} < ${maxAttempts}) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'dropdown_retry_needed',
                  attempt: ${attemptNumber + 1},
                  maxAttempts: ${maxAttempts}
                }));
              } else {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'dropdown_error',
                  error: 'No dropdowns found after ' + ${maxAttempts} + ' attempts'
                }));
              }
            }
          } catch (error) {
            console.log('❌ Error extracting dropdown on attempt ${attemptNumber + 1}:', error);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'dropdown_retry_needed',
              attempt: ${attemptNumber + 1},
              maxAttempts: ${maxAttempts},
              error: error.message
            }));
          }
        })();
      `;

      if (webViewRef.current) {
        webViewRef.current.injectJavaScript(jsCode);
      }
    }, delay);
  };

  const handleWebViewLoad = () => {
    console.log('📱 Evaluate WebView loaded, extracting dropdown options...');
    extractDropdownOptions();

    // Check for submission success message on page load (optimized)
    safeSetTimeout(() => {
      checkForSubmissionMessage();
    }, 1000); // Optimized from 2 seconds to 1 second
  };

  // Check for submission success message
  const checkForSubmissionMessage = () => {
    if (!webViewRef.current) return;

    const checkJS = `
      (function() {
        try {
          const msgElement = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_msgLbl');

          if (msgElement && msgElement.textContent) {
            const messageText = msgElement.textContent.trim();
            console.log('📋 Page message found:', messageText);

            if (messageText.includes('Course Evaluation has been posted. Thanks for your participation')) {
              console.log('✅ Submission success message detected');
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'submission_success_detected',
                message: messageText
              }));
            }
          }
        } catch (error) {
          console.log('❌ Error checking submission message:', error);
        }
      })();
      true;
    `;

    webViewRef.current.injectJavaScript(checkJS);
  };

  const handleWebViewMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('📨 Evaluate WebView message received:', data.type);

      switch (data.type) {
        case 'course_dropdown_options':
          console.log(`📋 Course dropdown options received on attempt ${data.attempt || 1}:`, data.options.length, 'courses');
          setDropdownOptions(data.options);
          setIsDropdownLoading(false);

          // Compare with grades courses if they're available
          if (gradesCourses.length > 0) {
            compareCourses(data.options, gradesCourses);
          }
          break;

        case 'staff_dropdown_options':
          console.log(`👨‍🏫 Staff dropdown options received on attempt ${data.attempt || 1}:`, data.options.length, 'staff members');
          setStaffDropdownOptions(data.options);
          setIsStaffDropdownLoading(false);
          break;

        case 'dropdown_retry_needed':
          console.log(`🔄 Retrying dropdown extraction (attempt ${data.attempt}/${data.maxAttempts})...`);
          if (data.attempt < data.maxAttempts) {
            extractDropdownOptions(data.attempt, data.maxAttempts);
          } else {
            console.log('❌ Max attempts reached for dropdown extraction');
            setIsDropdownLoading(false);
            setIsStaffDropdownLoading(false);
          }
          break;

        case 'dropdown_error':
          console.log('❌ Dropdown extraction failed:', data.error);
          setIsDropdownLoading(false);
          setIsStaffDropdownLoading(false);
          break;

        case 'staff_selection_phase1_complete':
          console.log('✅ Staff selection phase 1 completed for:', data.staffName);
          break;

        case 'evaluation_status_check':
          console.log('📋 Evaluation status check completed for:', data.staffName);
          setIsCheckingEvaluation(false);

          if (data.message && data.message.includes('You have already evaluated this staff member')) {
            console.log('⚠️ Staff already evaluated:', data.staffName);
            setEvaluationStatus(`${data.staffName} has already been evaluated`);
          } else {
            console.log('✅ Staff can be evaluated:', data.staffName);
            setEvaluationStatus('Under Development');
          }
          break;

        case 'staff_selection_error':
          console.log('❌ Staff selection error:', data.error);
          setIsCheckingEvaluation(false);
          setEvaluationStatus(`Error selecting ${data.staffName}: ${data.error}`);
          break;

        case 'evaluation_status_error':
          console.log('❌ Evaluation status check error:', data.error);
          setIsCheckingEvaluation(false);
          setEvaluationStatus(`Error checking evaluation status for ${data.staffName}`);
          break;

        case 'course_selection_phase1_complete':
          console.log('✅ Course selection phase 1 completed for:', data.courseName);
          break;

        case 'course_evaluation_status_check':
          console.log('📋 Course evaluation status check completed for:', data.courseName);
          setIsCheckingEvaluation(false);

          if (data.message && data.message.includes('You have already evaluated this Course')) {
            console.log('⚠️ Course already evaluated on server:', data.courseName);
            setEvaluationStatus(`${data.courseName} has already been evaluated`);
            setShowEditButton(false);
          } else {
            console.log('✅ Course can be evaluated on server:', data.courseName);
            // Now check if we have cached evaluation for this course
            if (currentCourseCode) {
              hasCachedEvaluation(currentCourseCode).then(hasCache => {
                if (hasCache) {
                  console.log('📂 Found cached evaluation, loading...');
                  loadEvaluationFromCache(currentCourseCode);
                  setEvaluationStatus('Course pre-evaluation has been saved');
                  setShowEditButton(true);
                } else {
                  console.log('🆕 No cache found, course evaluation available');
                  setEvaluationStatus('Course evaluation available');
                  setShowEditButton(false);
                }
              });
            } else {
              console.log('🆕 No course code, course evaluation available');
              setEvaluationStatus('Course evaluation available');
              setShowEditButton(false);
            }
          }
          break;

        case 'course_selection_error':
          console.log('❌ Course selection error:', data.error);
          setIsCheckingEvaluation(false);
          setEvaluationStatus(`Error selecting ${data.courseName}: ${data.error}`);
          break;

        case 'course_evaluation_status_error':
          console.log('❌ Course evaluation status check error:', data.error);
          setIsCheckingEvaluation(false);
          setEvaluationStatus(`Error checking evaluation status for ${data.courseName}`);
          break;

        case 'responses_transferred':
          console.log('✅ Responses transferred successfully');
          break;

        case 'transfer_error':
          console.log('❌ Transfer error:', data.error);
          break;

        case 'evaluation_form_submitted':
          console.log('✅ Form submitted successfully');
          setIsSubmitting(false);
          setSubmissionVerified(true);
          setSubmissionStatus('Evaluation submitted successfully!');
          setEvaluationStatus('Evaluation completed');
          break;

        case 'submission_error':
          console.log('❌ Submission error:', data.error);
          setIsSubmitting(false);
          setSubmissionStatus('Error submitting evaluation. Please try again.');
          scrollToErrorMessage();
          setTimeout(() => setSubmissionStatus(''), 5000);
          break;

        case 'submission_success_detected':
          console.log('✅ Submission success detected on page load');
          setIsSubmitting(false);
          setSubmissionVerified(true);
          setSubmissionStatus('Evaluation submitted successfully!');
          setEvaluationStatus('Evaluation completed');
          break;

        default:
          console.log('📨 Unknown message type:', data.type);
          break;
      }
    } catch (error) {
      console.log('❌ Error parsing WebView message:', error);
    }
  };

  const handleWebViewError = (syntheticEvent) => {
    const { nativeEvent } = syntheticEvent;
    console.log('❌ Evaluate WebView error:', nativeEvent);
    setIsDropdownLoading(false);
  };

  return (
    <ThemeTransitionWrapper>
      <View style={{ flex: 1 }} {...swipeGestureHandler.panHandlers}>
        <SafeAreaView style={styles.container} edges={['top']}>
      {/* Sidebar Button */}
      <View style={styles.sidebarButtonContainer} pointerEvents="box-none">
        <TouchableOpacity
          style={styles.sidebarButton}
          onPress={openSidebar}
        >
          <HamburgerIcon size={20} color={theme.colors.primary} strokeWidth={3} />
        </TouchableOpacity>
      </View>

      {/* Page Title */}
      <View style={styles.titleContainer} pointerEvents="box-none">
        <Text style={styles.title}>Evaluate</Text>
      </View>

      {/* Evaluate Staff Toggle Button */}
      <View style={styles.staffToggleButtonContainer} pointerEvents="box-none">
        <TouchableOpacity
          style={styles.staffToggleButton}
          onPress={evaluationMode === 'course' ? handleEvaluateStaffPress : handleEvaluateCoursePress}
        >
          {evaluationMode === 'course' ? (
            <ProfileIcon size={28} color={safeCurrentThemeName === 'navy' ? '#DC2626' : '#EAB308'} />
          ) : (
            <BookIcon size={28} color={safeCurrentThemeName === 'navy' ? '#DC2626' : '#EAB308'} />
          )}
        </TouchableOpacity>
      </View>

      {/* Refresh Button */}
      <View style={styles.refreshButtonContainer} pointerEvents="box-none">
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={handleRefresh}
          disabled={isRefreshing}
        >
          <Animated.View
            style={{
              transform: [{
                rotate: refreshRotation.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg']
                })
              }]
            }}
          >
            <RefreshIcon
              size={24}
              color={isRefreshing ? theme.colors.textSecondary : safeCurrentThemeName === 'navy' ? '#DC2626' : '#f1c40f'}
            />
          </Animated.View>
        </TouchableOpacity>
      </View>

      {/* Main Content */}
      <TouchableOpacity
        style={styles.mainContent}
        activeOpacity={1}
        onPress={() => {
          if (isDropdownVisible || isStaffDropdownVisible || isBulkDropdownVisible) {
            console.log('🎯 Main content pressed - closing dropdowns');
            setIsDropdownVisible(false);
            setIsStaffDropdownVisible(false);
            setIsBulkDropdownVisible(false);
          }
        }}
      >
        {isLoading || !evaluateUrl ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#EAB308" />
            <Text style={styles.loadingText}>Loading evaluation page...</Text>
          </View>
        ) : (
          <>
            {/* Dropdown Menus Section */}
            <View style={styles.dropdownSection}>
              <Text style={styles.sectionTitle}>
                {evaluationMode === 'course' ? 'Course Selection' : 'Staff Selection'}
              </Text>

              {/* Course or Staff Dropdown */}
              {evaluationMode === 'course' ? (
                <View style={styles.dropdownContainer}>
                  <Text style={styles.dropdownLabel}>Select Course:</Text>
                  <View style={styles.dropdownWrapper}>
                    <TouchableOpacity
                      style={[styles.dropdown, isDropdownLoading && styles.dropdownDisabled]}
                      onPress={toggleDropdown}
                      disabled={isDropdownLoading}
                    >
                      <View style={styles.dropdownContent}>
                        {selectedCourse && selectedCourse.main && selectedCourse.main !== 'Select Course' ? (
                          <View style={styles.selectedCourseContainer}>
                            <Text style={styles.dropdownText}>{selectedCourse.main}</Text>
                            <Text style={styles.courseCode}>{selectedCourse.code}</Text>
                          </View>
                        ) : (
                          <Text style={styles.dropdownPlaceholder}>Select Course</Text>
                        )}
                      </View>
                      <Text style={[
                        styles.dropdownArrow,
                        { transform: [{ rotate: isDropdownVisible ? '180deg' : '0deg' }] }
                      ]}>
                        ▼
                      </Text>
                    </TouchableOpacity>

{/* Course Dropdown Options List - Rendered at end for proper layering */}
        {isDropdownVisible && (dropdownOptions.length > 0 || advanceCourses.length > 0) && (
          <View style={styles.dropdownListFloating}>
            <ScrollView
              style={styles.optionsList}
              showsVerticalScrollIndicator={false}
              nestedScrollEnabled={true}
              keyboardShouldPersistTaps="handled"
            >
              {/* Regular evaluation courses */}
              {dropdownOptions.map((option, index) => {
                const formatted = formatCourseName(option.text);
                return (
                  <TouchableOpacity
                    key={`regular-${index}`}
                    style={styles.optionItem}
                    onPress={() => handleCourseSelect(option)}
                  >
                    <View style={styles.optionContent}>
                      <Text style={styles.optionText}>{formatted.main}</Text>
                      <Text style={styles.optionCode}>{formatted.code}</Text>
                    </View>
                  </TouchableOpacity>
                );
              })}

              {/* Advance evaluation courses */}
              {advanceCourses.map((option, index) => {
                const formatted = formatCourseName(option.text);
                return (
                  <TouchableOpacity
                    key={`advance-${index}`}
                    style={styles.optionItem}
                    onPress={() => handleCourseSelect(option)}
                  >
                    <View style={styles.optionContent}>
                      <Text style={styles.optionText}>{formatted.main}</Text>
                      <Text style={styles.advanceOptionCode}>(Evaluate in advance)</Text>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>
        )}                
          </View>

                  {/* Course Options count indicator */}
                  {isDropdownLoading ? (
                    <View style={styles.statusLoadingContainer}>
                      <ActivityIndicator size="small" color="#9CA3AF" />
                      <Text style={styles.optionsCount}>Loading</Text>
                    </View>
                  ) : dropdownOptions.length > 0 || advanceCourses.length > 0 ? (
                    <View>
                      <Text style={styles.optionsCount}>
                        {dropdownOptions.length} {dropdownOptions.length === 1 ? 'course' : 'courses'} available
                      </Text>
                      {advanceCourses.length > 0 && (
                        <Text style={styles.advanceCoursesCount}>
                          {advanceCourses.length} courses can be evaluated in advance
                        </Text>
                      )}
                    </View>
                  ) : (
                    <Text style={styles.optionsCount}>No courses found</Text>
                  )}
                </View>
              ) : (
                <View style={styles.dropdownContainer}>
                  <Text style={styles.dropdownLabel}>Select Staff:</Text>
                  <View style={styles.dropdownWrapper}>
                    <TouchableOpacity
                      style={[styles.dropdown, isStaffDropdownLoading && styles.dropdownDisabled]}
                      onPress={toggleStaffDropdown}
                      disabled={isStaffDropdownLoading}
                    >
                      <View style={styles.dropdownContent}>
                        {selectedStaff && selectedStaff.main && selectedStaff.main !== 'Select Staff' ? (
                          <View style={styles.selectedCourseContainer}>
                            <Text style={styles.dropdownText}>{selectedStaff.main}</Text>
                            <Text style={styles.courseCode}>{selectedStaff.code}</Text>
                          </View>
                        ) : (
                          <Text style={styles.dropdownPlaceholder}>Select Staff</Text>
                        )}
                      </View>
                      <Text style={[
                        styles.dropdownArrow,
                        { transform: [{ rotate: isStaffDropdownVisible ? '180deg' : '0deg' }] }
                      ]}>
                        ▼
                      </Text>
                    </TouchableOpacity>

{/* Staff Dropdown Options List - Rendered at end for proper layering */}
        {isStaffDropdownVisible && staffDropdownOptions.length > 0 && (
          <View style={styles.staffDropdownListFloating}>
            <ScrollView
              style={styles.optionsList}
              showsVerticalScrollIndicator={false}
              nestedScrollEnabled={true}
              keyboardShouldPersistTaps="handled"
            >
              {staffDropdownOptions.map((option, index) => {
                const formatted = formatStaffName(option.text);
                return (
                  <TouchableOpacity
                    key={index}
                    style={styles.optionItem}
                    onPress={() => handleStaffSelect(option)}
                  >
                    <View style={styles.optionContent}>
                      <Text style={styles.optionText}>{formatted.main}</Text>
                      <Text style={styles.optionCode}>{formatted.code}</Text>
                    </View>
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </View>
        )}
                          </View>

                  {/* Staff Options count indicator */}
                  {isStaffDropdownLoading ? (
                    <View style={styles.statusLoadingContainer}>
                      <ActivityIndicator size="small" color="#9CA3AF" />
                      <Text style={styles.optionsCount}>Loading</Text>
                    </View>
                  ) : staffDropdownOptions.length > 0 ? (
                    <Text style={styles.optionsCount}>
                      {staffDropdownOptions.length} staff members available
                    </Text>
                  ) : (
                    <Text style={styles.optionsCount}>No staff found</Text>
                  )}
                </View>
              )}



            </View>

            {/* Evaluation Feedback - Compact Component */}
            {(evaluationStatus === 'Course pre-evaluation has been saved' || submissionVerified) && (
              <View style={styles.savedStatusContainer}>
                {submissionVerified ? (
                  <>
                    <Text style={styles.savedStatusText}>
                      ✅ Evaluation Submitted Successfully!
                    </Text>
                    <Text style={styles.submissionSuccessSubtext}>
                      Thank you for your participation.
                    </Text>
                  </>
                ) : (
                  <>
                    <Text style={styles.savedStatusText}>
                      Course pre-evaluation has been saved
                    </Text>
                    {showEditButton && (
                      <TouchableOpacity
                        style={styles.editAnswersButton}
                        onPress={handleEditAnswers}
                      >
                        <Text style={styles.editAnswersButtonText}>Edit Your Answers</Text>
                      </TouchableOpacity>
                    )}
                  </>
                )}
              </View>
            )}

            {/* Evaluation Status Display - Outside dropdown section */}
            {((evaluationMode === 'staff' && (selectedStaff.main !== 'Select Staff' || isCheckingEvaluation || evaluationStatus)) ||
              (evaluationMode === 'course' && (selectedCourse.main !== 'Select Course' || isCheckingEvaluation || evaluationStatus))) &&
              evaluationStatus !== 'Course pre-evaluation has been saved' && !submissionVerified && (
              <View style={[
                styles.evaluationStatusContainer,
                (evaluationStatus && evaluationStatus.includes('already been evaluated')) && styles.evaluationStatusContainerSmall,
                isCheckingEvaluation && styles.evaluationStatusContainerLoading
              ]}>
                {isCheckingEvaluation ? (
                  <View style={styles.statusLoadingContainerCentered}>
                    <ActivityIndicator size="small" color="#EAB308" />
                    <Text style={styles.evaluationStatusText}>Checking evaluation status...</Text>
                  </View>
                ) : evaluationStatus ? (
                  <>
                    {evaluationStatus === 'Course evaluation available' && !submissionVerified ? (
                      // Show evaluation questionnaire
                      <View style={styles.questionnaireContainer}>
                        <Text style={styles.questionnaireTitle}>Course Evaluation</Text>

                        {/* Bulk Rating Section */}
                        <View style={styles.bulkRatingRow}>
                          {/* Bulk Rating Dropdown Container */}
                          <View style={styles.bulkDropdownContainer}>
                            <TouchableOpacity
                              style={styles.bulkDropdownButton}
                              onPress={() => setIsBulkDropdownVisible(!isBulkDropdownVisible)}
                            >
                              <Text style={styles.bulkDropdownButtonText}>
                                {bulkRating ? bulkRatingOptions.find(opt => opt.value === bulkRating)?.label : 'Select Rating'}
                              </Text>
                              <Text style={styles.bulkDropdownArrow}>▼</Text>
                            </TouchableOpacity>

                            {/* Bulk Rating Dropdown Options */}
                            {isBulkDropdownVisible && (
                              <View style={styles.bulkDropdownOptions}>
                                <ScrollView
                                  style={styles.bulkDropdownScrollView}
                                  showsVerticalScrollIndicator={true}
                                  nestedScrollEnabled={true}
                                >
                                  {bulkRatingOptions.map((option, index) => (
                                    <TouchableOpacity
                                      key={index}
                                      style={styles.bulkDropdownOption}
                                      onPress={() => handleBulkRatingSelect(option)}
                                    >
                                      <Text style={styles.bulkDropdownOptionText}>{option.label}</Text>
                                    </TouchableOpacity>
                                  ))}
                                </ScrollView>
                              </View>
                            )}
                          </View>

                          {/* Apply to All Button */}
                          <TouchableOpacity
                            style={[styles.applyAllButton, !bulkRating && styles.applyAllButtonDisabled]}
                            onPress={applyBulkRating}
                            disabled={!bulkRating}
                          >
                            <Text style={[styles.applyAllButtonText, !bulkRating && styles.applyAllButtonTextDisabled]}>
                              Apply to All
                            </Text>
                          </TouchableOpacity>
                        </View>

                        <ScrollView
                          ref={scrollViewRef}
                          style={styles.questionsScrollView}
                          showsVerticalScrollIndicator={false}
                          contentContainerStyle={styles.scrollViewContent}
                          keyboardShouldPersistTaps="handled"
                        >
                          {evaluationQuestions.map((question, index) => {
                            console.log(`📝 Rendering question ${index + 1}: ${question.substring(0, 50)}...`);
                            return (
                            <View key={index} style={styles.questionContainer}>
                              <Text style={styles.questionText}>
                                {index + 1}. {question}
                              </Text>
                              <View style={styles.ratingsContainer}>
                                {/* Agree options - Top row */}
                                <View style={styles.ratingRow}>
                                  {ratingOptions.slice(0, 3).map((rating, ratingIndex) => (
                                    <TouchableOpacity
                                      key={ratingIndex}
                                      style={[
                                        styles.ratingButton,
                                        evaluationResponses[index] === rating && styles.ratingButtonSelected
                                      ]}
                                      onPress={() => handleRatingSelect(index, rating)}
                                    >
                                      <Text style={[
                                        styles.ratingButtonText,
                                        evaluationResponses[index] === rating && styles.ratingButtonTextSelected
                                      ]}>
                                        {rating}
                                      </Text>
                                    </TouchableOpacity>
                                  ))}
                                </View>

                                {/* Disagree options - Bottom row */}
                                <View style={styles.ratingRow}>
                                  {ratingOptions.slice(3, 6).map((rating, ratingIndex) => (
                                    <TouchableOpacity
                                      key={ratingIndex + 3}
                                      style={[
                                        styles.ratingButton,
                                        evaluationResponses[index] === rating && styles.ratingButtonSelected
                                      ]}
                                      onPress={() => handleRatingSelect(index, rating)}
                                    >
                                      <Text style={[
                                        styles.ratingButtonText,
                                        evaluationResponses[index] === rating && styles.ratingButtonTextSelected
                                      ]}>
                                        {rating}
                                      </Text>
                                    </TouchableOpacity>
                                  ))}
                                </View>
                              </View>
                            </View>
                          );
                          })}

                          {/* Additional Questions with Different Rating Scales */}
                          {additionalQuestions.map((questionObj, index) => (
                            <View key={`additional-${index}`} style={styles.questionContainer}>
                              <Text style={styles.questionText}>
                                {evaluationQuestions.length + index + 1}. {questionObj.question}
                              </Text>
                              <View style={styles.additionalRatingsContainer}>
                                {questionObj.options.map((option, optionIndex) => (
                                  <TouchableOpacity
                                    key={optionIndex}
                                    style={[
                                      styles.additionalRatingButton,
                                      additionalResponses[index] === option && styles.ratingButtonSelected
                                    ]}
                                    onPress={() => handleAdditionalRatingSelect(index, option)}
                                  >
                                    <Text style={[
                                      styles.additionalRatingButtonText,
                                      additionalResponses[index] === option && styles.ratingButtonTextSelected
                                    ]}>
                                      {option}
                                    </Text>
                                  </TouchableOpacity>
                                ))}
                              </View>
                            </View>
                          ))}

                          {/* Course Critique Text Area */}
                          <View style={styles.questionContainer}>
                            <Text style={styles.questionText}>
                              {evaluationQuestions.length + additionalQuestions.length + 1}. Course critique (e.g. likes, dislikes) and suggestions for improvement
                            </Text>
                            <TextInput
                              style={styles.critiqueTextInput}
                              multiline={true}
                              numberOfLines={6}
                              placeholder="Enter your feedback, suggestions, and comments here..."
                              placeholderTextColor="#9CA3AF"
                              value={critiqueText}
                              onChangeText={setCritiqueText}
                              textAlignVertical="top"
                            />
                          </View>

                          {/* Submit/Save Button */}
                          <View style={styles.submitButtonContainer}>
                            <TouchableOpacity
                              style={[
                                styles.submitButton,
                                (isSubmitting || isSaving) && styles.submitButtonDisabled
                              ]}
                              onPress={isAdvanceCourse ?
                                () => saveEvaluationToCache(currentCourseCode, selectedCourse.main) :
                                submitEvaluation
                              }
                              disabled={isSubmitting || isSaving}
                            >
                              {(isSubmitting || isSaving) ? (
                                <ActivityIndicator size="small" color="#2C2C2C" />
                              ) : (
                                <Text style={styles.submitButtonText}>
                                  {isAdvanceCourse ? 'Save Evaluation' : 'Submit Evaluation'}
                                </Text>
                              )}
                            </TouchableOpacity>
                          </View>

                          {/* Status Messages */}
                          {submissionStatus && (
                            <View
                              ref={submissionStatus.includes('successfully') ? null : errorMessageRef}
                              style={styles.submissionStatusContainer}
                            >
                              <Text style={[
                                styles.submissionStatusText,
                                submissionStatus.includes('successfully') ? styles.successText : styles.errorText
                              ]}>
                                {submissionStatus}
                              </Text>
                            </View>
                          )}

                          {saveStatus && (
                            <View
                              ref={saveStatus.includes('saved') ? null : errorMessageRef}
                              style={styles.submissionStatusContainer}
                            >
                              <Text style={[
                                styles.submissionStatusText,
                                saveStatus.includes('saved') ? styles.successText : styles.errorText
                              ]}>
                                {saveStatus}
                              </Text>
                            </View>
                          )}


                        </ScrollView>
                      </View>
                    ) : (
                      // Show status message for other cases (excluding saved status)
                      <View style={styles.statusMessageContainer}>
                        <Text style={[
                          styles.evaluationStatusText,
                          evaluationStatus.includes('already been evaluated') ? styles.alreadyEvaluatedText : styles.underDevelopmentText
                        ]}>
                          {evaluationStatus}
                        </Text>
                      </View>
                    )}
                  </>
                ) : null}
              </View>
            )}

            {/* TODO: Add evaluation form/content here after course selection */}
          </>
        )}



        {/* Hidden WebView for evaluation */}
        {evaluateUrl && (
          <View style={styles.hiddenWebView}>
            <WebView
              ref={webViewRef}
              source={{ uri: evaluateUrl }}
              onLoad={handleWebViewLoad}
              onMessage={handleWebViewMessage}
              onError={handleWebViewError}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              mixedContentMode="compatibility"
              cacheEnabled={false}
              incognito={true}
            />
          </View>
        )}

        {/* Hidden WebView for grades data extraction */}
        {gradesUrl && evaluationMode === 'course' && (
          <View style={styles.hiddenWebView}>
            <WebView
              ref={gradesWebViewRef}
              source={{ uri: gradesUrl }}
              onLoad={handleGradesWebViewLoad}
              onMessage={handleGradesWebViewMessage}
              onError={handleGradesWebViewError}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              mixedContentMode="compatibility"
              cacheEnabled={false}
              incognito={true}
            />
          </View>
        )}

        
      </TouchableOpacity>



      {/* Sidebar Component */}
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={closeSidebar}
        sidebarAnim={sidebarAnim}
        navigation={navigation}
        currentScreen="Evaluate"
      />
    </SafeAreaView>
    </View>
    </ThemeTransitionWrapper>
  );
};

// Create styles function that uses theme
const createStyles = (theme, safeCurrentThemeName) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  titleContainer: {
    position: 'absolute',
    top: 55, // Raised higher
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 10,
  },
  title: {
    fontSize: 30, // Increased font size slightly
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
  },
  sidebarButtonContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 10,
  },
  sidebarButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 3,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  refreshButtonContainer: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 10,
  },
  refreshButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 2,
    elevation: 3,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  staffToggleButtonContainer: {
    position: 'absolute',
    top: 50,
    right: 80, // Position to the left of refresh button (refresh is at right: 20, width: 50, so 20 + 50 + 10 = 80)
    zIndex: 10,
  },
  staffToggleButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  staffToggleButtonText: {
    fontSize: 24,
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
  mainContent: {
    flex: 1,
    paddingTop: 100, // Reduced from 120 to move card up
    paddingBottom: 5, // Minimal padding for full screen usage
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 16,
    color: '#9CA3AF',
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 15,
  },
  dropdownSection: {
    backgroundColor: theme.colors.surface,
    borderRadius: 15,
    padding: 15,
    marginBottom: 10,
    marginHorizontal: Math.max(12, Dimensions.get('window').width * 0.03),
    width: '95%',
    alignSelf: 'center',
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : theme.colors.border,
    zIndex: 9998,
    elevation: 9998,
    overflow: 'visible',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    marginBottom: 15,
    textAlign: 'center',
  },
  dropdownContainer: {
    marginBottom: 10,
    overflow: 'visible',
  },
  dropdownLabel: {
    fontSize: 16,
    color: theme.colors.text,
    marginBottom: 8,
    fontWeight: '600',
  },
  dropdownWrapper: {
    position: 'relative',
    zIndex: 9999,
    elevation: 9999,
  },
  dropdown: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 10,
    paddingVertical: 15,
    paddingHorizontal: 18,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    minHeight: 50,
  },
  dropdownContent: {
    flex: 1,
    justifyContent: 'center',
    minHeight: 20,
  },
  selectedCourseContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownText: {
    fontSize: 16,
    color: theme.colors.text,
    flex: 1,
    textAlignVertical: 'center',
  },
  dropdownPlaceholder: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    flex: 1,
    fontStyle: 'italic',
    textAlignVertical: 'center',
  },
  statusLoadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start', // Left-align with dropdown menu
    marginTop: 5,
  },
  statusLoadingContainerCentered: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center', // Center the loading content
    paddingVertical: 20,
  },
  dropdownDisabled: {
    opacity: 0.6,
    borderColor: theme.colors.textSecondary,
  },
  courseCode: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    marginLeft: 10,
  },
  dropdownArrow: {
    fontSize: 14,
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    marginLeft: 15,
    textAlignVertical: 'center',
  },
  dropdownList: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    backgroundColor: '#2A2A2A',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#EAB308',
    borderTopWidth: 0,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    maxHeight: 200,
    zIndex: 101,
    elevation: 101,
  },
  dropdownListFloating: {
    position: 'absolute',
    marginTop: 55,
    left: 0,
    right: 0,
    backgroundColor: theme.colors.surface,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    borderTopWidth: 0,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    maxHeight: 200,
    zIndex: 99999,
    elevation: 99999,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.44,
    shadowRadius: 10.32,
  },
  staffDropdownListFloating: {
    position: 'absolute',
    marginTop:55,
    left: 0,
    right: 0,
    backgroundColor: '#2A2A2A',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#EAB308',
    borderTopWidth: 0,
    borderTopLeftRadius: 0,
    borderTopRightRadius: 0,
    maxHeight: 200,
    zIndex: 99999,
    elevation: 99999,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.44,
    shadowRadius: 10.32,
  },
  optionsCount: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 1,
    marginLeft: 8,
    fontStyle: 'italic',
  },
  advanceCoursesCount: {
    fontSize: 12,
    color: '#EAB308',
    marginTop: 0,
    marginLeft: 8,
    fontStyle: 'italic',
  },
  optionsList: {
    flex: 1,
    overflow: 'hidden', // Clip content to container bounds
    borderBottomLeftRadius: 10, // Add bottom border radius to container
    borderBottomRightRadius: 10, // Add bottom border radius to container
  },
  optionItem: {
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    backgroundColor: theme.colors.surface, // Solid background
  },
  optionItemLast: {
    padding: 15,
    borderBottomWidth: 0, // Remove bottom border for last item
    backgroundColor: theme.colors.surface, // Solid background
    borderBottomLeftRadius: 10, // Add bottom left radius
    borderBottomRightRadius: 10, // Add bottom right radius
  },
  optionContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  optionText: {
    fontSize: 16,
    color: theme.colors.text,
    fontWeight: '600',
    flex: 1,
  },
  optionCode: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    marginLeft: 10,
  },
  advanceOptionCode: {
    fontSize: 14,
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    fontStyle: 'italic',
    marginLeft: 10,
    fontWeight: '600',
  },
  dropdownOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',
    zIndex: 50,
    elevation: 50,
  },
  hiddenWebView: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
    opacity: 0,
  },
  toggleButtonContainer: {
    alignItems: 'flex-end',
    marginTop: 2,
    marginBottom: 0,
  },
  toggleButton: {
    backgroundColor: '#EAB308',
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  toggleButtonText: {
    color: '#2C2C2C', // Updated to new dark grey
    fontSize: 14,
    fontWeight: 'bold',
  },
  evaluationStatusContainer: {
    flex: 1,
    marginTop: 15,
    marginBottom: -30,
    marginHorizontal: Math.max(12, Dimensions.get('window').width * 0.03),
    width: '95%',
    alignSelf: 'center',
    padding: 15,
    paddingBottom: 50,
    backgroundColor: theme.colors.surface,
    borderRadius: 10,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border,
    zIndex: -1,
    elevation: -1,
  },
  evaluationStatusContainerSmall: {
    flex: 0, // Don't take full height
    marginBottom: 15, // Normal margin instead of negative
    paddingBottom: 15, // Normal padding
    minHeight: 80, // Small fixed height for already evaluated messages
  },
  evaluationStatusContainerLoading: {
    flex: 0, // Don't take full height when loading
    marginBottom: 15, // Normal margin instead of negative
    paddingVertical: 0, // Remove extra padding
    minHeight: 60, // Minimal height just for the loading message
    justifyContent: 'center', // Center content vertically
  },
  evaluationStatusText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginLeft: 8,
    color: theme.colors.text,
  },
  alreadyEvaluatedText: {
    color: safeCurrentThemeName === 'colorful' ? '#00CED1' : '#EF4444', // Turquoise in pink mode, red in others
  },
  underDevelopmentText: {
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
  },
  questionnaireContainer: {
    width: '100%', // Full width
    flex: 1,
    marginTop: 0,
    marginBottom: 0, // Reset margin since parent container handles the extension
    backgroundColor: 'transparent', // Changed to transparent to remove mini black component
    paddingHorizontal: 0, // Removed horizontal padding
    paddingTop: 5, // Reduced top padding to raise title up
    paddingBottom: 20, // Normal padding since parent handles extension
  },
  questionnaireTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    textAlign: 'center',
    marginBottom: 10, // Reduced bottom margin to bring title closer to content
    marginHorizontal: 8, // Further reduced horizontal margins to match questions
    letterSpacing: 0.5,
  },
  questionsScrollView: {
    flex: 1,
    marginTop: 5,
    minHeight: 250, // Reduced height to make component shorter
  },
  scrollViewContent: {
    paddingBottom: 20, // Increased bottom padding for better scrolling experience
  },
  questionContainer: {
    marginBottom: 12,
    marginHorizontal: 8, // Further reduced horizontal margins to make questions wider
    padding: 15, // Increased padding for better spacing
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 1.5,
    elevation: 2,
  },
  questionText: {
    fontSize: 14,
    color: theme.colors.text,
    marginBottom: 8,
    lineHeight: 18,
  },
  ratingsContainer: {
    width: '100%',
  },
  ratingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  ratingButton: {
    backgroundColor: theme.colors.surface,
    paddingVertical: 8,
    paddingHorizontal: 6,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#00CED1' : theme.colors.border,
    flex: 1,
    marginHorizontal: 1,
    alignItems: 'center',
  },
  ratingButtonSelected: {
    backgroundColor: safeCurrentThemeName === 'colorful' ? '#00CED1' : theme.colors.primary,
    borderColor: safeCurrentThemeName === 'colorful' ? '#00CED1' : theme.colors.primary,
  },
  ratingButtonText: {
    fontSize: 10,
    color: theme.colors.text,
    textAlign: 'center',
    fontWeight: '500',
  },
  ratingButtonTextSelected: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.primaryText,
    fontWeight: 'bold',
  },
  additionalRatingsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  additionalRatingButton: {
    backgroundColor: theme.colors.surface,
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    borderWidth: safeCurrentThemeName === 'colorful' ? 2 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#00CED1' : theme.colors.border,
    marginBottom: 8,
    marginHorizontal: 3,
    minWidth: '14%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  additionalRatingButtonText: {
    fontSize: 16,
    color: theme.colors.text,
    textAlign: 'center',
    fontWeight: 'bold',
  },
  critiqueTextInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: 10,
    borderWidth: safeCurrentThemeName === 'colorful' ? 2 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border,
    padding: 15,
    marginHorizontal: Math.max(12, Dimensions.get('window').width * 0.03), // Add horizontal margins
    color: theme.colors.text,
    fontSize: 16,
    minHeight: 120,
    maxHeight: 200,
  },
  submitButtonContainer: {
    alignItems: 'center',
    marginTop: 20,
    marginBottom: 20,
    marginHorizontal: Math.max(12, Dimensions.get('window').width * 0.03), // Add horizontal margins
  },
  submitButton: {
    backgroundColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    borderRadius: 10,
    paddingVertical: 15,
    paddingHorizontal: 40,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 200,
  },
  submitButtonDisabled: {
    backgroundColor: theme.colors.surfaceSecondary,
  },
  submitButtonText: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.primaryText,
    fontSize: 16,
    fontWeight: 'bold',
  },
  submissionStatusContainer: {
    alignItems: 'center',
    marginTop: 10,
    marginBottom: 10,
  },
  submissionStatusText: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  successText: {
    color: '#22C55E', // Green color for success
  },
  errorText: {
    color: '#EF4444', // Red color for error
  },
  hiddenWebView: {
    height: 0,
    overflow: 'hidden',
  },
  bulkRatingRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10,
    marginBottom: 15,
    marginHorizontal: Math.max(12, Dimensions.get('window').width * 0.03), // Add horizontal margins
    backgroundColor: 'transparent',
    paddingHorizontal: 0,
    paddingVertical: 0,
    height: 50,
  },
  bulkDropdownContainer: {
    flex: 1,
    position: 'relative',
    zIndex: 9997,
    elevation: 9997,
  },
  bulkDropdownButton: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 8,
    borderWidth: safeCurrentThemeName === 'colorful' ? 2 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border,
    paddingVertical: 12,
    paddingHorizontal: 15,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    minHeight: 45,
  },
  bulkDropdownButtonText: {
    color: theme.colors.text,
    fontSize: 14,
    flex: 1,
  },
  bulkDropdownArrow: {
    color: theme.colors.primary,
    fontSize: 12,
    marginLeft: 8,
  },
  applyAllButton: {
    backgroundColor: safeCurrentThemeName === 'colorful' ? '#00CED1' : theme.colors.primary,
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 18,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 100,
    minHeight: 45,
    borderWidth: safeCurrentThemeName === 'colorful' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : 'transparent',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 3,
  },
  applyAllButtonDisabled: {
    backgroundColor: theme.colors.surfaceSecondary,
  },
  applyAllButtonText: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.primaryText,
    fontSize: 14,
    fontWeight: 'bold',
  },
  applyAllButtonTextDisabled: {
    color: theme.colors.textSecondary,
  },
  bulkDropdownOptions: {
    position: 'absolute',
    top: '100%',
    left: 0,
    width: '100%',
    backgroundColor: theme.colors.surface,
    borderRadius: 6,
    borderWidth: safeCurrentThemeName === 'colorful' ? 2 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border,
    maxHeight: 150,
    zIndex: 99999,
    elevation: 99999,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.44,
    shadowRadius: 10.32,
  },
  bulkDropdownScrollView: {
    maxHeight: 150,
  },
  bulkDropdownOption: {
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    width: '100%',
  },
  bulkDropdownOptionText: {
    color: theme.colors.text,
    fontSize: 14,
    flexShrink: 1,
    numberOfLines: 1,
  },
  statusMessageContainer: {
    alignItems: 'center',
  },
  savedStatusContainer: {
    backgroundColor: '#2A2A2A',
    borderRadius: 10,
    padding: 20,
    marginTop: 20,
    marginHorizontal: Math.max(12, Dimensions.get('window').width * 0.03), // Match course selection margins
    width: '95%', // Match course selection width
    alignSelf: 'center', // Center the component
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#EAB308',
  },
  savedStatusText: {
    color: '#EAB308',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    marginBottom: 15,
  },
  submissionSuccessSubtext: {
    color: '#9CA3AF',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 5,
  },
  editAnswersButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    paddingVertical: 8,
    paddingHorizontal: 15,
    marginTop: 10,
  },
  editAnswersButtonText: {
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },

});

export default EvaluateScreen;
